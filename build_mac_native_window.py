#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
macOS 原生标题栏示例应用的 Nuitka 打包脚本
专门为 examples/mac_native_fluent_window.py 设计
"""

import os
import sys
import subprocess
import shutil
import platform
from pathlib import Path


class MacNativeWindowBuilder:
    """macOS 原生窗口应用构建器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.source_file = self.project_root / "examples" / "mac_native_fluent_window.py"
        self.output_dir = self.project_root / "dist_mac_native"
        self.app_name = "MacNativeFluentWindow"
        
        # 检查源文件是否存在
        if not self.source_file.exists():
            raise FileNotFoundError(f"源文件不存在: {self.source_file}")
    
    def clean_output_dir(self):
        """清理输出目录"""
        if self.output_dir.exists():
            print(f"🧹 清理输出目录: {self.output_dir}")
            shutil.rmtree(self.output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def check_dependencies(self):
        """检查构建依赖"""
        print("🔍 检查构建依赖...")
        
        # 检查 Nuitka
        try:
            result = subprocess.run([sys.executable, "-m", "nuitka", "--version"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Nuitka 版本: {result.stdout.strip()}")
            else:
                raise Exception("Nuitka 未正确安装")
        except Exception as e:
            print(f"❌ Nuitka 检查失败: {e}")
            return False
        
        # 检查 PySide6
        try:
            import PySide6
            print(f"✅ PySide6 版本: {PySide6.__version__}")
        except ImportError:
            print("❌ PySide6 未安装")
            return False
        
        # 检查 vendor/qfluentwidgets
        vendor_path = self.project_root / "vendor" / "qfluentwidgets"
        if vendor_path.exists():
            print(f"✅ qfluentwidgets 路径: {vendor_path}")
        else:
            print(f"❌ qfluentwidgets 未找到: {vendor_path}")
            return False
        
        return True
    
    def get_nuitka_command(self):
        """构建 Nuitka 命令"""
        icon_path = self.project_root / "components" / "assets" / "lapped.icns"
        
        command = [
            sys.executable, "-m", "nuitka",
            
            # 基本设置
            "--standalone",
            "--onefile-tempdir-spec={CACHE_DIR}/{COMPANY}/{PRODUCT}/{VERSION}",
            
            # macOS 特定设置
            "--macos-create-app-bundle",
            f"--macos-app-name={self.app_name}",
            "--macos-app-version=1.0.0",
            
            # 图标设置
            f"--macos-app-icon={icon_path}" if icon_path.exists() else "",
            
            # PySide6 插件
            "--enable-plugin=pyside6",
            
            # 包含路径
            f"--include-package-data=vendor.qfluentwidgets",
            "--include-package=vendor.qfluentwidgets",
            
            # 资源文件
            f"--include-data-dir={self.project_root}/vendor={self.app_name}.app/Contents/Resources/vendor",
            f"--include-data-dir={self.project_root}/components/assets={self.app_name}.app/Contents/Resources/components/assets",
            
            # 优化设置
            "--python-flag=no_warnings,-O",
            "--remove-output",
            "--no-pyi-file",
            
            # 输出设置
            f"--output-dir={self.output_dir}",
            
            # 源文件
            str(self.source_file)
        ]
        
        # 过滤空字符串
        return [cmd for cmd in command if cmd]
    
    def build(self):
        """执行构建"""
        print(f"🚀 开始构建 {self.app_name}...")
        print(f"📁 源文件: {self.source_file}")
        print(f"📁 输出目录: {self.output_dir}")
        
        # 检查平台
        if platform.system() != "Darwin":
            print("⚠️  警告: 当前不在 macOS 平台，构建的应用可能无法正常运行")
        
        # 构建命令
        command = self.get_nuitka_command()
        
        print("\n🔧 Nuitka 命令:")
        for i, cmd in enumerate(command):
            if i == 0:
                print(f"  {cmd} \\")
            elif i == len(command) - 1:
                print(f"    {cmd}")
            else:
                print(f"    {cmd} \\")
        
        print("\n⏳ 开始编译...")
        
        try:
            # 执行构建
            result = subprocess.run(command, cwd=self.project_root, check=True)
            
            if result.returncode == 0:
                print("\n✅ 构建成功!")
                self.post_build_info()
            else:
                print(f"\n❌ 构建失败，返回码: {result.returncode}")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"\n❌ 构建过程中出现错误: {e}")
            return False
        except KeyboardInterrupt:
            print("\n⚠️ 构建被用户中断")
            return False
        
        return True
    
    def post_build_info(self):
        """构建后信息显示"""
        app_path = self.output_dir / f"{self.app_name}.app"
        
        if app_path.exists():
            print(f"\n📦 应用程序位置: {app_path}")
            
            # 计算应用大小
            try:
                size = self.get_directory_size(app_path)
                print(f"📏 应用大小: {self.format_size(size)}")
            except Exception as e:
                print(f"⚠️ 无法计算应用大小: {e}")
            
            print(f"\n🎯 运行应用:")
            print(f"  open '{app_path}'")
            print(f"\n📋 或者双击应用图标运行")
            
        else:
            print(f"\n❌ 应用程序未找到: {app_path}")
    
    def get_directory_size(self, path):
        """计算目录大小"""
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total_size += os.path.getsize(filepath)
        return total_size
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"


def main():
    """主函数"""
    print("🍎 macOS 原生标题栏示例应用 - Nuitka 构建器")
    print("=" * 50)
    
    try:
        builder = MacNativeWindowBuilder()
        
        # 检查依赖
        if not builder.check_dependencies():
            print("\n❌ 依赖检查失败，请安装必要的依赖后重试")
            sys.exit(1)
        
        # 清理输出目录
        builder.clean_output_dir()
        
        # 执行构建
        success = builder.build()
        
        if success:
            print("\n🎉 构建完成!")
            sys.exit(0)
        else:
            print("\n💥 构建失败!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ 构建被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 构建过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
