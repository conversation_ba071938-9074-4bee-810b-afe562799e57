#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LinLin 项目完整打包脚本
以 run.py 为入口，使用 Nuitka 打包整个项目
"""

import os
import sys
import subprocess
import shutil
import platform
from pathlib import Path


class LinLinProjectBuilder:
    """LinLin 项目构建器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.source_file = self.project_root / "run.py"
        self.output_dir = self.project_root / "dist_linlin"
        self.app_name = "LinLin"
        
        # 检查并使用虚拟环境的 Python
        venv_python = self.project_root / ".venv" / "bin" / "python"
        if venv_python.exists():
            self.python_executable = str(venv_python)
            print(f"🐍 使用虚拟环境 Python: {self.python_executable}")
        else:
            self.python_executable = sys.executable
            print(f"🐍 使用系统 Python: {self.python_executable}")
        
        # 检查源文件是否存在
        if not self.source_file.exists():
            raise FileNotFoundError(f"源文件不存在: {self.source_file}")
    
    def clean_output_dir(self):
        """清理输出目录"""
        if self.output_dir.exists():
            print(f"🧹 清理输出目录: {self.output_dir}")
            shutil.rmtree(self.output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def check_dependencies(self):
        """检查构建依赖"""
        print("🔍 检查构建依赖...")
        
        # 检查 Nuitka
        try:
            result = subprocess.run([self.python_executable, "-m", "nuitka", "--version"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Nuitka 版本: {result.stdout.strip()}")
            else:
                raise Exception("Nuitka 未正确安装")
        except Exception as e:
            print(f"❌ Nuitka 检查失败: {e}")
            return False
        
        # 检查 PySide6
        try:
            result = subprocess.run([self.python_executable, "-c", "import PySide6; print(PySide6.__version__)"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ PySide6 版本: {result.stdout.strip()}")
            else:
                print("❌ PySide6 未安装")
                return False
        except Exception as e:
            print(f"❌ PySide6 检查失败: {e}")
            return False
        
        # 检查关键项目模块
        project_modules = [
            ("nice_ui", self.project_root / "nice_ui"),
            ("vendor/qfluentwidgets", self.project_root / "vendor" / "qfluentwidgets"),
            ("orm", self.project_root / "orm"),
            ("utils", self.project_root / "utils"),
            ("agent", self.project_root / "agent"),
            ("components", self.project_root / "components"),
            ("services", self.project_root / "services"),
            ("api_client.py", self.project_root / "api_client.py"),
        ]
        
        for name, path in project_modules:
            if path.exists():
                print(f"✅ {name}: {path}")
            else:
                print(f"❌ {name} 未找到: {path}")
                return False
        
        return True
    
    def get_nuitka_command(self, third_party_strategy="smart"):
        """构建 Nuitka 命令
        
        Args:
            third_party_strategy: 第三方包处理策略
                - "smart": 智能处理（推荐）
                - "minimal": 最小化包含
                - "full": 完整包含
        """
        icon_path = self.project_root / "components" / "assets" / "lapped.icns"
        
        command = [
            self.python_executable,
            "-m",
            "nuitka",
            "--standalone",
        ]
        
        # 平台特定设置
        if platform.system() == "Darwin":
            command.extend([
                "--macos-create-app-bundle",
                f"--macos-app-name={self.app_name}",
                "--macos-app-version=0.2.1",
                f"--macos-app-icon={icon_path}" if icon_path.exists() else "",
                # 禁用代码签名以避免参数列表过长的问题
                "--macos-sign-identity=-",
            ])
        elif platform.system() == "Windows":
            command.extend([
                "--windows-console-mode=disable",  # 隐藏控制台窗口
                f"--windows-icon-from-ico={self.project_root}/components/assets/lapped.ico" if (self.project_root / "components" / "assets" / "lapped.ico").exists() else "",
            ])
        
        # 插件和基本设置
        command.extend([
            "--enable-plugin=pyside6",
            "--enable-plugin=multiprocessing",
            
            # 包含项目核心模块
            "--include-package=nice_ui",
            "--include-package=vendor.qfluentwidgets",
            "--include-package=orm",
            "--include-package=utils", 
            "--include-package=agent",
            "--include-package=components",
            "--include-package=services",
            
            # 包含单独的模块文件
            "--include-module=api_client",
        ])
        
        # 包含数据目录
        data_dirs = [
            f"--include-data-dir={self.project_root}/vendor/qfluentwidgets=vendor/qfluentwidgets",
            f"--include-data-dir={self.project_root}/components/assets=components/assets",
            f"--include-data-dir={self.project_root}/orm=orm",
            f"--include-data-dir={self.project_root}/config=config",
        ]
        
        # 检查可选数据目录
        optional_dirs = [
            ("nice_ui/language", "nice_ui/language"),
            ("logs", "logs"),
            ("result", "result"),
        ]
        
        for src, dst in optional_dirs:
            src_path = self.project_root / src
            if src_path.exists():
                data_dirs.append(f"--include-data-dir={src_path}={dst}")
        
        command.extend(data_dirs)
        
        # 第三方包处理策略
        if third_party_strategy == "smart":
            # 智能策略：包含数据但不编译大型包
            command.extend([
                # 云服务相关包 - 包含数据但不编译
                "--include-package-data=alibabacloud_oss_v2",
                "--nofollow-import-to=alibabacloud_oss_v2",
                "--include-package-data=dashscope",
                "--nofollow-import-to=dashscope",

                # 核心依赖包 - 完整包含
                "--include-package=httpx",
                "--include-package=loguru",
                "--include-package=openai",
                "--include-package=sqlalchemy",
                "--include-package=pydantic",
                "--include-package=pydantic_settings",
                "--include-package=packaging",
                "--include-package=socksio",
                "--include-package=darkdetect",
                "--include-package=colorthief",
                "--include-package=pytz",

                # AI/ML 相关包 - 包含数据但不编译
                "--include-package-data=modelscope",
                "--nofollow-import-to=modelscope",
                "--include-package-data=funasr",
                "--nofollow-import-to=funasr",
                "--include-package-data=av",
                "--nofollow-import-to=av",
            ])
        elif third_party_strategy == "minimal":
            # 最小化策略：只包含核心依赖
            command.extend([
                "--include-package=httpx",
                "--include-package=loguru",
                "--include-package=sqlalchemy",
                "--include-package=pydantic",
                "--include-package=packaging",
            ])
        elif third_party_strategy == "full":
            # 完整策略：包含所有第三方包
            command.extend([
                "--include-package=alibabacloud_oss_v2",
                "--include-package=dashscope",
                "--include-package=httpx",
                "--include-package=loguru",
                "--include-package=openai",
                "--include-package=sqlalchemy",
                "--include-package=pydantic",
                "--include-package=pydantic_settings",
                "--include-package=packaging",
                "--include-package=socksio",
                "--include-package=darkdetect",
                "--include-package=colorthief",
                "--include-package=pytz",
                "--include-package=modelscope",
                "--include-package=funasr",
                "--include-package=av",
            ])

        # 只排除真正不需要的大型包
        exclude_packages = [
            "scipy", "numpy", "torch", "torchaudio", "matplotlib", "pandas",
            "jupyter", "IPython", "notebook", "pytest", "setuptools", "wheel", "pip"
        ]

        for pkg in exclude_packages:
            command.append(f"--nofollow-import-to={pkg}")
        
        # 优化设置
        command.extend([
            "--python-flag=no_warnings,-O",
            "--remove-output",
            "--no-pyi-file",
            "--assume-yes-for-downloads",
            
            # 输出设置
            f"--output-dir={self.output_dir}",
            
            # 源文件
            str(self.source_file),
        ])
        
        # 过滤空字符串
        return [cmd for cmd in command if cmd]
    
    def build(self, third_party_strategy="smart"):
        """执行构建"""
        print(f"🚀 开始构建 LinLin 项目...")
        print(f"📁 入口文件: {self.source_file}")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"📦 第三方包策略: {third_party_strategy}")
        
        # 检查平台
        platform_info = {
            "Darwin": ("🍎 macOS", ".app 应用包"),
            "Windows": ("🪟 Windows", ".exe 可执行文件"),
            "Linux": ("🐧 Linux", "可执行文件")
        }
        
        system = platform.system()
        if system in platform_info:
            icon, file_type = platform_info[system]
            print(f"{icon} 检测到 {system} 平台，将生成 {file_type}")
        
        # 构建命令
        command = self.get_nuitka_command(third_party_strategy=third_party_strategy)
        
        print("\n🔧 Nuitka 命令:")
        for i, cmd in enumerate(command):
            if i == 0:
                print(f"  {cmd} \\")
            elif i == len(command) - 1:
                print(f"    {cmd}")
            else:
                print(f"    {cmd} \\")
        
        print("\n⏳ 开始编译...")
        print("📝 注意: 首次编译可能需要较长时间，请耐心等待...")
        
        try:
            # 执行构建
            result = subprocess.run(command, cwd=self.project_root, check=True)
            
            if result.returncode == 0:
                print("\n✅ 构建成功!")
                self.post_build_info()
                return True
            else:
                print(f"\n❌ 构建失败，返回码: {result.returncode}")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"\n❌ 构建过程中出现错误: {e}")
            return False
        except KeyboardInterrupt:
            print("\n⚠️ 构建被用户中断")
            return False
    
    def post_build_info(self):
        """构建后信息显示"""
        # 查找生成的应用文件
        if platform.system() == "Darwin":
            app_files = list(self.output_dir.glob("*.app"))
            if app_files:
                app_path = app_files[0]
                print(f"\n📦 应用程序位置: {app_path}")
                print(f"\n🎯 运行应用:")
                print(f"  open '{app_path}'")
                print(f"\n📋 或者双击应用图标运行")
            else:
                self._show_fallback_info()
        else:
            # Windows/Linux
            exe_files = list(self.output_dir.glob("*.exe")) + list(self.output_dir.glob("run"))
            if exe_files:
                exe_path = exe_files[0]
                print(f"\n📦 可执行文件位置: {exe_path}")
                print(f"\n🎯 运行应用:")
                print(f"  {exe_path}")
            else:
                self._show_fallback_info()
        
        # 计算应用大小
        try:
            total_size = self.get_directory_size(self.output_dir)
            print(f"📏 总大小: {self.format_size(total_size)}")
        except Exception as e:
            print(f"⚠️ 无法计算应用大小: {e}")
        
        print(f"\n💡 提示:")
        print(f"  - 如果应用运行时缺少某些功能，可以尝试 --strategy full")
        print(f"  - 如果应用体积过大，可以尝试 --strategy minimal")
        print(f"  - 首次运行可能需要一些时间来初始化")
    
    def _show_fallback_info(self):
        """显示备用信息"""
        print(f"\n📁 输出目录: {self.output_dir}")
        print("📋 输出目录内容:")
        for item in self.output_dir.iterdir():
            print(f"  - {item.name}")
    
    def get_directory_size(self, path):
        """计算目录大小"""
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total_size += os.path.getsize(filepath)
        return total_size
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="LinLin 项目 - Nuitka 构建器")
    parser.add_argument(
        "--strategy", 
        choices=["smart", "minimal", "full"],
        default="smart",
        help="第三方包处理策略 (默认: smart)"
    )
    
    args = parser.parse_args()
    
    print("🎬 LinLin 项目 - Nuitka 构建器")
    print("=" * 50)
    
    # 显示策略说明
    strategy_descriptions = {
        "smart": "智能处理第三方包（推荐，平衡体积和功能）",
        "minimal": "最小化包含（体积最小，可能缺少某些功能）", 
        "full": "完整包含第三方包（功能完整，体积较大）"
    }
    
    print(f"📦 构建策略: {args.strategy}")
    print(f"📝 策略说明: {strategy_descriptions[args.strategy]}")
    print()
    
    try:
        builder = LinLinProjectBuilder()
        
        # 检查依赖
        if not builder.check_dependencies():
            print("\n❌ 依赖检查失败，请安装必要的依赖后重试")
            sys.exit(1)
        
        # 清理输出目录
        builder.clean_output_dir()
        
        # 执行构建
        success = builder.build(third_party_strategy=args.strategy)
        
        if success:
            print("\n🎉 构建完成!")
            print("\n📚 更多信息:")
            print("  - 查看 BUILD_PROJECT_README.md 了解详细说明")
            print("  - 如有问题，请检查构建日志")
            sys.exit(0)
        else:
            print("\n💥 构建失败!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ 构建被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 构建过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
