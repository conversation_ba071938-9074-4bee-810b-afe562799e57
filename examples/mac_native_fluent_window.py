#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import platform
from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton, QTextEdit
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon, QFont

# 导入FluentWidgets组件
from vendor.qfluentwidgets import MacFluentWindow, FluentWindow, FluentIcon as FIF
from vendor.qfluentwidgets import NavigationItemPosition, setThemeColor, PrimaryPushButton
from vendor.qfluentwidgets import InfoBar, InfoBarPosition


class HomePage(QWidget):
    """首页界面"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("home")
        self.initUI()
        
    def initUI(self):
        layout = QVBoxLayout(self)
        
        # 显示平台信息
        platform_info = QLabel(f"运行平台: {platform.system()} {platform.release()}")
        platform_info.setStyleSheet("font-size: 14px; color: gray; margin: 10px;")
        
        # 标题
        title = QLabel("macOS 原生标题栏示例")
        title.setStyleSheet("font-size: 24px; font-weight: bold; margin: 20px;")
        title.setAlignment(Qt.AlignCenter)
        
        # 描述文本
        if sys.platform == "darwin":
            description = QLabel("✅ 您正在macOS上运行，使用的是原生标题栏样式")
            description.setStyleSheet("font-size: 14px; color: green; margin: 10px;")
        else:
            description = QLabel("ℹ️ 您不在macOS上运行，仍使用标准FluentWindow标题栏")
            description.setStyleSheet("font-size: 14px; color: blue; margin: 10px;")
        
        description.setAlignment(Qt.AlignCenter)
        
        # 按钮
        button = PrimaryPushButton("测试原生标题栏特性")
        button.clicked.connect(self.on_button_clicked)
        
        layout.addStretch()
        layout.addWidget(platform_info)
        layout.addWidget(title)
        layout.addWidget(description)
        layout.addWidget(button, 0, Qt.AlignCenter)
        layout.addStretch()
        
    def on_button_clicked(self):
        """按钮点击事件"""
        if sys.platform == "darwin":
            InfoBar.success(
                title='macOS 原生特性',
                content='原生标题栏在macOS上工作良好！',
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=2000,
                parent=self.window()
            )
        else:
            InfoBar.info(
                title='跨平台兼容',
                content='在非macOS平台上使用标准标题栏',
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=2000,
                parent=self.window()
            )


class FeaturePage(QWidget):
    """特性介绍页面"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("features")
        self.initUI()
        
    def initUI(self):
        layout = QVBoxLayout(self)
        
        title = QLabel("MacFluentWindow 特性")
        title.setStyleSheet("font-size: 20px; font-weight: bold; margin: 20px;")
        
        features_text = """
📱 跨平台兼容性:
• macOS: 使用原生标题栏，完美融入系统风格
• Windows/Linux: 自动降级到标准FluentWindow

🎨 原生macOS体验:
• 系统原生标题栏按钮（红绿黄三个点）
• 符合Apple Human Interface Guidelines
• 自动适配系统主题（深色/浅色模式）

⚙️ 自动平台检测:
• 无需手动配置，自动识别运行平台
• 保持代码的跨平台一致性
• 提供统一的API接口

🔧 高级定制选项:
• setMacUnifiedTitleAndToolBar() 方法
• 原生macOS窗口样式设置
• 可选的金属质感外观
        """
        
        features_label = QLabel(features_text)
        features_label.setStyleSheet("margin: 10px; line-height: 1.5;")
        features_label.setWordWrap(True)
        
        layout.addWidget(title)
        layout.addWidget(features_label)
        layout.addStretch()


class ComparisonPage(QWidget):
    """对比页面"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("comparison")
        self.initUI()
        
    def initUI(self):
        layout = QVBoxLayout(self)
        
        title = QLabel("标题栏对比")
        title.setStyleSheet("font-size: 20px; font-weight: bold; margin: 20px;")
        
        comparison_text = """
🏷️ FluentWindow (标准版本):
• 自定义标题栏样式
• 跨平台一致的外观
• Windows风格的窗口控制按钮
• 固定48px标题栏高度

🍎 MacFluentWindow (macOS优化版本):
• macOS上使用原生标题栏
• 系统级窗口控制按钮
• 自适应标题栏高度（通常28px）
• 更好的macOS集成体验

💡 使用建议:
• 如果目标用户主要使用macOS → 选择MacFluentWindow
• 如果需要跨平台一致外观 → 选择FluentWindow
• MacFluentWindow在非macOS平台会自动降级为标准版本
        """
        
        comparison_label = QLabel(comparison_text)
        comparison_label.setStyleSheet("margin: 10px; line-height: 1.5;")
        comparison_label.setWordWrap(True)
        
        layout.addWidget(title)
        layout.addWidget(comparison_label)
        layout.addStretch()


class MacNativeFluentWindow(MacFluentWindow):
    """macOS原生标题栏的FluentWindow示例"""
    
    def __init__(self):
        super().__init__()
        self.initWindow()
        self.initNavigation()
        
    def initWindow(self):
        """初始化窗口"""
        # 设置窗口标题和图标
        self.setWindowTitle("macOS 原生标题栏示例")
        self.setWindowIcon(QIcon(":icon/assets/lapped.png"))
        
        # 设置窗口大小
        self.resize(900, 650)
        
        # 设置主题颜色
        setThemeColor("#007AFF")  # iOS/macOS蓝色
        
        # 在macOS上启用统一标题栏外观
        if sys.platform == "darwin":
            try:
                self.setMacUnifiedTitleAndToolBar(True)
            except Exception as e:
                print(f"设置macOS样式时出现问题: {e}")
        
        # 居中显示窗口
        self.centerWindow()
        
    def centerWindow(self):
        """将窗口居中显示"""
        screen = QApplication.primaryScreen().geometry()
        size = self.geometry()
        x = (screen.width() - size.width()) // 2
        y = (screen.height() - size.height()) // 2
        self.move(x, y)
        
    def initNavigation(self):
        """初始化导航"""
        # 创建页面实例
        self.homePage = HomePage(self)
        self.featuresPage = FeaturePage(self)
        self.comparisonPage = ComparisonPage(self)
        
        # 添加导航项
        self.addSubInterface(
            interface=self.homePage,
            icon=FIF.HOME,
            text="首页"
        )
        
        self.addSubInterface(
            interface=self.featuresPage,
            icon=FIF.INFO,
            text="特性说明"
        )
        
        self.addSubInterface(
            interface=self.comparisonPage,
            icon=FIF.VIEW,
            text="对比说明"
        )
        
        # 默认显示首页
        self.switchTo(self.homePage)


def main():
    """主函数"""
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("macOS原生标题栏示例")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("FluentWidgets示例")
    
    # 在macOS上设置更好的应用程序图标
    if sys.platform == "darwin":
        app.setWindowIcon(QIcon(":icon/assets/lapped.png"))
    
    # 创建并显示窗口
    window = MacNativeFluentWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main() 