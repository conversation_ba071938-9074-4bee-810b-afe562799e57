#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置界面应用的 Nuitka 打包脚本
专门为 nice_ui/ui/setting_ui.py 设计
"""

import os
import sys
import subprocess
import shutil
import platform
from pathlib import Path


class SettingUIBuilder:
    """设置界面应用构建器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.source_file = self.project_root / "nice_ui" / "ui" / "setting_ui.py"
        self.output_dir = self.project_root / "dist_setting_ui"
        self.app_name = "SettingUI"
        
        # 检查并使用虚拟环境的 Python
        venv_python = self.project_root / ".venv" / "bin" / "python"
        if venv_python.exists():
            self.python_executable = str(venv_python)
            print(f"🐍 使用虚拟环境 Python: {self.python_executable}")
        else:
            self.python_executable = sys.executable
            print(f"🐍 使用系统 Python: {self.python_executable}")
        
        # 检查源文件是否存在
        if not self.source_file.exists():
            raise FileNotFoundError(f"源文件不存在: {self.source_file}")
    
    def clean_output_dir(self):
        """清理输出目录"""
        if self.output_dir.exists():
            print(f"🧹 清理输出目录: {self.output_dir}")
            shutil.rmtree(self.output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def check_dependencies(self):
        """检查构建依赖"""
        print("🔍 检查构建依赖...")
        
        # 检查 Nuitka
        try:
            result = subprocess.run([self.python_executable, "-m", "nuitka", "--version"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Nuitka 版本: {result.stdout.strip()}")
            else:
                raise Exception("Nuitka 未正确安装")
        except Exception as e:
            print(f"❌ Nuitka 检查失败: {e}")
            return False
        
        # 检查 PySide6
        try:
            result = subprocess.run([self.python_executable, "-c", "import PySide6; print(PySide6.__version__)"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ PySide6 版本: {result.stdout.strip()}")
            else:
                print("❌ PySide6 未安装")
                return False
        except Exception as e:
            print(f"❌ PySide6 检查失败: {e}")
            return False
        
        # 检查关键依赖目录
        dependencies = [
            ("nice_ui", self.project_root / "nice_ui"),
            ("vendor/qfluentwidgets", self.project_root / "vendor" / "qfluentwidgets"),
            ("orm", self.project_root / "orm"),
            ("utils", self.project_root / "utils"),
            ("agent", self.project_root / "agent"),
            ("components", self.project_root / "components"),
        ]
        
        for name, path in dependencies:
            if path.exists():
                print(f"✅ {name} 路径: {path}")
            else:
                print(f"❌ {name} 未找到: {path}")
                return False
        
        return True
    
    def get_nuitka_command(self):
        """构建 Nuitka 命令"""
        icon_path = self.project_root / "components" / "assets" / "lapped.icns"
        
        command = [
            self.python_executable,
            "-m",
            "nuitka",
            "--standalone",
            "--macos-create-app-bundle" if platform.system() == "Darwin" else "",
            f"--macos-app-name={self.app_name}" if platform.system() == "Darwin" else "",
            "--macos-app-version=1.0.0" if platform.system() == "Darwin" else "",
            f"--macos-app-icon={icon_path}" if platform.system() == "Darwin" and icon_path.exists() else "",
            "--enable-plugin=pyside6",
            
            # 包含项目模块
            "--include-package=nice_ui",
            "--include-package=vendor.qfluentwidgets", 
            "--include-package=orm",
            "--include-package=utils",
            "--include-package=agent",
            "--include-package=components",
            
            # 包含数据目录
            f"--include-data-dir={self.project_root}/vendor/qfluentwidgets=vendor/qfluentwidgets",
            f"--include-data-dir={self.project_root}/components/assets=components/assets",
            f"--include-data-dir={self.project_root}/nice_ui/language=nice_ui/language",
            f"--include-data-dir={self.project_root}/orm=orm",
            
            # 排除大型不必要的包
            "--nofollow-import-to=scipy",
            "--nofollow-import-to=numpy", 
            "--nofollow-import-to=torch",
            "--nofollow-import-to=torchaudio",
            "--nofollow-import-to=matplotlib",
            "--nofollow-import-to=pandas",
            "--nofollow-import-to=funasr",
            "--nofollow-import-to=modelscope",
            "--nofollow-import-to=av",
            "--nofollow-import-to=alibabacloud_oss_v2",
            "--nofollow-import-to=dashscope",
            
            # 优化设置
            "--python-flag=no_warnings,-O",
            "--remove-output",
            "--no-pyi-file",
            
            # 输出设置
            f"--output-dir={self.output_dir}",
            
            # 源文件
            str(self.source_file),
        ]
        
        # 过滤空字符串
        return [cmd for cmd in command if cmd]
    
    def build(self):
        """执行构建"""
        print(f"🚀 开始构建 {self.app_name}...")
        print(f"📁 源文件: {self.source_file}")
        print(f"📁 输出目录: {self.output_dir}")
        
        # 检查平台
        if platform.system() == "Darwin":
            print("🍎 检测到 macOS 平台，将生成 .app 应用包")
        elif platform.system() == "Windows":
            print("🪟 检测到 Windows 平台，将生成 .exe 可执行文件")
        else:
            print("🐧 检测到 Linux 平台，将生成可执行文件")
        
        # 构建命令
        command = self.get_nuitka_command()
        
        print("\n🔧 Nuitka 命令:")
        for i, cmd in enumerate(command):
            if i == 0:
                print(f"  {cmd} \\")
            elif i == len(command) - 1:
                print(f"    {cmd}")
            else:
                print(f"    {cmd} \\")
        
        print("\n⏳ 开始编译...")
        
        try:
            # 执行构建
            result = subprocess.run(command, cwd=self.project_root, check=True)
            
            if result.returncode == 0:
                print("\n✅ 构建成功!")
                self.post_build_info()
            else:
                print(f"\n❌ 构建失败，返回码: {result.returncode}")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"\n❌ 构建过程中出现错误: {e}")
            return False
        except KeyboardInterrupt:
            print("\n⚠️ 构建被用户中断")
            return False
        
        return True
    
    def post_build_info(self):
        """构建后信息显示"""
        # 查找生成的应用文件
        if platform.system() == "Darwin":
            app_files = list(self.output_dir.glob("*.app"))
            if app_files:
                app_path = app_files[0]
                print(f"\n📦 应用程序位置: {app_path}")
                print(f"\n🎯 运行应用:")
                print(f"  open '{app_path}'")
                print(f"\n📋 或者双击应用图标运行")
            else:
                self._show_fallback_info()
        else:
            # Windows/Linux
            exe_files = list(self.output_dir.glob("*.exe")) + list(self.output_dir.glob("setting_ui"))
            if exe_files:
                exe_path = exe_files[0]
                print(f"\n📦 可执行文件位置: {exe_path}")
                print(f"\n🎯 运行应用:")
                print(f"  {exe_path}")
            else:
                self._show_fallback_info()
        
        # 计算应用大小
        try:
            total_size = self.get_directory_size(self.output_dir)
            print(f"📏 总大小: {self.format_size(total_size)}")
        except Exception as e:
            print(f"⚠️ 无法计算应用大小: {e}")
    
    def _show_fallback_info(self):
        """显示备用信息"""
        print(f"\n📁 输出目录: {self.output_dir}")
        print("📋 输出目录内容:")
        for item in self.output_dir.iterdir():
            print(f"  - {item.name}")
    
    def get_directory_size(self, path):
        """计算目录大小"""
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total_size += os.path.getsize(filepath)
        return total_size
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"


def main():
    """主函数"""
    print("⚙️ 设置界面应用 - Nuitka 构建器")
    print("=" * 50)
    
    try:
        builder = SettingUIBuilder()
        
        # 检查依赖
        if not builder.check_dependencies():
            print("\n❌ 依赖检查失败，请安装必要的依赖后重试")
            sys.exit(1)
        
        # 清理输出目录
        builder.clean_output_dir()
        
        # 执行构建
        success = builder.build()
        
        if success:
            print("\n🎉 构建完成!")
            sys.exit(0)
        else:
            print("\n💥 构建失败!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ 构建被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 构建过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
