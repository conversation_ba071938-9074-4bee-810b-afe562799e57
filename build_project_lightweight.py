#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LinLin 项目轻量级打包脚本
解决 macOS 代码签名参数过长问题
"""

import os
import sys
import subprocess
import shutil
import platform
from pathlib import Path


class LinLinLightweightBuilder:
    """LinLin 项目轻量级构建器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.source_file = self.project_root / "run.py"
        self.output_dir = self.project_root / "dist_linlin_lite"
        self.app_name = "LinLin"
        
        # 检查并使用虚拟环境的 Python
        venv_python = self.project_root / ".venv" / "bin" / "python"
        if venv_python.exists():
            self.python_executable = str(venv_python)
            print(f"🐍 使用虚拟环境 Python: {self.python_executable}")
        else:
            self.python_executable = sys.executable
            print(f"🐍 使用系统 Python: {self.python_executable}")
        
        # 检查源文件是否存在
        if not self.source_file.exists():
            raise FileNotFoundError(f"源文件不存在: {self.source_file}")
    
    def clean_output_dir(self):
        """清理输出目录"""
        if self.output_dir.exists():
            print(f"🧹 清理输出目录: {self.output_dir}")
            shutil.rmtree(self.output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def check_dependencies(self):
        """检查构建依赖"""
        print("🔍 检查构建依赖...")
        
        # 检查 Nuitka
        try:
            result = subprocess.run([self.python_executable, "-m", "nuitka", "--version"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Nuitka 版本: {result.stdout.strip()}")
            else:
                raise Exception("Nuitka 未正确安装")
        except Exception as e:
            print(f"❌ Nuitka 检查失败: {e}")
            return False
        
        return True
    
    def get_nuitka_command(self):
        """构建 Nuitka 命令 - 轻量级版本"""
        icon_path = self.project_root / "components" / "assets" / "lapped.icns"
        
        command = [
            self.python_executable,
            "-m",
            "nuitka",
            "--standalone",
        ]
        
        # 平台特定设置
        if platform.system() == "Darwin":
            command.extend([
                "--macos-create-app-bundle",
                f"--macos-app-name={self.app_name}",
                "--macos-app-version=0.2.1",
                f"--macos-app-icon={icon_path}" if icon_path.exists() else "",
                # 禁用代码签名
                "--macos-sign-identity=-",
            ])
        elif platform.system() == "Windows":
            command.extend([
                "--windows-console-mode=disable",
                f"--windows-icon-from-ico={self.project_root}/components/assets/lapped.ico" if (self.project_root / "components" / "assets" / "lapped.ico").exists() else "",
            ])
        
        # 插件和基本设置
        command.extend([
            "--enable-plugin=pyside6",
            
            # 只包含核心项目模块
            "--include-package=nice_ui",
            "--include-package=vendor.qfluentwidgets",
            "--include-package=orm",
            "--include-package=utils", 
            "--include-package=agent",
            "--include-package=components",
            "--include-module=api_client",
        ])
        
        # 只包含必要的数据目录
        essential_data_dirs = [
            f"--include-data-dir={self.project_root}/vendor/qfluentwidgets=vendor/qfluentwidgets",
            f"--include-data-dir={self.project_root}/components/assets=components/assets",
            f"--include-data-dir={self.project_root}/orm=orm",
        ]
        
        command.extend(essential_data_dirs)
        
        # 只包含核心第三方依赖
        core_packages = [
            "httpx",
            "loguru", 
            "sqlalchemy",
            "pydantic",
            "packaging",
            "dashscope",
            "alibabacloud_oss_v2",
            "openai",
        ]
        
        for pkg in core_packages:
            command.append(f"--include-package={pkg}")
        
        # 排除所有大型包
        exclude_packages = [
            "scipy", "numpy", "torch", "torchaudio", "matplotlib", "pandas",
            "funasr", "modelscope", "av", "jupyter", "IPython", "notebook", 
            "pytest", "setuptools", "wheel", "pip", "nuitka", "pyinstaller",
            "darkdetect", "colorthief", "path", "pytz", "pydantic_settings",
            "dotenv", "socksio", "services"
        ]
        
        for pkg in exclude_packages:
            command.append(f"--nofollow-import-to={pkg}")
        
        # 优化设置
        command.extend([
            "--python-flag=no_warnings,-O",
            "--remove-output",
            "--no-pyi-file",
            "--assume-yes-for-downloads",
            
            # 输出设置
            f"--output-dir={self.output_dir}",
            
            # 源文件
            str(self.source_file),
        ])
        
        # 过滤空字符串
        return [cmd for cmd in command if cmd]
    
    def build(self):
        """执行构建"""
        print(f"🚀 开始构建 LinLin 项目（轻量级版本）...")
        print(f"📁 入口文件: {self.source_file}")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"📦 策略: 轻量级构建，避免代码签名问题")
        
        # 检查平台
        platform_info = {
            "Darwin": ("🍎 macOS", ".app 应用包"),
            "Windows": ("🪟 Windows", ".exe 可执行文件"),
            "Linux": ("🐧 Linux", "可执行文件")
        }
        
        system = platform.system()
        if system in platform_info:
            icon, file_type = platform_info[system]
            print(f"{icon} 检测到 {system} 平台，将生成 {file_type}")
        
        # 构建命令
        command = self.get_nuitka_command()
        
        print("\n🔧 Nuitka 命令:")
        print("  " + " \\\n    ".join(command))
        
        print("\n⏳ 开始编译...")
        print("📝 注意: 轻量级版本，编译速度较快...")
        
        try:
            # 执行构建
            result = subprocess.run(command, cwd=self.project_root, check=True)
            
            if result.returncode == 0:
                print("\n✅ 构建成功!")
                self.post_build_info()
                return True
            else:
                print(f"\n❌ 构建失败，返回码: {result.returncode}")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"\n❌ 构建过程中出现错误: {e}")
            return False
        except KeyboardInterrupt:
            print("\n⚠️ 构建被用户中断")
            return False
    
    def post_build_info(self):
        """构建后信息显示"""
        # 查找生成的应用文件
        if platform.system() == "Darwin":
            app_files = list(self.output_dir.glob("*.app"))
            if app_files:
                app_path = app_files[0]
                print(f"\n📦 应用程序位置: {app_path}")
                print(f"\n🎯 运行应用:")
                print(f"  open '{app_path}'")
                print(f"\n📋 或者双击应用图标运行")
            else:
                self._show_fallback_info()
        else:
            # Windows/Linux
            exe_files = list(self.output_dir.glob("*.exe")) + list(self.output_dir.glob("run"))
            if exe_files:
                exe_path = exe_files[0]
                print(f"\n📦 可执行文件位置: {exe_path}")
                print(f"\n🎯 运行应用:")
                print(f"  {exe_path}")
            else:
                self._show_fallback_info()
        
        # 计算应用大小
        try:
            total_size = self.get_directory_size(self.output_dir)
            print(f"📏 总大小: {self.format_size(total_size)}")
        except Exception as e:
            print(f"⚠️ 无法计算应用大小: {e}")
        
        print(f"\n💡 提示:")
        print(f"  - 此版本避免了 macOS 代码签名问题")
        print(f"  - 如果缺少某些功能，请手动安装对应的 Python 包")
        print(f"  - 或者使用完整版本构建脚本")
    
    def _show_fallback_info(self):
        """显示备用信息"""
        print(f"\n📁 输出目录: {self.output_dir}")
        print("📋 输出目录内容:")
        for item in self.output_dir.iterdir():
            print(f"  - {item.name}")
    
    def get_directory_size(self, path):
        """计算目录大小"""
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total_size += os.path.getsize(filepath)
        return total_size
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"


def main():
    """主函数"""
    print("🎬 LinLin 项目 - 轻量级构建器")
    print("=" * 50)
    print("📦 此版本专门解决 macOS 代码签名参数过长问题")
    print("⚡ 构建速度快，应用体积小，但可能缺少某些功能")
    print()
    
    try:
        builder = LinLinLightweightBuilder()
        
        # 检查依赖
        if not builder.check_dependencies():
            print("\n❌ 依赖检查失败，请安装必要的依赖后重试")
            sys.exit(1)
        
        # 清理输出目录
        builder.clean_output_dir()
        
        # 执行构建
        success = builder.build()
        
        if success:
            print("\n🎉 构建完成!")
            print("\n📚 如果应用运行时缺少模块，请尝试:")
            print("  1. 使用 build_project_full_deps.py 完整版本")
            print("  2. 手动安装缺少的 Python 包")
            sys.exit(0)
        else:
            print("\n💥 构建失败!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ 构建被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 构建过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
