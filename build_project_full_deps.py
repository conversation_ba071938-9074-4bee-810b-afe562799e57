#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LinLin 项目完整依赖打包脚本
确保包含 pyproject.toml 中的所有依赖
"""

import os
import sys
import subprocess
import shutil
import platform
from pathlib import Path


class LinLinFullDepsBuilder:
    """LinLin 项目完整依赖构建器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.source_file = self.project_root / "run.py"
        self.output_dir = self.project_root / "dist_linlin_full"
        self.app_name = "LinLin"
        
        # 检查并使用虚拟环境的 Python
        venv_python = self.project_root / ".venv" / "bin" / "python"
        if venv_python.exists():
            self.python_executable = str(venv_python)
            print(f"🐍 使用虚拟环境 Python: {self.python_executable}")
        else:
            self.python_executable = sys.executable
            print(f"🐍 使用系统 Python: {self.python_executable}")
        
        # 检查源文件是否存在
        if not self.source_file.exists():
            raise FileNotFoundError(f"源文件不存在: {self.source_file}")
    
    def clean_output_dir(self):
        """清理输出目录"""
        if self.output_dir.exists():
            print(f"🧹 清理输出目录: {self.output_dir}")
            shutil.rmtree(self.output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def check_dependencies(self):
        """检查构建依赖"""
        print("🔍 检查构建依赖...")
        
        # 检查 Nuitka
        try:
            result = subprocess.run([self.python_executable, "-m", "nuitka", "--version"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Nuitka 版本: {result.stdout.strip()}")
            else:
                raise Exception("Nuitka 未正确安装")
        except Exception as e:
            print(f"❌ Nuitka 检查失败: {e}")
            return False
        
        # 检查关键第三方依赖
        required_packages = [
            "PySide6", "httpx", "loguru", "openai", "sqlalchemy", "pydantic",
            "dashscope", "alibabacloud_oss_v2", "modelscope", "funasr"
        ]
        
        for pkg in required_packages:
            try:
                result = subprocess.run([self.python_executable, "-c", f"import {pkg}"], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print(f"✅ {pkg}: 已安装")
                else:
                    print(f"⚠️ {pkg}: 未安装或导入失败")
            except Exception as e:
                print(f"⚠️ {pkg}: 检查失败 - {e}")
        
        return True
    
    def get_nuitka_command(self):
        """构建 Nuitka 命令 - 包含所有依赖"""
        icon_path = self.project_root / "components" / "assets" / "lapped.icns"
        
        command = [
            self.python_executable,
            "-m",
            "nuitka",
            "--standalone",
        ]
        
        # 平台特定设置
        if platform.system() == "Darwin":
            command.extend([
                "--macos-create-app-bundle",
                f"--macos-app-name={self.app_name}",
                "--macos-app-version=0.2.1",
                f"--macos-app-icon={icon_path}" if icon_path.exists() else "",
                # 禁用代码签名以避免参数列表过长的问题
                "--macos-sign-identity=-",
            ])
        elif platform.system() == "Windows":
            command.extend([
                "--windows-console-mode=disable",
                f"--windows-icon-from-ico={self.project_root}/components/assets/lapped.ico" if (self.project_root / "components" / "assets" / "lapped.ico").exists() else "",
            ])
        
        # 插件和基本设置
        command.extend([
            "--enable-plugin=pyside6",
            "--enable-plugin=multiprocessing",
            
            # 包含项目核心模块
            "--include-package=nice_ui",
            "--include-package=vendor.qfluentwidgets",
            "--include-package=orm",
            "--include-package=utils", 
            "--include-package=agent",
            "--include-package=components",
            "--include-package=services",
            "--include-module=api_client",
        ])
        
        # 包含数据目录
        data_dirs = [
            f"--include-data-dir={self.project_root}/vendor/qfluentwidgets=vendor/qfluentwidgets",
            f"--include-data-dir={self.project_root}/components/assets=components/assets",
            f"--include-data-dir={self.project_root}/orm=orm",
        ]
        
        # 检查可选数据目录
        optional_dirs = [
            ("nice_ui/language", "nice_ui/language"),
            ("config", "config"),
            ("logs", "logs"),
            ("result", "result"),
        ]
        
        for src, dst in optional_dirs:
            src_path = self.project_root / src
            if src_path.exists():
                data_dirs.append(f"--include-data-dir={src_path}={dst}")
        
        command.extend(data_dirs)
        
        # 包含所有 pyproject.toml 中的依赖
        # 根据 pyproject.toml 的依赖列表
        third_party_packages = [
            # 网络和HTTP
            "httpx",
            "socksio",
            
            # 日志和工具
            "loguru", 
            "packaging",
            "darkdetect",
            "colorthief",
            "path",
            "pytz",
            
            # 数据库和ORM
            "sqlalchemy",
            
            # 数据验证和配置
            "pydantic",
            "pydantic_settings",
            "dotenv",
            
            # AI和云服务
            "openai",
            "dashscope",
            "alibabacloud_oss_v2",
            
            # 音视频处理
            "av",
            
            # AI/ML框架（使用数据包含策略）
            # "modelscope", "funasr" - 这些包太大，使用特殊处理
        ]
        
        # 包含常规第三方包
        for pkg in third_party_packages:
            command.append(f"--include-package={pkg}")
        
        # 对于大型AI/ML包，使用数据包含策略
        large_packages = [
            "modelscope",
            "funasr", 
            "scipy",
            "numpy",
            "torch",
            "torchaudio",
        ]
        
        for pkg in large_packages:
            command.extend([
                f"--include-package-data={pkg}",
                f"--nofollow-import-to={pkg}",
            ])
        
        # 只排除真正不需要的包
        exclude_packages = [
            "matplotlib", "pandas", "jupyter", "IPython", "notebook", 
            "pytest", "setuptools", "wheel", "pip", "nuitka", "pyinstaller"
        ]
        
        for pkg in exclude_packages:
            command.append(f"--nofollow-import-to={pkg}")
        
        # 优化设置
        command.extend([
            "--python-flag=no_warnings,-O",
            "--remove-output",
            "--no-pyi-file",
            "--assume-yes-for-downloads",
            
            # 输出设置
            f"--output-dir={self.output_dir}",
            
            # 源文件
            str(self.source_file),
        ])
        
        # 过滤空字符串
        return [cmd for cmd in command if cmd]
    
    def build(self):
        """执行构建"""
        print(f"🚀 开始构建 LinLin 项目（完整依赖版本）...")
        print(f"📁 入口文件: {self.source_file}")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"📦 策略: 包含所有 pyproject.toml 依赖")
        
        # 检查平台
        platform_info = {
            "Darwin": ("🍎 macOS", ".app 应用包"),
            "Windows": ("🪟 Windows", ".exe 可执行文件"),
            "Linux": ("🐧 Linux", "可执行文件")
        }
        
        system = platform.system()
        if system in platform_info:
            icon, file_type = platform_info[system]
            print(f"{icon} 检测到 {system} 平台，将生成 {file_type}")
        
        # 构建命令
        command = self.get_nuitka_command()
        
        print("\n🔧 Nuitka 命令:")
        print("  " + " \\\n    ".join(command))
        
        print("\n⏳ 开始编译...")
        print("📝 注意: 包含完整依赖的编译需要更长时间，请耐心等待...")
        
        try:
            # 执行构建
            result = subprocess.run(command, cwd=self.project_root, check=True)
            
            if result.returncode == 0:
                print("\n✅ 构建成功!")
                self.post_build_info()
                return True
            else:
                print(f"\n❌ 构建失败，返回码: {result.returncode}")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"\n❌ 构建过程中出现错误: {e}")
            return False
        except KeyboardInterrupt:
            print("\n⚠️ 构建被用户中断")
            return False
    
    def post_build_info(self):
        """构建后信息显示"""
        # 查找生成的应用文件
        if platform.system() == "Darwin":
            app_files = list(self.output_dir.glob("*.app"))
            if app_files:
                app_path = app_files[0]
                print(f"\n📦 应用程序位置: {app_path}")
                print(f"\n🎯 运行应用:")
                print(f"  open '{app_path}'")
                print(f"\n📋 或者双击应用图标运行")
            else:
                self._show_fallback_info()
        else:
            # Windows/Linux
            exe_files = list(self.output_dir.glob("*.exe")) + list(self.output_dir.glob("run"))
            if exe_files:
                exe_path = exe_files[0]
                print(f"\n📦 可执行文件位置: {exe_path}")
                print(f"\n🎯 运行应用:")
                print(f"  {exe_path}")
            else:
                self._show_fallback_info()
        
        # 计算应用大小
        try:
            total_size = self.get_directory_size(self.output_dir)
            print(f"📏 总大小: {self.format_size(total_size)}")
        except Exception as e:
            print(f"⚠️ 无法计算应用大小: {e}")
        
        print(f"\n💡 提示:")
        print(f"  - 此版本包含完整依赖，应该解决 ModuleNotFoundError 问题")
        print(f"  - 如果仍有问题，请检查具体的错误信息")
        print(f"  - 应用体积较大是正常的，因为包含了所有依赖")
    
    def _show_fallback_info(self):
        """显示备用信息"""
        print(f"\n📁 输出目录: {self.output_dir}")
        print("📋 输出目录内容:")
        for item in self.output_dir.iterdir():
            print(f"  - {item.name}")
    
    def get_directory_size(self, path):
        """计算目录大小"""
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total_size += os.path.getsize(filepath)
        return total_size
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"


def main():
    """主函数"""
    print("🎬 LinLin 项目 - 完整依赖构建器")
    print("=" * 50)
    print("📦 此版本将包含 pyproject.toml 中的所有依赖")
    print("⚠️ 构建时间较长，应用体积较大，但功能完整")
    print()
    
    try:
        builder = LinLinFullDepsBuilder()
        
        # 检查依赖
        # if not builder.check_dependencies():
        #     print("\n❌ 依赖检查失败，请安装必要的依赖后重试")
        #     print("💡 提示: 运行 'uv sync' 或 'pip install -e .' 安装依赖")
        #     sys.exit(1)
        
        # 清理输出目录
        builder.clean_output_dir()
        
        # 执行构建
        success = builder.build()
        
        if success:
            print("\n🎉 构建完成!")
            print("\n📚 这个版本应该解决了 ModuleNotFoundError 问题")
            sys.exit(0)
        else:
            print("\n💥 构建失败!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ 构建被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 构建过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
