#!/bin/bash
# macOS 原生标题栏示例应用的简化构建脚本

set -e  # 遇到错误立即退出

echo "🍎 macOS 原生标题栏示例应用 - 快速构建"
echo "================================================"

# 检查是否在 macOS 上运行
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "⚠️  警告: 当前不在 macOS 平台，构建的应用可能无法正常运行"
fi

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SOURCE_FILE="$PROJECT_ROOT/examples/mac_native_fluent_window.py"
OUTPUT_DIR="$PROJECT_ROOT/dist_mac_native"
APP_NAME="MacNativeFluentWindow"
ICON_PATH="$PROJECT_ROOT/components/assets/lapped.icns"

echo "📁 项目根目录: $PROJECT_ROOT"
echo "📁 源文件: $SOURCE_FILE"
echo "📁 输出目录: $OUTPUT_DIR"

# 检查源文件是否存在
if [ ! -f "$SOURCE_FILE" ]; then
    echo "❌ 源文件不存在: $SOURCE_FILE"
    exit 1
fi

# 清理输出目录
if [ -d "$OUTPUT_DIR" ]; then
    echo "🧹 清理输出目录..."
    rm -rf "$OUTPUT_DIR"
fi
mkdir -p "$OUTPUT_DIR"

# 检查 Nuitka 是否安装
echo "🔍 检查 Nuitka..."
if ! python3 -m nuitka --version > /dev/null 2>&1; then
    echo "❌ Nuitka 未安装，请先安装: pip install nuitka"
    exit 1
fi

echo "✅ Nuitka 已安装"

# 构建应用
echo "🚀 开始构建应用..."

python3 -m nuitka \
    --standalone \
    --onefile-tempdir-spec="{CACHE_DIR}/FluentWidgets/{PRODUCT}/{VERSION}" \
    --macos-create-app-bundle \
    --macos-app-name="$APP_NAME" \
    --macos-app-version="1.0.0" \
    --macos-app-mode=ui-element \
    $([ -f "$ICON_PATH" ] && echo "--macos-app-icon=$ICON_PATH") \
    --enable-plugin=pyside6 \
    --include-package-data=vendor.qfluentwidgets \
    --include-package=vendor.qfluentwidgets \
    --include-data-dir="$PROJECT_ROOT/vendor=$APP_NAME.app/Contents/Resources/vendor" \
    --include-data-dir="$PROJECT_ROOT/components/assets=$APP_NAME.app/Contents/Resources/components/assets" \
    --python-flag=no_warnings,-O \
    --remove-output \
    --no-pyi-file \
    --output-dir="$OUTPUT_DIR" \
    "$SOURCE_FILE"

# 检查构建结果
APP_PATH="$OUTPUT_DIR/$APP_NAME.app"
if [ -d "$APP_PATH" ]; then
    echo ""
    echo "✅ 构建成功!"
    echo "📦 应用程序位置: $APP_PATH"
    
    # 计算应用大小
    APP_SIZE=$(du -sh "$APP_PATH" | cut -f1)
    echo "📏 应用大小: $APP_SIZE"
    
    echo ""
    echo "🎯 运行应用:"
    echo "  open '$APP_PATH'"
    echo ""
    echo "📋 或者双击应用图标运行"
    
else
    echo "❌ 构建失败，应用程序未找到: $APP_PATH"
    exit 1
fi

echo ""
echo "🎉 构建完成!"
