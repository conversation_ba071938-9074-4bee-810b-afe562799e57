#titleButton {
    font: 14px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
    font-weight: 500;
    color: black;
    background-color: transparent;
    border: none;
    margin: 0;
    padding-left: 8px;
    text-align: left;
    border-radius: 5px;
}

#titleButton:hover {
    background-color: rgba(0, 0, 0, 9);
}

#titleButton:pressed {
    background-color: rgba(0, 0, 0, 6);
}

#titleButton:disabled {
    color: rgba(0, 0, 0, 0.4);
}

#weekDayLabel {
    font: 12px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
    font-weight: 500;
    color: black;
    background-color: transparent;
    border: none;
    text-align: center;
}

#weekDayGroup {
    background-color: transparent;
}

CalendarViewBase {
    background-color: rgb(255, 255, 255);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
}

ScrollViewBase {
    border: none;
    padding: 0px 1px 0px 1px;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    border-top: 1px solid rgb(240, 240, 240);
    background-color: transparent;
}

CalendarPicker {
    color: rgba(0, 0, 0, 0.6063);
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(0, 0, 0, 0.073);
    border-bottom: 1px solid rgba(0, 0, 0, 0.183);
    border-radius: 5px;
    font: 14px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
    padding: 5px 32px 6px 12px;
    outline: none;
    text-align: left;
}


CalendarPicker:hover {
    background: rgba(249, 249, 249, 0.5);
}

CalendarPicker:pressed {
    background: rgba(249, 249, 249, 0.3);
    border-bottom: 1px solid rgba(0, 0, 0, 0.073);
}

CalendarPicker:disabled {
    color: rgba(0, 0, 0, 0.36);
    background: rgba(249, 249, 249, 0.3);
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

CalendarPicker[hasDate=true] {
    color: black;
}