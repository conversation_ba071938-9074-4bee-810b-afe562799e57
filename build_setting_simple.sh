#!/bin/bash
# 设置界面应用的简化构建脚本

set -e  # 遇到错误立即退出

echo "⚙️ 设置界面应用 - 快速构建"
echo "================================================"

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SOURCE_FILE="$PROJECT_ROOT/nice_ui/ui/setting_ui.py"
OUTPUT_DIR="$PROJECT_ROOT/dist_setting_ui"
APP_NAME="SettingUI"
ICON_PATH="$PROJECT_ROOT/components/assets/lapped.icns"

echo "📁 项目根目录: $PROJECT_ROOT"
echo "📁 源文件: $SOURCE_FILE"
echo "📁 输出目录: $OUTPUT_DIR"

# 检查源文件是否存在
if [ ! -f "$SOURCE_FILE" ]; then
    echo "❌ 源文件不存在: $SOURCE_FILE"
    exit 1
fi

# 清理输出目录
if [ -d "$OUTPUT_DIR" ]; then
    echo "🧹 清理输出目录..."
    rm -rf "$OUTPUT_DIR"
fi
mkdir -p "$OUTPUT_DIR"

# 检查 Nuitka 是否安装
echo "🔍 检查 Nuitka..."
if ! python3 -m nuitka --version > /dev/null 2>&1; then
    echo "❌ Nuitka 未安装，请先安装: pip install nuitka"
    exit 1
fi

echo "✅ Nuitka 已安装"

# 检测平台
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 检测到 macOS 平台，将生成 .app 应用包"
    PLATFORM_ARGS="--macos-create-app-bundle --macos-app-name=$APP_NAME --macos-app-version=1.0.0"
    if [ -f "$ICON_PATH" ]; then
        PLATFORM_ARGS="$PLATFORM_ARGS --macos-app-icon=$ICON_PATH"
    fi
elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    echo "🪟 检测到 Windows 平台，将生成 .exe 可执行文件"
    PLATFORM_ARGS=""
else
    echo "🐧 检测到 Linux 平台，将生成可执行文件"
    PLATFORM_ARGS=""
fi

# 构建应用
echo "🚀 开始构建应用..."

python3 -m nuitka \
    --standalone \
    $PLATFORM_ARGS \
    --enable-plugin=pyside6 \
    --include-package=nice_ui \
    --include-package=vendor.qfluentwidgets \
    --include-package=orm \
    --include-package=utils \
    --include-package=agent \
    --include-package=components \
    --include-data-dir="$PROJECT_ROOT/vendor/qfluentwidgets=vendor/qfluentwidgets" \
    --include-data-dir="$PROJECT_ROOT/components/assets=components/assets" \
    --include-data-dir="$PROJECT_ROOT/nice_ui/language=nice_ui/language" \
    --include-data-dir="$PROJECT_ROOT/orm=orm" \
    --nofollow-import-to=scipy \
    --nofollow-import-to=numpy \
    --nofollow-import-to=torch \
    --nofollow-import-to=torchaudio \
    --nofollow-import-to=matplotlib \
    --nofollow-import-to=pandas \
    --nofollow-import-to=funasr \
    --nofollow-import-to=modelscope \
    --nofollow-import-to=av \
    --nofollow-import-to=alibabacloud_oss_v2 \
    --nofollow-import-to=dashscope \
    --python-flag=no_warnings,-O \
    --remove-output \
    --no-pyi-file \
    --output-dir="$OUTPUT_DIR" \
    "$SOURCE_FILE"

# 检查构建结果
echo ""
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS - 查找 .app 文件
    APP_PATH=$(find "$OUTPUT_DIR" -name "*.app" -type d | head -1)
    if [ -n "$APP_PATH" ]; then
        echo "✅ 构建成功!"
        echo "📦 应用程序位置: $APP_PATH"
        
        # 计算应用大小
        APP_SIZE=$(du -sh "$APP_PATH" | cut -f1)
        echo "📏 应用大小: $APP_SIZE"
        
        echo ""
        echo "🎯 运行应用:"
        echo "  open '$APP_PATH'"
        echo ""
        echo "📋 或者双击应用图标运行"
    else
        echo "❌ 构建失败，应用程序未找到"
        exit 1
    fi
else
    # Windows/Linux - 查找可执行文件
    EXE_PATH=$(find "$OUTPUT_DIR" -name "*.exe" -o -name "setting_ui" | head -1)
    if [ -n "$EXE_PATH" ]; then
        echo "✅ 构建成功!"
        echo "📦 可执行文件位置: $EXE_PATH"
        
        # 计算文件大小
        if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
            EXE_SIZE=$(du -sh "$EXE_PATH" | cut -f1)
        else
            EXE_SIZE=$(du -sh "$EXE_PATH" | cut -f1)
        fi
        echo "📏 文件大小: $EXE_SIZE"
        
        echo ""
        echo "🎯 运行应用:"
        echo "  $EXE_PATH"
    else
        echo "❌ 构建失败，可执行文件未找到"
        echo "📁 输出目录内容:"
        ls -la "$OUTPUT_DIR"
        exit 1
    fi
fi

echo ""
echo "🎉 构建完成!"
