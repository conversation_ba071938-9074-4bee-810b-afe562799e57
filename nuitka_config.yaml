# macOS 原生标题栏示例应用的 Nuitka 构建配置
# 使用方法: python build_mac_native_window.py

app:
  name: "MacNativeFluentWindow"
  version: "1.0.0"
  display_name: "macOS 原生标题栏示例"
  bundle_identifier: "com.fluentwidgets.mac-native-window"
  copyright: "© 2025 FluentWidgets示例"

source:
  main_file: "examples/mac_native_fluent_window.py"
  
output:
  directory: "dist_mac_native"
  clean_before_build: true

nuitka:
  # 基本选项
  standalone: true
  onefile_tempdir_spec: "{CACHE_DIR}/{COMPANY}/{PRODUCT}/{VERSION}"
  
  # macOS 特定选项
  macos_create_app_bundle: true
  macos_app_icon: "components/assets/lapped.icns"
  
  # 插件
  plugins:
    - "pyside6"
  
  # 包含的包和数据
  include_packages:
    - "vendor.qfluentwidgets"
  
  include_package_data:
    - "vendor.qfluentwidgets"
  
  include_data_dirs:
    - source: "vendor"
      target: "{APP_NAME}.app/Contents/Resources/vendor"
    - source: "components/assets" 
      target: "{APP_NAME}.app/Contents/Resources/components/assets"
  
  # Python 标志
  python_flags:
    - "no_warnings"
    - "-O"
  
  # 其他选项
  remove_output: true
  no_pyi_file: true
  
  # 排除的模块（可选，用于减小应用大小）
  exclude_modules:
    - "tkinter"
    - "matplotlib"
    - "scipy"
    - "numpy"
    - "pandas"
    - "jupyter"
    - "IPython"

# 构建后操作
post_build:
  # 是否显示应用信息
  show_app_info: true
  
  # 是否自动打开应用目录
  open_output_dir: false
  
  # 是否运行应用进行测试
  test_run: false
