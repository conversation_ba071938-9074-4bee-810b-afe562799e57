/* Generated code for Python module 'Quartz$CoreGraphics$_metadata'
 * created by Nuitka version 2.7.5
 *
 * This code is in part copyright 2025 Kay <PERSON>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "nuitka/prelude.h"

#include "nuitka/unfreezing.h"

#include "__helpers.h"

/* The "module_Quartz$CoreGraphics$_metadata" is a Python object pointer of module type.
 *
 * Note: For full compatibility with CPython, every module variable access
 * needs to go through it except for cases where the module cannot possibly
 * have changed in the mean time.
 */

PyObject *module_Quartz$CoreGraphics$_metadata;
PyDictObject *moduledict_Quartz$CoreGraphics$_metadata;

/* The declarations of module constants used, if any. */
static PyObject *mod_consts[1461];
#ifndef __NUITKA_NO_ASSERT__
static Py_hash_t mod_consts_hash[1461];
#endif

static PyObject *module_filename_obj = NULL;

/* Indicator if this modules private constants were created yet. */
static bool constants_created = false;

/* Function to create module private constants. */
static void createModuleConstants(PyThreadState *tstate) {
    if (constants_created == false) {
        loadConstantsBlob(tstate, &mod_consts[0], UN_TRANSLATE("Quartz.CoreGraphics._metadata"));
        constants_created = true;

#ifndef __NUITKA_NO_ASSERT__
        for (int i = 0; i < 1461; i++) {
            mod_consts_hash[i] = DEEP_HASH(tstate, mod_consts[i]);
        }
#endif
    }
}

// We want to be able to initialize the "__main__" constants in any case.
#if 0
void createMainModuleConstants(PyThreadState *tstate) {
    createModuleConstants(tstate);
}
#endif

/* Function to verify module private constants for non-corruption. */
#ifndef __NUITKA_NO_ASSERT__
void checkModuleConstants_Quartz$CoreGraphics$_metadata(PyThreadState *tstate) {
    // The module may not have been used at all, then ignore this.
    if (constants_created == false) return;

    for (int i = 0; i < 1461; i++) {
        assert(mod_consts_hash[i] == DEEP_HASH(tstate, mod_consts[i]));
        CHECK_OBJECT_DEEP(mod_consts[i]);
    }
}
#endif

// Helper to preserving module variables for Python3.11+
#if 5
#if PYTHON_VERSION >= 0x3c0
NUITKA_MAY_BE_UNUSED static uint32_t _Nuitka_PyDictKeys_GetVersionForCurrentState(PyInterpreterState *interp, PyDictKeysObject *dk)
{
    if (dk->dk_version != 0) {
        return dk->dk_version;
    }
    uint32_t result = interp->dict_state.next_keys_version++;
    dk->dk_version = result;
    return result;
}
#elif PYTHON_VERSION >= 0x3b0
static uint32_t _Nuitka_next_dict_keys_version = 2;

NUITKA_MAY_BE_UNUSED static uint32_t _Nuitka_PyDictKeys_GetVersionForCurrentState(PyDictKeysObject *dk)
{
    if (dk->dk_version != 0) {
        return dk->dk_version;
    }
    uint32_t result = _Nuitka_next_dict_keys_version++;
    dk->dk_version = result;
    return result;
}
#endif
#endif

// Accessors to module variables.
static PyObject *module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(PyThreadState *tstate) {
#if 0
    PyObject *result;

#if PYTHON_VERSION < 0x3b0
    static uint64_t dict_version = 0;
    static PyObject *cache_value = NULL;

    if (moduledict_Quartz$CoreGraphics$_metadata->ma_version_tag == dict_version) {
        CHECK_OBJECT_X(cache_value);
        result = cache_value;
    } else {
        dict_version = moduledict_Quartz$CoreGraphics$_metadata->ma_version_tag;

        result = GET_STRING_DICT_VALUE(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[7]);
        cache_value = result;
    }
#else
    static uint32_t dict_keys_version = 0xFFFFFFFF;
    static Py_ssize_t cache_dk_index = 0;

    PyDictKeysObject *dk = moduledict_Quartz$CoreGraphics$_metadata->ma_keys;
    if (likely(DK_IS_UNICODE(dk))) {

#if PYTHON_VERSION >= 0x3c0
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(tstate->interp, dk);
#else
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(dk);
#endif

        if (current_dk_version != dict_keys_version) {
            dict_keys_version = current_dk_version;
            Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[7]);
            assert(hash != -1);

            cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[7], hash);
        }

        if (cache_dk_index >= 0) {
            assert(dk->dk_kind != DICT_KEYS_SPLIT);

            PyDictUnicodeEntry *entries = DK_UNICODE_ENTRIES(dk);

            result = entries[cache_dk_index].me_value;

            if (unlikely(result == NULL)) {
                Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[7]);
                assert(hash != -1);

                cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[7], hash);

                if (cache_dk_index >= 0) {
                    result = entries[cache_dk_index].me_value;
                }
            }
        } else {
            result = NULL;
        }
    } else {
        result = GET_STRING_DICT_VALUE(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[7]);
    }
#endif

#else
    PyObject *result = GET_STRING_DICT_VALUE(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[7]);
#endif

    if (unlikely(result == NULL)) {
        result = GET_STRING_DICT_VALUE(dict_builtin, (Nuitka_StringObject *)mod_consts[7]);
    }

    return result;
}

static PyObject *module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$__spec__(PyThreadState *tstate) {
#if 0
    PyObject *result;

#if PYTHON_VERSION < 0x3b0
    static uint64_t dict_version = 0;
    static PyObject *cache_value = NULL;

    if (moduledict_Quartz$CoreGraphics$_metadata->ma_version_tag == dict_version) {
        CHECK_OBJECT_X(cache_value);
        result = cache_value;
    } else {
        dict_version = moduledict_Quartz$CoreGraphics$_metadata->ma_version_tag;

        result = GET_STRING_DICT_VALUE(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[1460]);
        cache_value = result;
    }
#else
    static uint32_t dict_keys_version = 0xFFFFFFFF;
    static Py_ssize_t cache_dk_index = 0;

    PyDictKeysObject *dk = moduledict_Quartz$CoreGraphics$_metadata->ma_keys;
    if (likely(DK_IS_UNICODE(dk))) {

#if PYTHON_VERSION >= 0x3c0
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(tstate->interp, dk);
#else
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(dk);
#endif

        if (current_dk_version != dict_keys_version) {
            dict_keys_version = current_dk_version;
            Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[1460]);
            assert(hash != -1);

            cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[1460], hash);
        }

        if (cache_dk_index >= 0) {
            assert(dk->dk_kind != DICT_KEYS_SPLIT);

            PyDictUnicodeEntry *entries = DK_UNICODE_ENTRIES(dk);

            result = entries[cache_dk_index].me_value;

            if (unlikely(result == NULL)) {
                Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[1460]);
                assert(hash != -1);

                cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[1460], hash);

                if (cache_dk_index >= 0) {
                    result = entries[cache_dk_index].me_value;
                }
            }
        } else {
            result = NULL;
        }
    } else {
        result = GET_STRING_DICT_VALUE(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[1460]);
    }
#endif

#else
    PyObject *result = GET_STRING_DICT_VALUE(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[1460]);
#endif

    if (unlikely(result == NULL)) {
        result = GET_STRING_DICT_VALUE(dict_builtin, (Nuitka_StringObject *)mod_consts[1460]);
    }

    return result;
}

static PyObject *module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$misc(PyThreadState *tstate) {
#if 0
    PyObject *result;

#if PYTHON_VERSION < 0x3b0
    static uint64_t dict_version = 0;
    static PyObject *cache_value = NULL;

    if (moduledict_Quartz$CoreGraphics$_metadata->ma_version_tag == dict_version) {
        CHECK_OBJECT_X(cache_value);
        result = cache_value;
    } else {
        dict_version = moduledict_Quartz$CoreGraphics$_metadata->ma_version_tag;

        result = GET_STRING_DICT_VALUE(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[12]);
        cache_value = result;
    }
#else
    static uint32_t dict_keys_version = 0xFFFFFFFF;
    static Py_ssize_t cache_dk_index = 0;

    PyDictKeysObject *dk = moduledict_Quartz$CoreGraphics$_metadata->ma_keys;
    if (likely(DK_IS_UNICODE(dk))) {

#if PYTHON_VERSION >= 0x3c0
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(tstate->interp, dk);
#else
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(dk);
#endif

        if (current_dk_version != dict_keys_version) {
            dict_keys_version = current_dk_version;
            Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[12]);
            assert(hash != -1);

            cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[12], hash);
        }

        if (cache_dk_index >= 0) {
            assert(dk->dk_kind != DICT_KEYS_SPLIT);

            PyDictUnicodeEntry *entries = DK_UNICODE_ENTRIES(dk);

            result = entries[cache_dk_index].me_value;

            if (unlikely(result == NULL)) {
                Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[12]);
                assert(hash != -1);

                cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[12], hash);

                if (cache_dk_index >= 0) {
                    result = entries[cache_dk_index].me_value;
                }
            }
        } else {
            result = NULL;
        }
    } else {
        result = GET_STRING_DICT_VALUE(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[12]);
    }
#endif

#else
    PyObject *result = GET_STRING_DICT_VALUE(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[12]);
#endif

    if (unlikely(result == NULL)) {
        result = GET_STRING_DICT_VALUE(dict_builtin, (Nuitka_StringObject *)mod_consts[12]);
    }

    return result;
}

static PyObject *module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(PyThreadState *tstate) {
#if 0
    PyObject *result;

#if PYTHON_VERSION < 0x3b0
    static uint64_t dict_version = 0;
    static PyObject *cache_value = NULL;

    if (moduledict_Quartz$CoreGraphics$_metadata->ma_version_tag == dict_version) {
        CHECK_OBJECT_X(cache_value);
        result = cache_value;
    } else {
        dict_version = moduledict_Quartz$CoreGraphics$_metadata->ma_version_tag;

        result = GET_STRING_DICT_VALUE(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[5]);
        cache_value = result;
    }
#else
    static uint32_t dict_keys_version = 0xFFFFFFFF;
    static Py_ssize_t cache_dk_index = 0;

    PyDictKeysObject *dk = moduledict_Quartz$CoreGraphics$_metadata->ma_keys;
    if (likely(DK_IS_UNICODE(dk))) {

#if PYTHON_VERSION >= 0x3c0
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(tstate->interp, dk);
#else
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(dk);
#endif

        if (current_dk_version != dict_keys_version) {
            dict_keys_version = current_dk_version;
            Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[5]);
            assert(hash != -1);

            cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[5], hash);
        }

        if (cache_dk_index >= 0) {
            assert(dk->dk_kind != DICT_KEYS_SPLIT);

            PyDictUnicodeEntry *entries = DK_UNICODE_ENTRIES(dk);

            result = entries[cache_dk_index].me_value;

            if (unlikely(result == NULL)) {
                Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[5]);
                assert(hash != -1);

                cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[5], hash);

                if (cache_dk_index >= 0) {
                    result = entries[cache_dk_index].me_value;
                }
            }
        } else {
            result = NULL;
        }
    } else {
        result = GET_STRING_DICT_VALUE(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[5]);
    }
#endif

#else
    PyObject *result = GET_STRING_DICT_VALUE(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[5]);
#endif

    if (unlikely(result == NULL)) {
        result = GET_STRING_DICT_VALUE(dict_builtin, (Nuitka_StringObject *)mod_consts[5]);
    }

    return result;
}

static PyObject *module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(PyThreadState *tstate) {
#if 0
    PyObject *result;

#if PYTHON_VERSION < 0x3b0
    static uint64_t dict_version = 0;
    static PyObject *cache_value = NULL;

    if (moduledict_Quartz$CoreGraphics$_metadata->ma_version_tag == dict_version) {
        CHECK_OBJECT_X(cache_value);
        result = cache_value;
    } else {
        dict_version = moduledict_Quartz$CoreGraphics$_metadata->ma_version_tag;

        result = GET_STRING_DICT_VALUE(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[11]);
        cache_value = result;
    }
#else
    static uint32_t dict_keys_version = 0xFFFFFFFF;
    static Py_ssize_t cache_dk_index = 0;

    PyDictKeysObject *dk = moduledict_Quartz$CoreGraphics$_metadata->ma_keys;
    if (likely(DK_IS_UNICODE(dk))) {

#if PYTHON_VERSION >= 0x3c0
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(tstate->interp, dk);
#else
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(dk);
#endif

        if (current_dk_version != dict_keys_version) {
            dict_keys_version = current_dk_version;
            Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[11]);
            assert(hash != -1);

            cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[11], hash);
        }

        if (cache_dk_index >= 0) {
            assert(dk->dk_kind != DICT_KEYS_SPLIT);

            PyDictUnicodeEntry *entries = DK_UNICODE_ENTRIES(dk);

            result = entries[cache_dk_index].me_value;

            if (unlikely(result == NULL)) {
                Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[11]);
                assert(hash != -1);

                cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[11], hash);

                if (cache_dk_index >= 0) {
                    result = entries[cache_dk_index].me_value;
                }
            }
        } else {
            result = NULL;
        }
    } else {
        result = GET_STRING_DICT_VALUE(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[11]);
    }
#endif

#else
    PyObject *result = GET_STRING_DICT_VALUE(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[11]);
#endif

    if (unlikely(result == NULL)) {
        result = GET_STRING_DICT_VALUE(dict_builtin, (Nuitka_StringObject *)mod_consts[11]);
    }

    return result;
}


#if !defined(_NUITKA_EXPERIMENTAL_NEW_CODE_OBJECTS)
// The module code objects.
static PyCodeObject *code_objects_fea1cd4ab9aa0d52a34f22b524b3b0a3;
static PyCodeObject *code_objects_f71a3575fe474981367cdf38e0b21d26;
static PyCodeObject *code_objects_b906b58df13f472c850fd43abfa15dc3;
static PyCodeObject *code_objects_ad914f710c854f8297f6f0619b8093ef;

static void createModuleCodeObjects(void) {
    module_filename_obj = MAKE_RELATIVE_PATH(mod_consts[1457]); CHECK_OBJECT(module_filename_obj);
    code_objects_fea1cd4ab9aa0d52a34f22b524b3b0a3 = MAKE_CODE_OBJECT(module_filename_obj, 1, 0, mod_consts[1458], mod_consts[1458], NULL, NULL, 0, 0, 0);
    code_objects_f71a3575fe474981367cdf38e0b21d26 = MAKE_CODE_OBJECT(module_filename_obj, 12, CO_OPTIMIZED | CO_NEWLOCALS, mod_consts[8], mod_consts[8], mod_consts[1459], NULL, 2, 0, 0);
    code_objects_b906b58df13f472c850fd43abfa15dc3 = MAKE_CODE_OBJECT(module_filename_obj, 23, CO_OPTIMIZED | CO_NEWLOCALS, mod_consts[11], mod_consts[11], mod_consts[1459], NULL, 2, 0, 0);
    code_objects_ad914f710c854f8297f6f0619b8093ef = MAKE_CODE_OBJECT(module_filename_obj, 28, CO_OPTIMIZED | CO_NEWLOCALS, mod_consts[11], mod_consts[11], mod_consts[1459], NULL, 2, 0, 0);
}
#endif

// The module function declarations.
static PyObject *MAKE_FUNCTION_Quartz$CoreGraphics$_metadata$$$function__1_sel32or64(PyThreadState *tstate);


static PyObject *MAKE_FUNCTION_Quartz$CoreGraphics$_metadata$$$function__3_selAorI(PyThreadState *tstate);


static PyObject *MAKE_FUNCTION_Quartz$CoreGraphics$_metadata$$$function__4_selAorI(PyThreadState *tstate);


// The module function definitions.
static PyObject *impl_Quartz$CoreGraphics$_metadata$$$function__1_sel32or64(PyThreadState *tstate, struct Nuitka_FunctionObject const *self, PyObject **python_pars) {
    // Preserve error status for checks
#ifndef __NUITKA_NO_ASSERT__
    NUITKA_MAY_BE_UNUSED bool had_error = HAS_ERROR_OCCURRED(tstate);
#endif

    // Local variable declarations.
    PyObject *par_a = python_pars[0];
    PyObject *par_b = python_pars[1];
    PyObject *tmp_return_value = NULL;

    // Actual function body.
    CHECK_OBJECT(par_b);
    tmp_return_value = par_b;
    Py_INCREF(tmp_return_value);
    goto function_return_exit;

    NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
    return NULL;


function_return_exit:
   // Function cleanup code if any.
    CHECK_OBJECT(par_a);
    Py_DECREF(par_a);
    CHECK_OBJECT(par_b);
    Py_DECREF(par_b);

   // Actual function exit with return value, making sure we did not make
   // the error status worse despite non-NULL return.
   CHECK_OBJECT(tmp_return_value);
   assert(had_error || !HAS_ERROR_OCCURRED(tstate));
   return tmp_return_value;
}


static PyObject *impl_Quartz$CoreGraphics$_metadata$$$function__3_selAorI(PyThreadState *tstate, struct Nuitka_FunctionObject const *self, PyObject **python_pars) {
    // Preserve error status for checks
#ifndef __NUITKA_NO_ASSERT__
    NUITKA_MAY_BE_UNUSED bool had_error = HAS_ERROR_OCCURRED(tstate);
#endif

    // Local variable declarations.
    PyObject *par_a = python_pars[0];
    PyObject *par_b = python_pars[1];
    PyObject *tmp_return_value = NULL;

    // Actual function body.
    CHECK_OBJECT(par_a);
    tmp_return_value = par_a;
    Py_INCREF(tmp_return_value);
    goto function_return_exit;

    NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
    return NULL;


function_return_exit:
   // Function cleanup code if any.
    CHECK_OBJECT(par_a);
    Py_DECREF(par_a);
    CHECK_OBJECT(par_b);
    Py_DECREF(par_b);

   // Actual function exit with return value, making sure we did not make
   // the error status worse despite non-NULL return.
   CHECK_OBJECT(tmp_return_value);
   assert(had_error || !HAS_ERROR_OCCURRED(tstate));
   return tmp_return_value;
}


static PyObject *impl_Quartz$CoreGraphics$_metadata$$$function__4_selAorI(PyThreadState *tstate, struct Nuitka_FunctionObject const *self, PyObject **python_pars) {
    // Preserve error status for checks
#ifndef __NUITKA_NO_ASSERT__
    NUITKA_MAY_BE_UNUSED bool had_error = HAS_ERROR_OCCURRED(tstate);
#endif

    // Local variable declarations.
    PyObject *par_a = python_pars[0];
    PyObject *par_b = python_pars[1];
    PyObject *tmp_return_value = NULL;

    // Actual function body.
    CHECK_OBJECT(par_b);
    tmp_return_value = par_b;
    Py_INCREF(tmp_return_value);
    goto function_return_exit;

    NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
    return NULL;


function_return_exit:
   // Function cleanup code if any.
    CHECK_OBJECT(par_a);
    Py_DECREF(par_a);
    CHECK_OBJECT(par_b);
    Py_DECREF(par_b);

   // Actual function exit with return value, making sure we did not make
   // the error status worse despite non-NULL return.
   CHECK_OBJECT(tmp_return_value);
   assert(had_error || !HAS_ERROR_OCCURRED(tstate));
   return tmp_return_value;
}



static PyObject *MAKE_FUNCTION_Quartz$CoreGraphics$_metadata$$$function__1_sel32or64(PyThreadState *tstate) {
    struct Nuitka_FunctionObject *result = Nuitka_Function_New(
        impl_Quartz$CoreGraphics$_metadata$$$function__1_sel32or64,
        mod_consts[8],
#if PYTHON_VERSION >= 0x300
        NULL,
#endif
        code_objects_f71a3575fe474981367cdf38e0b21d26,
        NULL,
#if PYTHON_VERSION >= 0x300
        NULL,
        NULL,
#endif
        module_Quartz$CoreGraphics$_metadata,
        NULL,
        NULL,
        0
    );


    return (PyObject *)result;
}



static PyObject *MAKE_FUNCTION_Quartz$CoreGraphics$_metadata$$$function__3_selAorI(PyThreadState *tstate) {
    struct Nuitka_FunctionObject *result = Nuitka_Function_New(
        impl_Quartz$CoreGraphics$_metadata$$$function__3_selAorI,
        mod_consts[11],
#if PYTHON_VERSION >= 0x300
        NULL,
#endif
        code_objects_b906b58df13f472c850fd43abfa15dc3,
        NULL,
#if PYTHON_VERSION >= 0x300
        NULL,
        NULL,
#endif
        module_Quartz$CoreGraphics$_metadata,
        NULL,
        NULL,
        0
    );


    return (PyObject *)result;
}



static PyObject *MAKE_FUNCTION_Quartz$CoreGraphics$_metadata$$$function__4_selAorI(PyThreadState *tstate) {
    struct Nuitka_FunctionObject *result = Nuitka_Function_New(
        impl_Quartz$CoreGraphics$_metadata$$$function__4_selAorI,
        mod_consts[11],
#if PYTHON_VERSION >= 0x300
        NULL,
#endif
        code_objects_ad914f710c854f8297f6f0619b8093ef,
        NULL,
#if PYTHON_VERSION >= 0x300
        NULL,
        NULL,
#endif
        module_Quartz$CoreGraphics$_metadata,
        NULL,
        NULL,
        0
    );


    return (PyObject *)result;
}


extern void _initCompiledCellType();
extern void _initCompiledGeneratorType();
extern void _initCompiledFunctionType();
extern void _initCompiledMethodType();
extern void _initCompiledFrameType();

extern PyTypeObject Nuitka_Loader_Type;

#ifdef _NUITKA_PLUGIN_DILL_ENABLED
// Provide a way to create find a function via its C code and create it back
// in another process, useful for multiprocessing extensions like dill
extern void registerDillPluginTables(PyThreadState *tstate, char const *module_name, PyMethodDef *reduce_compiled_function, PyMethodDef *create_compiled_function);

static function_impl_code const function_table_Quartz$CoreGraphics$_metadata[] = {
    impl_Quartz$CoreGraphics$_metadata$$$function__1_sel32or64,
    impl_Quartz$CoreGraphics$_metadata$$$function__3_selAorI,
    impl_Quartz$CoreGraphics$_metadata$$$function__4_selAorI,
    NULL
};

static PyObject *_reduce_compiled_function(PyObject *self, PyObject *args, PyObject *kwds) {
    PyObject *func;

    if (!PyArg_ParseTuple(args, "O:reduce_compiled_function", &func, NULL)) {
        return NULL;
    }

    if (Nuitka_Function_Check(func) == false) {
        PyThreadState *tstate = PyThreadState_GET();

        SET_CURRENT_EXCEPTION_TYPE0_STR(tstate, PyExc_TypeError, "not a compiled function");
        return NULL;
    }

    struct Nuitka_FunctionObject *function = (struct Nuitka_FunctionObject *)func;

    return Nuitka_Function_GetFunctionState(function, function_table_Quartz$CoreGraphics$_metadata);
}

static PyMethodDef _method_def_reduce_compiled_function = {"reduce_compiled_function", (PyCFunction)_reduce_compiled_function,
                                                           METH_VARARGS, NULL};


static PyObject *_create_compiled_function(PyObject *self, PyObject *args, PyObject *kwds) {
    CHECK_OBJECT_DEEP(args);

    PyObject *function_index;
    PyObject *code_object_desc;
    PyObject *defaults;
    PyObject *kw_defaults;
    PyObject *doc;
    PyObject *constant_return_value;
    PyObject *function_qualname;
    PyObject *closure;
    PyObject *annotations;
    PyObject *func_dict;

    if (!PyArg_ParseTuple(args, "OOOOOOOOOO:create_compiled_function", &function_index, &code_object_desc, &defaults, &kw_defaults, &doc, &constant_return_value, &function_qualname, &closure, &annotations, &func_dict, NULL)) {
        return NULL;
    }

    return (PyObject *)Nuitka_Function_CreateFunctionViaCodeIndex(
        module_Quartz$CoreGraphics$_metadata,
        function_qualname,
        function_index,
        code_object_desc,
        constant_return_value,
        defaults,
        kw_defaults,
        doc,
        closure,
        annotations,
        func_dict,
        function_table_Quartz$CoreGraphics$_metadata,
        sizeof(function_table_Quartz$CoreGraphics$_metadata) / sizeof(function_impl_code)
    );
}

static PyMethodDef _method_def_create_compiled_function = {
    "create_compiled_function",
    (PyCFunction)_create_compiled_function,
    METH_VARARGS, NULL
};


#endif

// Actual name might be different when loaded as a package.
#if _NUITKA_MODULE_MODE && 0
static char const *module_full_name = "Quartz.CoreGraphics._metadata";
#endif

// Internal entry point for module code.
PyObject *modulecode_Quartz$CoreGraphics$_metadata(PyThreadState *tstate, PyObject *module, struct Nuitka_MetaPathBasedLoaderEntry const *loader_entry) {
    // Report entry to PGO.
    PGO_onModuleEntered("Quartz$CoreGraphics$_metadata");

    // Store the module for future use.
    module_Quartz$CoreGraphics$_metadata = module;

    moduledict_Quartz$CoreGraphics$_metadata = MODULE_DICT(module_Quartz$CoreGraphics$_metadata);

    // Modules can be loaded again in case of errors, avoid the init being done again.
    static bool init_done = false;

    if (init_done == false) {
#if _NUITKA_MODULE_MODE && 0
        // In case of an extension module loaded into a process, we need to call
        // initialization here because that's the first and potentially only time
        // we are going called.
#if PYTHON_VERSION > 0x350 && !defined(_NUITKA_EXPERIMENTAL_DISABLE_ALLOCATORS)
        initNuitkaAllocators();
#endif
        // Initialize the constant values used.
        _initBuiltinModule();

        PyObject *real_module_name = PyObject_GetAttrString(module, "__name__");
        CHECK_OBJECT(real_module_name);
        module_full_name = strdup(Nuitka_String_AsString(real_module_name));

        createGlobalConstants(tstate, real_module_name);

        /* Initialize the compiled types of Nuitka. */
        _initCompiledCellType();
        _initCompiledGeneratorType();
        _initCompiledFunctionType();
        _initCompiledMethodType();
        _initCompiledFrameType();

        _initSlotCompare();
#if PYTHON_VERSION >= 0x270
        _initSlotIterNext();
#endif

        patchTypeComparison();

        // Enable meta path based loader if not already done.
#ifdef _NUITKA_TRACE
        PRINT_STRING("Quartz$CoreGraphics$_metadata: Calling setupMetaPathBasedLoader().\n");
#endif
        setupMetaPathBasedLoader(tstate);
#if 0 >= 0
#ifdef _NUITKA_TRACE
        PRINT_STRING("Quartz$CoreGraphics$_metadata: Calling updateMetaPathBasedLoaderModuleRoot().\n");
#endif
        updateMetaPathBasedLoaderModuleRoot(module_full_name);
#endif


#if PYTHON_VERSION >= 0x300
        patchInspectModule(tstate);
#endif

#endif

        /* The constants only used by this module are created now. */
        NUITKA_PRINT_TRACE("Quartz$CoreGraphics$_metadata: Calling createModuleConstants().\n");
        createModuleConstants(tstate);

#if !defined(_NUITKA_EXPERIMENTAL_NEW_CODE_OBJECTS)
        createModuleCodeObjects();
#endif
        init_done = true;
    }

#if _NUITKA_MODULE_MODE && 0
    PyObject *pre_load = IMPORT_EMBEDDED_MODULE(tstate, "Quartz.CoreGraphics._metadata" "-preLoad");
    if (pre_load == NULL) {
        return NULL;
    }
#endif

    // PRINT_STRING("in initQuartz$CoreGraphics$_metadata\n");

#ifdef _NUITKA_PLUGIN_DILL_ENABLED
    {
        char const *module_name_c;
        if (loader_entry != NULL) {
            module_name_c = loader_entry->name;
        } else {
            PyObject *module_name = GET_STRING_DICT_VALUE(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)const_str_plain___name__);
            module_name_c = Nuitka_String_AsString(module_name);
        }

        registerDillPluginTables(tstate, module_name_c, &_method_def_reduce_compiled_function, &_method_def_create_compiled_function);
    }
#endif

    // Set "__compiled__" to what version information we have.
    UPDATE_STRING_DICT0(
        moduledict_Quartz$CoreGraphics$_metadata,
        (Nuitka_StringObject *)const_str_plain___compiled__,
        Nuitka_dunder_compiled_value
    );

    // Update "__package__" value to what it ought to be.
    {
#if 0
        UPDATE_STRING_DICT0(
            moduledict_Quartz$CoreGraphics$_metadata,
            (Nuitka_StringObject *)const_str_plain___package__,
            mod_consts[289]
        );
#elif 0
        PyObject *module_name = GET_STRING_DICT_VALUE(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)const_str_plain___name__);

        UPDATE_STRING_DICT0(
            moduledict_Quartz$CoreGraphics$_metadata,
            (Nuitka_StringObject *)const_str_plain___package__,
            module_name
        );
#else

#if PYTHON_VERSION < 0x300
        PyObject *module_name = GET_STRING_DICT_VALUE(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)const_str_plain___name__);
        char const *module_name_cstr = PyString_AS_STRING(module_name);

        char const *last_dot = strrchr(module_name_cstr, '.');

        if (last_dot != NULL) {
            UPDATE_STRING_DICT1(
                moduledict_Quartz$CoreGraphics$_metadata,
                (Nuitka_StringObject *)const_str_plain___package__,
                PyString_FromStringAndSize(module_name_cstr, last_dot - module_name_cstr)
            );
        }
#else
        PyObject *module_name = GET_STRING_DICT_VALUE(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)const_str_plain___name__);
        Py_ssize_t dot_index = PyUnicode_Find(module_name, const_str_dot, 0, PyUnicode_GetLength(module_name), -1);

        if (dot_index != -1) {
            UPDATE_STRING_DICT1(
                moduledict_Quartz$CoreGraphics$_metadata,
                (Nuitka_StringObject *)const_str_plain___package__,
                PyUnicode_Substring(module_name, 0, dot_index)
            );
        }
#endif
#endif
    }

    CHECK_OBJECT(module_Quartz$CoreGraphics$_metadata);

    // For deep importing of a module we need to have "__builtins__", so we set
    // it ourselves in the same way than CPython does. Note: This must be done
    // before the frame object is allocated, or else it may fail.

    if (GET_STRING_DICT_VALUE(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)const_str_plain___builtins__) == NULL) {
        PyObject *value = (PyObject *)builtin_module;

        // Check if main module, not a dict then but the module itself.
#if _NUITKA_MODULE_MODE || !0
        value = PyModule_GetDict(value);
#endif

        UPDATE_STRING_DICT0(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)const_str_plain___builtins__, value);
    }

    PyObject *module_loader = Nuitka_Loader_New(loader_entry);
    UPDATE_STRING_DICT0(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)const_str_plain___loader__, module_loader);

#if PYTHON_VERSION >= 0x300
// Set the "__spec__" value

#if 0
    // Main modules just get "None" as spec.
    UPDATE_STRING_DICT0(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)const_str_plain___spec__, Py_None);
#else
    // Other modules get a "ModuleSpec" from the standard mechanism.
    {
        PyObject *bootstrap_module = getImportLibBootstrapModule();
        CHECK_OBJECT(bootstrap_module);

        PyObject *_spec_from_module = PyObject_GetAttrString(bootstrap_module, "_spec_from_module");
        CHECK_OBJECT(_spec_from_module);

        PyObject *spec_value = CALL_FUNCTION_WITH_SINGLE_ARG(tstate, _spec_from_module, module_Quartz$CoreGraphics$_metadata);
        Py_DECREF(_spec_from_module);

        // We can assume this to never fail, or else we are in trouble anyway.
        // CHECK_OBJECT(spec_value);

        if (spec_value == NULL) {
            PyErr_PrintEx(0);
            abort();
        }

        // Mark the execution in the "__spec__" value.
        SET_ATTRIBUTE(tstate, spec_value, const_str_plain__initializing, Py_True);

#if _NUITKA_MODULE_MODE && 0 && 0 >= 0
        // Set our loader object in the "__spec__" value.
        SET_ATTRIBUTE(tstate, spec_value, const_str_plain_loader, module_loader);
#endif

        UPDATE_STRING_DICT1(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)const_str_plain___spec__, spec_value);
    }
#endif
#endif

    // Temp variables if any
    struct Nuitka_FrameObject *frame_frame_Quartz$CoreGraphics$_metadata;
    NUITKA_MAY_BE_UNUSED char const *type_description_1 = NULL;
    bool tmp_result;
    struct Nuitka_ExceptionPreservationItem exception_state = Empty_Nuitka_ExceptionPreservationItem;
    NUITKA_MAY_BE_UNUSED int exception_lineno = 0;
    NUITKA_MAY_BE_UNUSED nuitka_void tmp_unused;
    int tmp_res;

    // Module init code if any


    // Module code.
    {
        PyObject *tmp_assign_source_1;
        tmp_assign_source_1 = Py_None;
        UPDATE_STRING_DICT0(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[0], tmp_assign_source_1);
    }
    {
        PyObject *tmp_assign_source_2;
        tmp_assign_source_2 = module_filename_obj;
        UPDATE_STRING_DICT0(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[1], tmp_assign_source_2);
    }
    frame_frame_Quartz$CoreGraphics$_metadata = MAKE_MODULE_FRAME(code_objects_fea1cd4ab9aa0d52a34f22b524b3b0a3, module_Quartz$CoreGraphics$_metadata);

    // Push the new frame as the currently active one, and we should be exclusively
    // owning it.
    pushFrameStackCompiledFrame(tstate, frame_frame_Quartz$CoreGraphics$_metadata);
    assert(Py_REFCNT(frame_frame_Quartz$CoreGraphics$_metadata) == 2);

    // Framed code:
    {
        PyObject *tmp_assattr_value_1;
        PyObject *tmp_assattr_target_1;
        tmp_assattr_value_1 = module_filename_obj;
        tmp_assattr_target_1 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$__spec__(tstate);
        assert(!(tmp_assattr_target_1 == NULL));
        tmp_result = SET_ATTRIBUTE(tstate, tmp_assattr_target_1, mod_consts[2], tmp_assattr_value_1);
        if (tmp_result == false) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 1;

            goto frame_exception_exit_1;
        }
    }
    {
        PyObject *tmp_assattr_value_2;
        PyObject *tmp_assattr_target_2;
        tmp_assattr_value_2 = Py_True;
        tmp_assattr_target_2 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$__spec__(tstate);
        assert(!(tmp_assattr_target_2 == NULL));
        tmp_result = SET_ATTRIBUTE(tstate, tmp_assattr_target_2, mod_consts[3], tmp_assattr_value_2);
        if (tmp_result == false) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 1;

            goto frame_exception_exit_1;
        }
    }
    {
        PyObject *tmp_assign_source_3;
        tmp_assign_source_3 = Py_None;
        UPDATE_STRING_DICT0(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[4], tmp_assign_source_3);
    }
    {
        PyObject *tmp_assign_source_4;
        PyObject *tmp_name_value_1;
        PyObject *tmp_globals_arg_value_1;
        PyObject *tmp_locals_arg_value_1;
        PyObject *tmp_fromlist_value_1;
        PyObject *tmp_level_value_1;
        tmp_name_value_1 = mod_consts[5];
        tmp_globals_arg_value_1 = (PyObject *)moduledict_Quartz$CoreGraphics$_metadata;
        tmp_locals_arg_value_1 = Py_None;
        tmp_fromlist_value_1 = Py_None;
        tmp_level_value_1 = const_int_0;
        frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 7;
        tmp_assign_source_4 = IMPORT_MODULE5(tstate, tmp_name_value_1, tmp_globals_arg_value_1, tmp_locals_arg_value_1, tmp_fromlist_value_1, tmp_level_value_1);
        if (tmp_assign_source_4 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 7;

            goto frame_exception_exit_1;
        }
        UPDATE_STRING_DICT1(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[5], tmp_assign_source_4);
    }
    {
        PyObject *tmp_assign_source_5;
        tmp_assign_source_5 = IMPORT_HARD_SYS();
        assert(!(tmp_assign_source_5 == NULL));
        UPDATE_STRING_DICT0(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[6], tmp_assign_source_5);
    }
    {
        PyObject *tmp_assign_source_6;
        PyObject *tmp_import_name_from_1;
        tmp_import_name_from_1 = IMPORT_HARD_TYPING();
        assert(!(tmp_import_name_from_1 == NULL));
        if (PyModule_Check(tmp_import_name_from_1)) {
            tmp_assign_source_6 = IMPORT_NAME_OR_MODULE(
                tstate,
                tmp_import_name_from_1,
                (PyObject *)moduledict_Quartz$CoreGraphics$_metadata,
                mod_consts[7],
                const_int_0
            );
        } else {
            tmp_assign_source_6 = IMPORT_NAME_FROM_MODULE(tstate, tmp_import_name_from_1, mod_consts[7]);
        }

        if (tmp_assign_source_6 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 8;

            goto frame_exception_exit_1;
        }
        UPDATE_STRING_DICT1(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[7], tmp_assign_source_6);
    }
    {
        PyObject *tmp_assign_source_7;


        tmp_assign_source_7 = MAKE_FUNCTION_Quartz$CoreGraphics$_metadata$$$function__1_sel32or64(tstate);

        UPDATE_STRING_DICT1(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[8], tmp_assign_source_7);
    }
    {
        nuitka_bool tmp_condition_result_1;
        PyObject *tmp_cmp_expr_left_1;
        PyObject *tmp_cmp_expr_right_1;
        PyObject *tmp_expression_value_1;
        tmp_expression_value_1 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
        assert(!(tmp_expression_value_1 == NULL));
        tmp_cmp_expr_left_1 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_1, mod_consts[9]);
        if (tmp_cmp_expr_left_1 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 21;

            goto frame_exception_exit_1;
        }
        tmp_cmp_expr_right_1 = mod_consts[10];
        tmp_condition_result_1 = RICH_COMPARE_EQ_NBOOL_OBJECT_UNICODE(tmp_cmp_expr_left_1, tmp_cmp_expr_right_1);
        Py_DECREF(tmp_cmp_expr_left_1);
        if (tmp_condition_result_1 == NUITKA_BOOL_EXCEPTION) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 21;

            goto frame_exception_exit_1;
        }
        if (tmp_condition_result_1 == NUITKA_BOOL_TRUE) {
            goto branch_yes_1;
        } else {
            goto branch_no_1;
        }
    }
    branch_yes_1:;
    {
        PyObject *tmp_assign_source_8;


        tmp_assign_source_8 = MAKE_FUNCTION_Quartz$CoreGraphics$_metadata$$$function__3_selAorI(tstate);

        UPDATE_STRING_DICT1(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[11], tmp_assign_source_8);
    }
    goto branch_end_1;
    branch_no_1:;
    {
        PyObject *tmp_assign_source_9;


        tmp_assign_source_9 = MAKE_FUNCTION_Quartz$CoreGraphics$_metadata$$$function__4_selAorI(tstate);

        UPDATE_STRING_DICT1(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[11], tmp_assign_source_9);
    }
    branch_end_1:;
    {
        PyObject *tmp_assign_source_10;
        tmp_assign_source_10 = MAKE_DICT_EMPTY(tstate);
        UPDATE_STRING_DICT1(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[12], tmp_assign_source_10);
    }
    {
        PyObject *tmp_dict_arg_value_1;
        PyObject *tmp_iterable_value_1;
        PyObject *tmp_dict_key_1;
        PyObject *tmp_dict_value_1;
        PyObject *tmp_called_instance_1;
        PyObject *tmp_call_arg_element_1;
        PyObject *tmp_call_arg_element_2;
        PyObject *tmp_call_arg_element_3;
        tmp_dict_arg_value_1 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$misc(tstate);
        assert(!(tmp_dict_arg_value_1 == NULL));
        tmp_dict_key_1 = mod_consts[13];
        tmp_called_instance_1 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
        if (unlikely(tmp_called_instance_1 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
        }

        if (tmp_called_instance_1 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 35;

            goto frame_exception_exit_1;
        }
        tmp_call_arg_element_1 = mod_consts[15];
        tmp_call_arg_element_2 = mod_consts[16];
        tmp_call_arg_element_3 = MAKE_LIST_EMPTY(tstate, 0);
        frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 35;
        {
            PyObject *call_args[] = {tmp_call_arg_element_1, tmp_call_arg_element_2, tmp_call_arg_element_3};
            tmp_dict_value_1 = CALL_METHOD_WITH_ARGS3(
                tstate,
                tmp_called_instance_1,
                mod_consts[14],
                call_args
            );
        }

        Py_DECREF(tmp_call_arg_element_3);
        if (tmp_dict_value_1 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 35;

            goto frame_exception_exit_1;
        }
        tmp_iterable_value_1 = _PyDict_NewPresized( 14 );
        {
            PyObject *tmp_called_instance_2;
            PyObject *tmp_call_arg_element_4;
            PyObject *tmp_call_arg_element_5;
            PyObject *tmp_call_arg_element_6;
            PyObject *tmp_called_instance_3;
            PyObject *tmp_call_arg_element_7;
            PyObject *tmp_call_arg_element_8;
            PyObject *tmp_call_arg_element_9;
            PyObject *tmp_called_instance_4;
            PyObject *tmp_call_arg_element_10;
            PyObject *tmp_call_arg_element_11;
            PyObject *tmp_call_arg_element_12;
            PyObject *tmp_called_instance_5;
            PyObject *tmp_call_arg_element_13;
            PyObject *tmp_call_arg_element_14;
            PyObject *tmp_call_arg_element_15;
            PyObject *tmp_called_instance_6;
            PyObject *tmp_call_arg_element_16;
            PyObject *tmp_call_arg_element_17;
            PyObject *tmp_call_arg_element_18;
            PyObject *tmp_called_instance_7;
            PyObject *tmp_call_arg_element_19;
            PyObject *tmp_call_arg_element_20;
            PyObject *tmp_call_arg_element_21;
            PyObject *tmp_called_instance_8;
            PyObject *tmp_call_arg_element_22;
            PyObject *tmp_call_arg_element_23;
            PyObject *tmp_call_arg_element_24;
            PyObject *tmp_called_instance_9;
            PyObject *tmp_call_arg_element_25;
            PyObject *tmp_call_arg_element_26;
            PyObject *tmp_call_arg_element_27;
            PyObject *tmp_called_instance_10;
            PyObject *tmp_call_arg_element_28;
            PyObject *tmp_call_arg_element_29;
            PyObject *tmp_call_arg_element_30;
            PyObject *tmp_called_instance_11;
            PyObject *tmp_call_arg_element_31;
            PyObject *tmp_call_arg_element_32;
            PyObject *tmp_call_arg_element_33;
            PyObject *tmp_called_instance_12;
            PyObject *tmp_call_arg_element_34;
            PyObject *tmp_call_arg_element_35;
            PyObject *tmp_call_arg_element_36;
            PyObject *tmp_called_instance_13;
            PyObject *tmp_call_arg_element_37;
            PyObject *tmp_call_arg_element_38;
            PyObject *tmp_call_arg_element_39;
            PyObject *tmp_called_instance_14;
            PyObject *tmp_call_arg_element_40;
            PyObject *tmp_call_arg_element_41;
            PyObject *tmp_call_arg_element_42;
            tmp_res = PyDict_SetItem(tmp_iterable_value_1, tmp_dict_key_1, tmp_dict_value_1);
            Py_DECREF(tmp_dict_value_1);
            assert(!(tmp_res != 0));
            tmp_dict_key_1 = mod_consts[17];
            tmp_called_instance_2 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
            if (unlikely(tmp_called_instance_2 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_called_instance_2 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 40;

                goto dict_build_exception_1;
            }
            tmp_call_arg_element_4 = mod_consts[18];
            tmp_call_arg_element_5 = mod_consts[19];
            tmp_call_arg_element_6 = MAKE_LIST3(tstate, mod_consts[20],mod_consts[21],mod_consts[22]);
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 40;
            {
                PyObject *call_args[] = {tmp_call_arg_element_4, tmp_call_arg_element_5, tmp_call_arg_element_6};
                tmp_dict_value_1 = CALL_METHOD_WITH_ARGS3(
                    tstate,
                    tmp_called_instance_2,
                    mod_consts[14],
                    call_args
                );
            }

            Py_DECREF(tmp_call_arg_element_6);
            if (tmp_dict_value_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 40;

                goto dict_build_exception_1;
            }
            tmp_res = PyDict_SetItem(tmp_iterable_value_1, tmp_dict_key_1, tmp_dict_value_1);
            Py_DECREF(tmp_dict_value_1);
            assert(!(tmp_res != 0));
            tmp_dict_key_1 = mod_consts[23];
            tmp_called_instance_3 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
            if (unlikely(tmp_called_instance_3 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_called_instance_3 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 45;

                goto dict_build_exception_1;
            }
            tmp_call_arg_element_7 = mod_consts[24];
            tmp_call_arg_element_8 = mod_consts[25];
            tmp_call_arg_element_9 = MAKE_LIST_EMPTY(tstate, 0);
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 45;
            {
                PyObject *call_args[] = {tmp_call_arg_element_7, tmp_call_arg_element_8, tmp_call_arg_element_9};
                tmp_dict_value_1 = CALL_METHOD_WITH_ARGS3(
                    tstate,
                    tmp_called_instance_3,
                    mod_consts[14],
                    call_args
                );
            }

            Py_DECREF(tmp_call_arg_element_9);
            if (tmp_dict_value_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 45;

                goto dict_build_exception_1;
            }
            tmp_res = PyDict_SetItem(tmp_iterable_value_1, tmp_dict_key_1, tmp_dict_value_1);
            Py_DECREF(tmp_dict_value_1);
            assert(!(tmp_res != 0));
            tmp_dict_key_1 = mod_consts[26];
            tmp_called_instance_4 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
            if (unlikely(tmp_called_instance_4 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_called_instance_4 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 50;

                goto dict_build_exception_1;
            }
            tmp_call_arg_element_10 = mod_consts[27];
            tmp_call_arg_element_11 = mod_consts[28];
            tmp_call_arg_element_12 = MAKE_LIST5(tstate, mod_consts[29]);
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 50;
            {
                PyObject *call_args[] = {tmp_call_arg_element_10, tmp_call_arg_element_11, tmp_call_arg_element_12};
                tmp_dict_value_1 = CALL_METHOD_WITH_ARGS3(
                    tstate,
                    tmp_called_instance_4,
                    mod_consts[14],
                    call_args
                );
            }

            Py_DECREF(tmp_call_arg_element_12);
            if (tmp_dict_value_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 50;

                goto dict_build_exception_1;
            }
            tmp_res = PyDict_SetItem(tmp_iterable_value_1, tmp_dict_key_1, tmp_dict_value_1);
            Py_DECREF(tmp_dict_value_1);
            assert(!(tmp_res != 0));
            tmp_dict_key_1 = mod_consts[30];
            tmp_called_instance_5 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
            if (unlikely(tmp_called_instance_5 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_called_instance_5 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 61;

                goto dict_build_exception_1;
            }
            tmp_call_arg_element_13 = mod_consts[31];
            tmp_call_arg_element_14 = mod_consts[32];
            tmp_call_arg_element_15 = MAKE_LIST_EMPTY(tstate, 0);
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 61;
            {
                PyObject *call_args[] = {tmp_call_arg_element_13, tmp_call_arg_element_14, tmp_call_arg_element_15};
                tmp_dict_value_1 = CALL_METHOD_WITH_ARGS3(
                    tstate,
                    tmp_called_instance_5,
                    mod_consts[14],
                    call_args
                );
            }

            Py_DECREF(tmp_call_arg_element_15);
            if (tmp_dict_value_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 61;

                goto dict_build_exception_1;
            }
            tmp_res = PyDict_SetItem(tmp_iterable_value_1, tmp_dict_key_1, tmp_dict_value_1);
            Py_DECREF(tmp_dict_value_1);
            assert(!(tmp_res != 0));
            tmp_dict_key_1 = mod_consts[33];
            tmp_called_instance_6 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
            if (unlikely(tmp_called_instance_6 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_called_instance_6 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 64;

                goto dict_build_exception_1;
            }
            tmp_call_arg_element_16 = mod_consts[34];
            tmp_call_arg_element_17 = mod_consts[35];
            tmp_call_arg_element_18 = MAKE_LIST2(tstate, mod_consts[36],mod_consts[37]);
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 64;
            {
                PyObject *call_args[] = {tmp_call_arg_element_16, tmp_call_arg_element_17, tmp_call_arg_element_18};
                tmp_dict_value_1 = CALL_METHOD_WITH_ARGS3(
                    tstate,
                    tmp_called_instance_6,
                    mod_consts[14],
                    call_args
                );
            }

            Py_DECREF(tmp_call_arg_element_18);
            if (tmp_dict_value_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 64;

                goto dict_build_exception_1;
            }
            tmp_res = PyDict_SetItem(tmp_iterable_value_1, tmp_dict_key_1, tmp_dict_value_1);
            Py_DECREF(tmp_dict_value_1);
            assert(!(tmp_res != 0));
            tmp_dict_key_1 = mod_consts[38];
            tmp_called_instance_7 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
            if (unlikely(tmp_called_instance_7 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_called_instance_7 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 69;

                goto dict_build_exception_1;
            }
            tmp_call_arg_element_19 = mod_consts[39];
            tmp_call_arg_element_20 = mod_consts[40];
            tmp_call_arg_element_21 = MAKE_LIST3(tstate, mod_consts[20],mod_consts[21],mod_consts[22]);
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 69;
            {
                PyObject *call_args[] = {tmp_call_arg_element_19, tmp_call_arg_element_20, tmp_call_arg_element_21};
                tmp_dict_value_1 = CALL_METHOD_WITH_ARGS3(
                    tstate,
                    tmp_called_instance_7,
                    mod_consts[14],
                    call_args
                );
            }

            Py_DECREF(tmp_call_arg_element_21);
            if (tmp_dict_value_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 69;

                goto dict_build_exception_1;
            }
            tmp_res = PyDict_SetItem(tmp_iterable_value_1, tmp_dict_key_1, tmp_dict_value_1);
            Py_DECREF(tmp_dict_value_1);
            assert(!(tmp_res != 0));
            tmp_dict_key_1 = mod_consts[41];
            tmp_called_instance_8 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
            if (unlikely(tmp_called_instance_8 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_called_instance_8 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 74;

                goto dict_build_exception_1;
            }
            tmp_call_arg_element_22 = mod_consts[42];
            tmp_call_arg_element_23 = mod_consts[43];
            tmp_call_arg_element_24 = MAKE_LIST7(tstate, mod_consts[44]);
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 74;
            {
                PyObject *call_args[] = {tmp_call_arg_element_22, tmp_call_arg_element_23, tmp_call_arg_element_24};
                tmp_dict_value_1 = CALL_METHOD_WITH_ARGS3(
                    tstate,
                    tmp_called_instance_8,
                    mod_consts[14],
                    call_args
                );
            }

            Py_DECREF(tmp_call_arg_element_24);
            if (tmp_dict_value_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 74;

                goto dict_build_exception_1;
            }
            tmp_res = PyDict_SetItem(tmp_iterable_value_1, tmp_dict_key_1, tmp_dict_value_1);
            Py_DECREF(tmp_dict_value_1);
            assert(!(tmp_res != 0));
            tmp_dict_key_1 = mod_consts[45];
            tmp_called_instance_9 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
            if (unlikely(tmp_called_instance_9 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_called_instance_9 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 87;

                goto dict_build_exception_1;
            }
            tmp_call_arg_element_25 = mod_consts[46];
            tmp_call_arg_element_26 = mod_consts[47];
            tmp_call_arg_element_27 = MAKE_LIST_EMPTY(tstate, 0);
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 87;
            {
                PyObject *call_args[] = {tmp_call_arg_element_25, tmp_call_arg_element_26, tmp_call_arg_element_27};
                tmp_dict_value_1 = CALL_METHOD_WITH_ARGS3(
                    tstate,
                    tmp_called_instance_9,
                    mod_consts[14],
                    call_args
                );
            }

            Py_DECREF(tmp_call_arg_element_27);
            if (tmp_dict_value_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 87;

                goto dict_build_exception_1;
            }
            tmp_res = PyDict_SetItem(tmp_iterable_value_1, tmp_dict_key_1, tmp_dict_value_1);
            Py_DECREF(tmp_dict_value_1);
            assert(!(tmp_res != 0));
            tmp_dict_key_1 = mod_consts[48];
            tmp_called_instance_10 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
            if (unlikely(tmp_called_instance_10 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_called_instance_10 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 92;

                goto dict_build_exception_1;
            }
            tmp_call_arg_element_28 = mod_consts[49];
            tmp_call_arg_element_29 = mod_consts[50];
            tmp_call_arg_element_30 = MAKE_LIST_EMPTY(tstate, 0);
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 92;
            {
                PyObject *call_args[] = {tmp_call_arg_element_28, tmp_call_arg_element_29, tmp_call_arg_element_30};
                tmp_dict_value_1 = CALL_METHOD_WITH_ARGS3(
                    tstate,
                    tmp_called_instance_10,
                    mod_consts[14],
                    call_args
                );
            }

            Py_DECREF(tmp_call_arg_element_30);
            if (tmp_dict_value_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 92;

                goto dict_build_exception_1;
            }
            tmp_res = PyDict_SetItem(tmp_iterable_value_1, tmp_dict_key_1, tmp_dict_value_1);
            Py_DECREF(tmp_dict_value_1);
            assert(!(tmp_res != 0));
            tmp_dict_key_1 = mod_consts[51];
            tmp_called_instance_11 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
            if (unlikely(tmp_called_instance_11 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_called_instance_11 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 97;

                goto dict_build_exception_1;
            }
            tmp_call_arg_element_31 = mod_consts[52];
            tmp_call_arg_element_32 = mod_consts[53];
            tmp_call_arg_element_33 = MAKE_LIST2(tstate, mod_consts[54],mod_consts[55]);
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 97;
            {
                PyObject *call_args[] = {tmp_call_arg_element_31, tmp_call_arg_element_32, tmp_call_arg_element_33};
                tmp_dict_value_1 = CALL_METHOD_WITH_ARGS3(
                    tstate,
                    tmp_called_instance_11,
                    mod_consts[14],
                    call_args
                );
            }

            Py_DECREF(tmp_call_arg_element_33);
            if (tmp_dict_value_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 97;

                goto dict_build_exception_1;
            }
            tmp_res = PyDict_SetItem(tmp_iterable_value_1, tmp_dict_key_1, tmp_dict_value_1);
            Py_DECREF(tmp_dict_value_1);
            assert(!(tmp_res != 0));
            tmp_dict_key_1 = mod_consts[56];
            tmp_called_instance_12 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
            if (unlikely(tmp_called_instance_12 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_called_instance_12 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 102;

                goto dict_build_exception_1;
            }
            tmp_call_arg_element_34 = mod_consts[57];
            tmp_call_arg_element_35 = mod_consts[58];
            tmp_call_arg_element_36 = MAKE_LIST_EMPTY(tstate, 0);
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 102;
            {
                PyObject *call_args[] = {tmp_call_arg_element_34, tmp_call_arg_element_35, tmp_call_arg_element_36};
                tmp_dict_value_1 = CALL_METHOD_WITH_ARGS3(
                    tstate,
                    tmp_called_instance_12,
                    mod_consts[14],
                    call_args
                );
            }

            Py_DECREF(tmp_call_arg_element_36);
            if (tmp_dict_value_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 102;

                goto dict_build_exception_1;
            }
            tmp_res = PyDict_SetItem(tmp_iterable_value_1, tmp_dict_key_1, tmp_dict_value_1);
            Py_DECREF(tmp_dict_value_1);
            assert(!(tmp_res != 0));
            tmp_dict_key_1 = mod_consts[59];
            tmp_called_instance_13 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
            if (unlikely(tmp_called_instance_13 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_called_instance_13 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 107;

                goto dict_build_exception_1;
            }
            tmp_call_arg_element_37 = mod_consts[60];
            tmp_call_arg_element_38 = mod_consts[61];
            tmp_call_arg_element_39 = MAKE_LIST10(tstate, mod_consts[62]);
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 107;
            {
                PyObject *call_args[] = {tmp_call_arg_element_37, tmp_call_arg_element_38, tmp_call_arg_element_39};
                tmp_dict_value_1 = CALL_METHOD_WITH_ARGS3(
                    tstate,
                    tmp_called_instance_13,
                    mod_consts[14],
                    call_args
                );
            }

            Py_DECREF(tmp_call_arg_element_39);
            if (tmp_dict_value_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 107;

                goto dict_build_exception_1;
            }
            tmp_res = PyDict_SetItem(tmp_iterable_value_1, tmp_dict_key_1, tmp_dict_value_1);
            Py_DECREF(tmp_dict_value_1);
            assert(!(tmp_res != 0));
            tmp_dict_key_1 = mod_consts[63];
            tmp_called_instance_14 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
            if (unlikely(tmp_called_instance_14 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_called_instance_14 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 123;

                goto dict_build_exception_1;
            }
            tmp_call_arg_element_40 = mod_consts[64];
            tmp_call_arg_element_41 = mod_consts[65];
            tmp_call_arg_element_42 = MAKE_LIST_EMPTY(tstate, 0);
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 123;
            {
                PyObject *call_args[] = {tmp_call_arg_element_40, tmp_call_arg_element_41, tmp_call_arg_element_42};
                tmp_dict_value_1 = CALL_METHOD_WITH_ARGS3(
                    tstate,
                    tmp_called_instance_14,
                    mod_consts[14],
                    call_args
                );
            }

            Py_DECREF(tmp_call_arg_element_42);
            if (tmp_dict_value_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 123;

                goto dict_build_exception_1;
            }
            tmp_res = PyDict_SetItem(tmp_iterable_value_1, tmp_dict_key_1, tmp_dict_value_1);
            Py_DECREF(tmp_dict_value_1);
            assert(!(tmp_res != 0));
        }
        goto dict_build_noexception_1;
        // Exception handling pass through code for dict_build:
        dict_build_exception_1:;
        Py_DECREF(tmp_iterable_value_1);
        goto frame_exception_exit_1;
        // Finished with no exception for dict_build:
        dict_build_noexception_1:;
        assert(PyDict_Check(tmp_dict_arg_value_1));
            tmp_res = PyDict_Merge(tmp_dict_arg_value_1, tmp_iterable_value_1, 1);

        Py_DECREF(tmp_iterable_value_1);
        if (tmp_res != 0) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 34;

            goto frame_exception_exit_1;
        }
    }
    {
        PyObject *tmp_assign_source_11;
        tmp_assign_source_11 = mod_consts[66];
        UPDATE_STRING_DICT0(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[67], tmp_assign_source_11);
    }
    {
        PyObject *tmp_assign_source_12;
        tmp_assign_source_12 = mod_consts[68];
        UPDATE_STRING_DICT0(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[69], tmp_assign_source_12);
    }
    {
        PyObject *tmp_called_value_1;
        PyObject *tmp_expression_value_2;
        PyObject *tmp_call_result_1;
        PyObject *tmp_args_element_value_1;
        PyObject *tmp_dict_key_2;
        PyObject *tmp_dict_value_2;
        PyObject *tmp_called_value_2;
        tmp_expression_value_2 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$misc(tstate);
        if (unlikely(tmp_expression_value_2 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[12]);
        }

        if (tmp_expression_value_2 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 132;

            goto frame_exception_exit_1;
        }
        tmp_called_value_1 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_2, mod_consts[70]);
        if (tmp_called_value_1 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 132;

            goto frame_exception_exit_1;
        }
        tmp_dict_key_2 = mod_consts[71];
        tmp_called_value_2 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
        if (unlikely(tmp_called_value_2 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
        }

        if (tmp_called_value_2 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));

            Py_DECREF(tmp_called_value_1);

            exception_lineno = 134;

            goto frame_exception_exit_1;
        }
        frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 134;
        tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_2, mod_consts[72]);

        if (tmp_dict_value_2 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);
            Py_DECREF(tmp_called_value_1);

            exception_lineno = 134;

            goto frame_exception_exit_1;
        }
        tmp_args_element_value_1 = _PyDict_NewPresized( 54 );
        {
            PyObject *tmp_called_value_3;
            PyObject *tmp_called_value_4;
            PyObject *tmp_called_value_5;
            PyObject *tmp_called_value_6;
            PyObject *tmp_called_value_7;
            PyObject *tmp_called_value_8;
            PyObject *tmp_called_value_9;
            PyObject *tmp_called_value_10;
            PyObject *tmp_called_value_11;
            PyObject *tmp_called_value_12;
            PyObject *tmp_called_value_13;
            PyObject *tmp_called_value_14;
            PyObject *tmp_called_value_15;
            PyObject *tmp_called_value_16;
            PyObject *tmp_called_value_17;
            PyObject *tmp_called_value_18;
            PyObject *tmp_called_value_19;
            PyObject *tmp_called_value_20;
            PyObject *tmp_called_value_21;
            PyObject *tmp_called_value_22;
            PyObject *tmp_called_value_23;
            PyObject *tmp_called_value_24;
            PyObject *tmp_called_value_25;
            PyObject *tmp_called_value_26;
            PyObject *tmp_called_value_27;
            PyObject *tmp_called_value_28;
            PyObject *tmp_called_value_29;
            PyObject *tmp_called_value_30;
            PyObject *tmp_called_value_31;
            PyObject *tmp_called_value_32;
            PyObject *tmp_called_value_33;
            PyObject *tmp_called_value_34;
            PyObject *tmp_called_value_35;
            PyObject *tmp_called_value_36;
            PyObject *tmp_called_value_37;
            PyObject *tmp_called_value_38;
            PyObject *tmp_called_value_39;
            PyObject *tmp_called_value_40;
            PyObject *tmp_called_value_41;
            PyObject *tmp_called_value_42;
            PyObject *tmp_called_value_43;
            PyObject *tmp_called_value_44;
            PyObject *tmp_called_value_45;
            PyObject *tmp_called_value_46;
            PyObject *tmp_called_value_47;
            PyObject *tmp_called_value_48;
            PyObject *tmp_called_value_49;
            PyObject *tmp_called_value_50;
            PyObject *tmp_called_value_51;
            PyObject *tmp_called_value_52;
            PyObject *tmp_called_value_53;
            PyObject *tmp_called_value_54;
            PyObject *tmp_called_value_55;
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[73];
            tmp_called_value_3 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_3 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_3 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 135;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 135;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_3, mod_consts[74]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 135;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[75];
            tmp_called_value_4 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_4 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_4 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 136;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 136;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_4, mod_consts[76]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 136;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[77];
            tmp_called_value_5 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_5 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_5 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 137;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 137;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_5, mod_consts[78]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 137;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[79];
            tmp_called_value_6 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_6 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_6 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 138;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 138;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_6, mod_consts[80]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 138;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[81];
            tmp_called_value_7 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_7 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_7 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 139;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 139;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_7, mod_consts[82]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 139;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[83];
            tmp_called_value_8 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_8 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_8 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 140;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 140;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_8, mod_consts[84]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 140;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[85];
            tmp_called_value_9 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_9 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_9 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 141;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 141;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_9, mod_consts[86]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 141;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[87];
            tmp_called_value_10 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_10 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_10 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 142;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 142;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_10, mod_consts[88]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 142;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[89];
            tmp_called_value_11 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_11 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_11 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 143;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 143;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_11, mod_consts[90]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 143;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[91];
            tmp_called_value_12 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_12 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_12 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 144;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 144;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_12, mod_consts[92]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 144;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[93];
            tmp_called_value_13 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_13 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_13 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 145;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 145;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_13, mod_consts[94]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 145;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[95];
            tmp_called_value_14 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_14 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_14 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 146;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 146;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_14, mod_consts[96]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 146;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[97];
            tmp_called_value_15 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_15 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_15 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 147;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 147;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_15, mod_consts[98]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 147;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[99];
            tmp_called_value_16 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_16 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_16 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 148;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 148;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_16, mod_consts[100]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 148;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[101];
            tmp_called_value_17 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_17 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_17 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 149;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 149;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_17, mod_consts[102]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 149;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[103];
            tmp_called_value_18 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_18 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_18 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 150;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 150;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_18, mod_consts[104]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 150;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[105];
            tmp_called_value_19 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_19 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_19 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 151;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 151;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_19, mod_consts[106]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 151;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[107];
            tmp_called_value_20 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_20 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_20 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 152;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 152;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_20, mod_consts[108]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 152;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[109];
            tmp_called_value_21 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_21 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_21 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 153;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 153;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_21, mod_consts[110]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 153;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[111];
            tmp_called_value_22 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_22 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_22 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 154;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 154;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_22, mod_consts[112]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 154;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[113];
            tmp_called_value_23 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_23 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_23 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 155;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 155;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_23, mod_consts[114]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 155;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[115];
            tmp_called_value_24 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_24 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_24 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 158;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 158;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_24, mod_consts[116]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 158;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[117];
            tmp_called_value_25 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_25 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_25 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 159;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 159;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_25, mod_consts[118]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 159;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[119];
            tmp_called_value_26 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_26 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_26 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 160;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 160;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_26, mod_consts[120]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 160;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[121];
            tmp_called_value_27 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_27 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_27 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 161;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 161;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_27, mod_consts[122]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 161;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[123];
            tmp_called_value_28 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_28 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_28 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 162;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 162;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_28, mod_consts[124]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 162;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[125];
            tmp_called_value_29 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_29 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_29 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 163;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 163;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_29, mod_consts[126]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 163;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[127];
            tmp_called_value_30 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_30 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_30 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 164;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 164;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_30, mod_consts[128]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 164;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[129];
            tmp_called_value_31 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_31 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_31 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 165;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 165;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_31, mod_consts[130]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 165;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[131];
            tmp_called_value_32 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_32 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_32 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 166;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 166;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_32, mod_consts[132]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 166;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[133];
            tmp_called_value_33 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_33 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_33 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 167;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 167;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_33, mod_consts[134]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 167;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[135];
            tmp_called_value_34 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_34 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_34 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 168;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 168;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_34, mod_consts[136]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 168;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[137];
            tmp_called_value_35 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_35 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_35 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 169;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 169;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_35, mod_consts[138]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 169;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[139];
            tmp_called_value_36 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_36 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_36 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 170;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 170;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_36, mod_consts[140]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 170;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[141];
            tmp_called_value_37 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_37 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_37 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 171;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 171;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_37, mod_consts[142]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 171;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[143];
            tmp_called_value_38 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_38 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_38 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 172;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 172;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_38, mod_consts[144]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 172;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[145];
            tmp_called_value_39 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_39 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_39 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 173;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 173;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_39, mod_consts[146]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 173;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[147];
            tmp_called_value_40 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_40 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_40 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 174;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 174;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_40, mod_consts[148]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 174;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[149];
            tmp_called_value_41 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_41 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_41 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 175;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 175;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_41, mod_consts[150]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 175;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[151];
            tmp_called_value_42 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_42 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_42 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 176;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 176;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_42, mod_consts[152]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 176;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[153];
            tmp_called_value_43 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_43 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_43 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 177;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 177;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_43, mod_consts[154]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 177;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[155];
            tmp_called_value_44 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_44 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_44 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 178;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 178;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_44, mod_consts[156]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 178;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[157];
            tmp_called_value_45 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_45 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_45 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 179;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 179;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_45, mod_consts[158]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 179;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[159];
            tmp_called_value_46 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_46 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_46 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 180;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 180;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_46, mod_consts[160]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 180;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[161];
            tmp_called_value_47 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_47 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_47 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 181;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 181;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_47, mod_consts[162]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 181;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[163];
            tmp_called_value_48 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_48 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_48 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 182;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 182;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_48, mod_consts[164]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 182;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[165];
            tmp_called_value_49 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_49 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_49 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 183;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 183;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_49, mod_consts[166]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 183;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[167];
            tmp_called_value_50 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_50 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_50 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 184;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 184;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_50, mod_consts[168]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 184;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[169];
            tmp_called_value_51 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_51 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_51 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 185;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 185;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_51, mod_consts[170]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 185;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[171];
            tmp_called_value_52 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_52 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_52 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 186;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 186;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_52, mod_consts[172]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 186;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[173];
            tmp_called_value_53 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_53 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_53 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 187;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 187;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_53, mod_consts[174]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 187;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[175];
            tmp_called_value_54 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_54 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_54 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 188;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 188;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_54, mod_consts[176]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 188;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[177];
            tmp_called_value_55 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_55 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_55 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 189;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 189;
            tmp_dict_value_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_55, mod_consts[178]);

            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 189;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_1, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
        }
        goto dict_build_noexception_2;
        // Exception handling pass through code for dict_build:
        dict_build_exception_2:;
        Py_DECREF(tmp_called_value_1);
        Py_DECREF(tmp_args_element_value_1);
        goto frame_exception_exit_1;
        // Finished with no exception for dict_build:
        dict_build_noexception_2:;
        frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 132;
        tmp_call_result_1 = CALL_FUNCTION_WITH_SINGLE_ARG(tstate, tmp_called_value_1, tmp_args_element_value_1);
        Py_DECREF(tmp_called_value_1);
        Py_DECREF(tmp_args_element_value_1);
        if (tmp_call_result_1 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 132;

            goto frame_exception_exit_1;
        }
        Py_DECREF(tmp_call_result_1);
    }
    {
        PyObject *tmp_called_value_56;
        PyObject *tmp_expression_value_3;
        PyObject *tmp_call_result_2;
        PyObject *tmp_args_element_value_2;
        PyObject *tmp_dict_key_3;
        PyObject *tmp_dict_value_3;
        PyObject *tmp_called_value_57;
        tmp_expression_value_3 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$misc(tstate);
        if (unlikely(tmp_expression_value_3 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[12]);
        }

        if (tmp_expression_value_3 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 192;

            goto frame_exception_exit_1;
        }
        tmp_called_value_56 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_3, mod_consts[70]);
        if (tmp_called_value_56 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 192;

            goto frame_exception_exit_1;
        }
        tmp_dict_key_3 = mod_consts[179];
        tmp_called_value_57 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
        if (unlikely(tmp_called_value_57 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
        }

        if (tmp_called_value_57 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));

            Py_DECREF(tmp_called_value_56);

            exception_lineno = 194;

            goto frame_exception_exit_1;
        }
        frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 194;
        tmp_dict_value_3 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_57, mod_consts[180]);

        if (tmp_dict_value_3 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);
            Py_DECREF(tmp_called_value_56);

            exception_lineno = 194;

            goto frame_exception_exit_1;
        }
        tmp_args_element_value_2 = _PyDict_NewPresized( 7 );
        {
            PyObject *tmp_called_value_58;
            PyObject *tmp_called_value_59;
            PyObject *tmp_called_value_60;
            PyObject *tmp_called_value_61;
            PyObject *tmp_called_value_62;
            PyObject *tmp_called_value_63;
            tmp_res = PyDict_SetItem(tmp_args_element_value_2, tmp_dict_key_3, tmp_dict_value_3);
            Py_DECREF(tmp_dict_value_3);
            assert(!(tmp_res != 0));
            tmp_dict_key_3 = mod_consts[181];
            tmp_called_value_58 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_58 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_58 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 195;

                goto dict_build_exception_3;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 195;
            tmp_dict_value_3 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_58, mod_consts[182]);

            if (tmp_dict_value_3 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 195;

                goto dict_build_exception_3;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_2, tmp_dict_key_3, tmp_dict_value_3);
            Py_DECREF(tmp_dict_value_3);
            assert(!(tmp_res != 0));
            tmp_dict_key_3 = mod_consts[183];
            tmp_called_value_59 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_59 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_59 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 196;

                goto dict_build_exception_3;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 196;
            tmp_dict_value_3 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_59, mod_consts[184]);

            if (tmp_dict_value_3 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 196;

                goto dict_build_exception_3;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_2, tmp_dict_key_3, tmp_dict_value_3);
            Py_DECREF(tmp_dict_value_3);
            assert(!(tmp_res != 0));
            tmp_dict_key_3 = mod_consts[185];
            tmp_called_value_60 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_60 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_60 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 197;

                goto dict_build_exception_3;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 197;
            tmp_dict_value_3 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_60, mod_consts[186]);

            if (tmp_dict_value_3 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 197;

                goto dict_build_exception_3;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_2, tmp_dict_key_3, tmp_dict_value_3);
            Py_DECREF(tmp_dict_value_3);
            assert(!(tmp_res != 0));
            tmp_dict_key_3 = mod_consts[187];
            tmp_called_value_61 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_61 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_61 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 198;

                goto dict_build_exception_3;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 198;
            tmp_dict_value_3 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_61, mod_consts[188]);

            if (tmp_dict_value_3 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 198;

                goto dict_build_exception_3;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_2, tmp_dict_key_3, tmp_dict_value_3);
            Py_DECREF(tmp_dict_value_3);
            assert(!(tmp_res != 0));
            tmp_dict_key_3 = mod_consts[189];
            tmp_called_value_62 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_62 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_62 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 199;

                goto dict_build_exception_3;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 199;
            tmp_dict_value_3 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_62, mod_consts[190]);

            if (tmp_dict_value_3 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 199;

                goto dict_build_exception_3;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_2, tmp_dict_key_3, tmp_dict_value_3);
            Py_DECREF(tmp_dict_value_3);
            assert(!(tmp_res != 0));
            tmp_dict_key_3 = mod_consts[191];
            tmp_called_value_63 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_63 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_63 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 200;

                goto dict_build_exception_3;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 200;
            tmp_dict_value_3 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_63, mod_consts[192]);

            if (tmp_dict_value_3 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 200;

                goto dict_build_exception_3;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_2, tmp_dict_key_3, tmp_dict_value_3);
            Py_DECREF(tmp_dict_value_3);
            assert(!(tmp_res != 0));
        }
        goto dict_build_noexception_3;
        // Exception handling pass through code for dict_build:
        dict_build_exception_3:;
        Py_DECREF(tmp_called_value_56);
        Py_DECREF(tmp_args_element_value_2);
        goto frame_exception_exit_1;
        // Finished with no exception for dict_build:
        dict_build_noexception_3:;
        frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 192;
        tmp_call_result_2 = CALL_FUNCTION_WITH_SINGLE_ARG(tstate, tmp_called_value_56, tmp_args_element_value_2);
        Py_DECREF(tmp_called_value_56);
        Py_DECREF(tmp_args_element_value_2);
        if (tmp_call_result_2 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 192;

            goto frame_exception_exit_1;
        }
        Py_DECREF(tmp_call_result_2);
    }
    {
        PyObject *tmp_called_value_64;
        PyObject *tmp_expression_value_4;
        PyObject *tmp_call_result_3;
        PyObject *tmp_call_arg_element_43;
        tmp_expression_value_4 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$misc(tstate);
        if (unlikely(tmp_expression_value_4 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[12]);
        }

        if (tmp_expression_value_4 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 203;

            goto frame_exception_exit_1;
        }
        tmp_called_value_64 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_4, mod_consts[70]);
        if (tmp_called_value_64 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 203;

            goto frame_exception_exit_1;
        }
        tmp_call_arg_element_43 = DICT_COPY(tstate, mod_consts[193]);
        frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 203;
        tmp_call_result_3 = CALL_FUNCTION_WITH_SINGLE_ARG(tstate, tmp_called_value_64, tmp_call_arg_element_43);
        Py_DECREF(tmp_called_value_64);
        Py_DECREF(tmp_call_arg_element_43);
        if (tmp_call_result_3 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 203;

            goto frame_exception_exit_1;
        }
        Py_DECREF(tmp_call_result_3);
    }
    {
        PyObject *tmp_assign_source_13;
        PyObject *tmp_dict_key_4;
        PyObject *tmp_dict_value_4;
        tmp_dict_key_4 = mod_consts[194];
        tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[195], "iiD");
        tmp_assign_source_13 = _PyDict_NewPresized( 733 );
        {
            PyObject *tmp_tuple_element_1;
            PyObject *tmp_called_value_65;
            PyObject *tmp_tuple_element_2;
            PyObject *tmp_called_value_66;
            PyObject *tmp_tuple_element_3;
            PyObject *tmp_called_value_67;
            PyObject *tmp_tuple_element_4;
            PyObject *tmp_called_value_68;
            PyObject *tmp_tuple_element_5;
            PyObject *tmp_called_value_69;
            PyObject *tmp_tuple_element_6;
            PyObject *tmp_called_value_70;
            PyObject *tmp_tuple_element_7;
            PyObject *tmp_called_value_71;
            PyObject *tmp_tuple_element_8;
            PyObject *tmp_called_value_72;
            PyObject *tmp_tuple_element_9;
            PyObject *tmp_called_value_73;
            PyObject *tmp_tuple_element_10;
            PyObject *tmp_called_value_74;
            PyObject *tmp_tuple_element_11;
            PyObject *tmp_called_value_75;
            PyObject *tmp_tuple_element_12;
            PyObject *tmp_called_value_76;
            PyObject *tmp_tuple_element_13;
            PyObject *tmp_called_value_77;
            PyObject *tmp_tuple_element_14;
            PyObject *tmp_called_value_78;
            PyObject *tmp_tuple_element_15;
            PyObject *tmp_called_value_79;
            PyObject *tmp_tuple_element_16;
            PyObject *tmp_called_value_80;
            PyObject *tmp_tuple_element_17;
            PyObject *tmp_called_value_81;
            PyObject *tmp_tuple_element_18;
            PyObject *tmp_called_value_82;
            PyObject *tmp_tuple_element_19;
            PyObject *tmp_called_value_83;
            PyObject *tmp_tuple_element_20;
            PyObject *tmp_called_value_84;
            PyObject *tmp_tuple_element_21;
            PyObject *tmp_called_value_85;
            PyObject *tmp_tuple_element_22;
            PyObject *tmp_called_value_86;
            PyObject *tmp_tuple_element_23;
            PyObject *tmp_called_value_87;
            PyObject *tmp_tuple_element_24;
            PyObject *tmp_called_value_88;
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[196];
            tmp_dict_value_4 = mod_consts[197];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[198];
            tmp_dict_value_4 = mod_consts[199];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[200];
            tmp_dict_value_4 = mod_consts[201];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[202];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[203], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[204];
            tmp_dict_value_4 = mod_consts[205];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[206];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[207], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[208];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[209], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[210];
            tmp_dict_value_4 = mod_consts[211];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[212];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[213], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[214];
            tmp_dict_value_4 = mod_consts[215];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[216];
            tmp_dict_value_4 = mod_consts[217];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[218];
            tmp_dict_value_4 = mod_consts[219];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[220];
            tmp_dict_value_4 = mod_consts[221];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[222];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[223], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[224];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[225], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[226];
            tmp_dict_value_4 = mod_consts[227];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[228];
            tmp_dict_value_4 = mod_consts[229];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[230];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[231], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[232];
            tmp_dict_value_4 = mod_consts[233];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[234];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[235], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[236];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[237], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[238];
            tmp_dict_value_4 = mod_consts[239];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[240];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[241], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[242];
            tmp_dict_value_4 = mod_consts[243];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[244];
            tmp_dict_value_4 = mod_consts[245];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[246];
            tmp_dict_value_4 = mod_consts[247];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[248];
            tmp_dict_value_4 = mod_consts[249];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[250];
            tmp_dict_value_4 = mod_consts[251];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[252];
            tmp_dict_value_4 = mod_consts[253];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[254];
            tmp_dict_value_4 = mod_consts[255];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[256];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[257], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[258];
            tmp_dict_value_4 = mod_consts[259];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[260];
            tmp_dict_value_4 = mod_consts[221];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[261];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[262], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[263];
            tmp_dict_value_4 = mod_consts[264];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[265];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[266], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[267];
            tmp_dict_value_4 = mod_consts[268];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[269];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[209], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[270];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[271], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[272];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[273], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[274];
            tmp_dict_value_4 = mod_consts[255];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[275];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[276], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[277];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[278], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[279];
            tmp_dict_value_4 = mod_consts[280];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[281];
            tmp_dict_value_4 = mod_consts[282];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[283];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[284], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[285];
            tmp_dict_value_4 = mod_consts[286];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[287];
            tmp_called_value_65 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(tstate);
            if (unlikely(tmp_called_value_65 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[11]);
            }

            if (tmp_called_value_65 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 374;

                goto dict_build_exception_4;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 374;
            tmp_tuple_element_1 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_65, mod_consts[288]);

            if (tmp_tuple_element_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 374;

                goto dict_build_exception_4;
            }
            tmp_dict_value_4 = MAKE_TUPLE_EMPTY(tstate, 3);
            PyTuple_SET_ITEM(tmp_dict_value_4, 0, tmp_tuple_element_1);
            tmp_tuple_element_1 = mod_consts[289];
            PyTuple_SET_ITEM0(tmp_dict_value_4, 1, tmp_tuple_element_1);
            tmp_tuple_element_1 = DEEP_COPY_DICT(tstate, mod_consts[290]);
            PyTuple_SET_ITEM(tmp_dict_value_4, 2, tmp_tuple_element_1);
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[291];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[292], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[293];
            tmp_dict_value_4 = mod_consts[205];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[294];
            tmp_dict_value_4 = mod_consts[259];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[295];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[296], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[297];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[298], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[299];
            tmp_dict_value_4 = mod_consts[300];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[301];
            tmp_called_value_66 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(tstate);
            if (unlikely(tmp_called_value_66 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[11]);
            }

            if (tmp_called_value_66 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 396;

                goto dict_build_exception_4;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 396;
            tmp_tuple_element_2 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_66, mod_consts[302]);

            if (tmp_tuple_element_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 396;

                goto dict_build_exception_4;
            }
            tmp_dict_value_4 = MAKE_TUPLE_EMPTY(tstate, 1);
            PyTuple_SET_ITEM(tmp_dict_value_4, 0, tmp_tuple_element_2);
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[303];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[304], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[305];
            tmp_dict_value_4 = mod_consts[306];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[307];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[308], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[309];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[310], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[311];
            tmp_dict_value_4 = mod_consts[312];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[313];
            tmp_dict_value_4 = mod_consts[314];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[315];
            tmp_dict_value_4 = mod_consts[316];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[317];
            tmp_dict_value_4 = mod_consts[229];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[318];
            tmp_dict_value_4 = mod_consts[319];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[320];
            tmp_dict_value_4 = mod_consts[321];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[322];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[323], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[324];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[325], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[326];
            tmp_dict_value_4 = mod_consts[221];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[327];
            tmp_dict_value_4 = mod_consts[328];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[329];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[330], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[331];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[332], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[333];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[334], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[335];
            tmp_dict_value_4 = mod_consts[336];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[337];
            tmp_dict_value_4 = mod_consts[338];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[339];
            tmp_dict_value_4 = mod_consts[340];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[341];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[342], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[343];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[344], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[345];
            tmp_dict_value_4 = mod_consts[346];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[347];
            tmp_dict_value_4 = mod_consts[348];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[349];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[350], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[351];
            tmp_dict_value_4 = mod_consts[352];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[353];
            tmp_dict_value_4 = mod_consts[354];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[355];
            tmp_dict_value_4 = mod_consts[356];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[357];
            tmp_dict_value_4 = mod_consts[358];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[359];
            tmp_called_value_67 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(tstate);
            if (unlikely(tmp_called_value_67 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[11]);
            }

            if (tmp_called_value_67 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 503;

                goto dict_build_exception_4;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 503;
            tmp_tuple_element_3 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_67, mod_consts[360]);

            if (tmp_tuple_element_3 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 503;

                goto dict_build_exception_4;
            }
            tmp_dict_value_4 = MAKE_TUPLE_EMPTY(tstate, 1);
            PyTuple_SET_ITEM(tmp_dict_value_4, 0, tmp_tuple_element_3);
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[361];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[362], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[363];
            tmp_dict_value_4 = mod_consts[221];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[364];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[365], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[366];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[367], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[368];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[369], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[370];
            tmp_dict_value_4 = mod_consts[371];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[372];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[373], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[374];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[375], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[376];
            tmp_dict_value_4 = mod_consts[259];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[377];
            tmp_dict_value_4 = mod_consts[378];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[379];
            tmp_dict_value_4 = mod_consts[380];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[381];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[382], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[383];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[203], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[384];
            tmp_dict_value_4 = mod_consts[385];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[386];
            tmp_dict_value_4 = mod_consts[387];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[388];
            tmp_dict_value_4 = mod_consts[389];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[390];
            tmp_dict_value_4 = mod_consts[205];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[391];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[392], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[393];
            tmp_dict_value_4 = mod_consts[394];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[395];
            tmp_dict_value_4 = mod_consts[396];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[397];
            tmp_dict_value_4 = mod_consts[340];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[398];
            tmp_dict_value_4 = mod_consts[399];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[400];
            tmp_dict_value_4 = mod_consts[401];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[402];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[403], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[404];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[405], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[406];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[407], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[408];
            tmp_dict_value_4 = mod_consts[409];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[410];
            tmp_dict_value_4 = mod_consts[411];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[412];
            tmp_dict_value_4 = mod_consts[413];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[414];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[415], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[416];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[417], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[418];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[419], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[420];
            tmp_dict_value_4 = mod_consts[205];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[421];
            tmp_dict_value_4 = mod_consts[280];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[422];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[423], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[424];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[425], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[426];
            tmp_dict_value_4 = mod_consts[197];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[427];
            tmp_dict_value_4 = mod_consts[428];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[429];
            tmp_dict_value_4 = mod_consts[221];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[430];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[431], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[432];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[369], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[433];
            tmp_dict_value_4 = mod_consts[434];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[435];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[436], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[437];
            tmp_dict_value_4 = mod_consts[438];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[439];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[440], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[441];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[442], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[443];
            tmp_dict_value_4 = mod_consts[444];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[445];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[446], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[447];
            tmp_dict_value_4 = mod_consts[448];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[449];
            tmp_dict_value_4 = mod_consts[205];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[450];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[451], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[452];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[453], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[454];
            tmp_dict_value_4 = mod_consts[455];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[456];
            tmp_dict_value_4 = mod_consts[457];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[458];
            tmp_called_value_68 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(tstate);
            if (unlikely(tmp_called_value_68 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[11]);
            }

            if (tmp_called_value_68 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 729;

                goto dict_build_exception_4;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 729;
            tmp_tuple_element_4 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_68, mod_consts[360]);

            if (tmp_tuple_element_4 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 729;

                goto dict_build_exception_4;
            }
            tmp_dict_value_4 = MAKE_TUPLE_EMPTY(tstate, 1);
            PyTuple_SET_ITEM(tmp_dict_value_4, 0, tmp_tuple_element_4);
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[459];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[460], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[461];
            tmp_dict_value_4 = mod_consts[462];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[463];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[464], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[465];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[466], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[467];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[468], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[469];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[470], "iid");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[471];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[472], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[473];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[474], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[475];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[476], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[477];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[478], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[479];
            tmp_called_value_69 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(tstate);
            if (unlikely(tmp_called_value_69 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[11]);
            }

            if (tmp_called_value_69 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 811;

                goto dict_build_exception_4;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 811;
            tmp_tuple_element_5 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_69, mod_consts[480]);

            if (tmp_tuple_element_5 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 811;

                goto dict_build_exception_4;
            }
            tmp_dict_value_4 = MAKE_TUPLE_EMPTY(tstate, 1);
            PyTuple_SET_ITEM(tmp_dict_value_4, 0, tmp_tuple_element_5);
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[481];
            tmp_dict_value_4 = mod_consts[482];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[483];
            tmp_dict_value_4 = mod_consts[358];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[484];
            tmp_dict_value_4 = mod_consts[485];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[486];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[487], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[488];
            tmp_dict_value_4 = mod_consts[489];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[490];
            tmp_called_value_70 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(tstate);
            if (unlikely(tmp_called_value_70 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[11]);
            }

            if (tmp_called_value_70 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 821;

                goto dict_build_exception_4;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 821;
            tmp_tuple_element_6 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_70, mod_consts[491]);

            if (tmp_tuple_element_6 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 821;

                goto dict_build_exception_4;
            }
            tmp_dict_value_4 = MAKE_TUPLE_EMPTY(tstate, 1);
            PyTuple_SET_ITEM(tmp_dict_value_4, 0, tmp_tuple_element_6);
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[492];
            tmp_dict_value_4 = mod_consts[239];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[493];
            tmp_dict_value_4 = mod_consts[409];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[494];
            tmp_dict_value_4 = mod_consts[495];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[496];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[497], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[498];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[499], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[500];
            tmp_dict_value_4 = mod_consts[501];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[502];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[503], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[504];
            tmp_dict_value_4 = mod_consts[239];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[505];
            tmp_dict_value_4 = mod_consts[444];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[506];
            tmp_dict_value_4 = mod_consts[205];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[507];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[508], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[509];
            tmp_dict_value_4 = mod_consts[510];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[511];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[512], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[513];
            tmp_dict_value_4 = mod_consts[514];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[515];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[516], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[517];
            tmp_called_value_71 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(tstate);
            if (unlikely(tmp_called_value_71 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[11]);
            }

            if (tmp_called_value_71 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 864;

                goto dict_build_exception_4;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 864;
            tmp_tuple_element_7 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_71, mod_consts[518]);

            if (tmp_tuple_element_7 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 864;

                goto dict_build_exception_4;
            }
            tmp_dict_value_4 = MAKE_TUPLE_EMPTY(tstate, 1);
            PyTuple_SET_ITEM(tmp_dict_value_4, 0, tmp_tuple_element_7);
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[519];
            tmp_dict_value_4 = mod_consts[520];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[521];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[522], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[523];
            tmp_dict_value_4 = mod_consts[524];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[525];
            tmp_dict_value_4 = mod_consts[526];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[527];
            tmp_dict_value_4 = mod_consts[358];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[528];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[529], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[530];
            tmp_dict_value_4 = mod_consts[531];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[532];
            tmp_dict_value_4 = mod_consts[401];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[533];
            tmp_dict_value_4 = mod_consts[229];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[534];
            tmp_dict_value_4 = mod_consts[535];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[536];
            tmp_dict_value_4 = mod_consts[537];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[538];
            tmp_dict_value_4 = mod_consts[539];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[540];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[541], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[542];
            tmp_dict_value_4 = mod_consts[543];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[544];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[545], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[546];
            tmp_dict_value_4 = mod_consts[205];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[547];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[342], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[548];
            tmp_dict_value_4 = mod_consts[514];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[549];
            tmp_dict_value_4 = mod_consts[550];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[551];
            tmp_dict_value_4 = mod_consts[552];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[553];
            tmp_dict_value_4 = mod_consts[221];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[554];
            tmp_called_value_72 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(tstate);
            if (unlikely(tmp_called_value_72 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[11]);
            }

            if (tmp_called_value_72 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 914;

                goto dict_build_exception_4;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 914;
            tmp_tuple_element_8 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_72, mod_consts[360]);

            if (tmp_tuple_element_8 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 914;

                goto dict_build_exception_4;
            }
            tmp_dict_value_4 = MAKE_TUPLE_EMPTY(tstate, 1);
            PyTuple_SET_ITEM(tmp_dict_value_4, 0, tmp_tuple_element_8);
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[555];
            tmp_dict_value_4 = mod_consts[556];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[557];
            tmp_dict_value_4 = mod_consts[558];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[559];
            tmp_dict_value_4 = mod_consts[560];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[561];
            tmp_dict_value_4 = mod_consts[562];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[563];
            tmp_dict_value_4 = mod_consts[564];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[565];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[566], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[567];
            tmp_dict_value_4 = mod_consts[568];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[569];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[570], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[571];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[572], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[573];
            tmp_dict_value_4 = mod_consts[574];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[575];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[576], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[577];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[241], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[578];
            tmp_dict_value_4 = mod_consts[579];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[580];
            tmp_dict_value_4 = mod_consts[581];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[582];
            tmp_dict_value_4 = mod_consts[229];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[583];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[584], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[585];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[586], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[587];
            tmp_dict_value_4 = mod_consts[239];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[588];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[589], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[590];
            tmp_dict_value_4 = mod_consts[591];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[592];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[593], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[594];
            tmp_dict_value_4 = mod_consts[595];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[596];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[597], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[598];
            tmp_dict_value_4 = mod_consts[599];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[600];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[601], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[602];
            tmp_dict_value_4 = mod_consts[603];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[604];
            tmp_dict_value_4 = mod_consts[605];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[606];
            tmp_dict_value_4 = mod_consts[259];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[607];
            tmp_dict_value_4 = mod_consts[608];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[609];
            tmp_dict_value_4 = mod_consts[610];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[611];
            tmp_dict_value_4 = mod_consts[371];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[612];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[613], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[614];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[615], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[616];
            tmp_dict_value_4 = mod_consts[617];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[618];
            tmp_dict_value_4 = mod_consts[619];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[620];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[621], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[622];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[623], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[624];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[625], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[626];
            tmp_dict_value_4 = mod_consts[462];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[627];
            tmp_dict_value_4 = mod_consts[221];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[628];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[629], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[630];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[631], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[632];
            tmp_dict_value_4 = mod_consts[633];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[634];
            tmp_dict_value_4 = mod_consts[635];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[636];
            tmp_dict_value_4 = mod_consts[560];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[637];
            tmp_dict_value_4 = mod_consts[608];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[638];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[639], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[640];
            tmp_dict_value_4 = mod_consts[568];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[641];
            tmp_dict_value_4 = mod_consts[259];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[642];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[643], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[644];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[645], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[646];
            tmp_dict_value_4 = mod_consts[358];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[647];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[648], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[649];
            tmp_dict_value_4 = mod_consts[448];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[650];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[651], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[652];
            tmp_dict_value_4 = mod_consts[653];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[654];
            tmp_dict_value_4 = mod_consts[205];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[655];
            tmp_dict_value_4 = mod_consts[656];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[657];
            tmp_dict_value_4 = mod_consts[658];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[659];
            tmp_dict_value_4 = mod_consts[660];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[661];
            tmp_dict_value_4 = mod_consts[385];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[662];
            tmp_dict_value_4 = mod_consts[663];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[664];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[665], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[666];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[667], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[668];
            tmp_dict_value_4 = mod_consts[669];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[670];
            tmp_dict_value_4 = mod_consts[671];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[672];
            tmp_dict_value_4 = mod_consts[673];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[674];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[675], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[676];
            tmp_dict_value_4 = mod_consts[677];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[678];
            tmp_dict_value_4 = mod_consts[679];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[680];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[681], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[682];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[683], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[684];
            tmp_dict_value_4 = mod_consts[685];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[686];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[687], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[688];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[689], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[690];
            tmp_dict_value_4 = mod_consts[691];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[692];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[693], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[694];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[237], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[695];
            tmp_dict_value_4 = mod_consts[568];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[696];
            tmp_dict_value_4 = mod_consts[697];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[698];
            tmp_dict_value_4 = mod_consts[699];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[700];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[487], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[701];
            tmp_dict_value_4 = mod_consts[221];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[702];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[703], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[704];
            tmp_dict_value_4 = mod_consts[705];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[706];
            tmp_dict_value_4 = mod_consts[707];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[708];
            tmp_dict_value_4 = mod_consts[709];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[710];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[711], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[712];
            tmp_dict_value_4 = mod_consts[669];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[713];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[714], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[715];
            tmp_dict_value_4 = mod_consts[229];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[716];
            tmp_dict_value_4 = mod_consts[717];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[718];
            tmp_dict_value_4 = mod_consts[719];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[720];
            tmp_dict_value_4 = mod_consts[221];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[721];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[266], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[722];
            tmp_dict_value_4 = mod_consts[462];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[723];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[724], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[725];
            tmp_dict_value_4 = mod_consts[726];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[727];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[728], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[729];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[730], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[731];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[478], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[732];
            tmp_dict_value_4 = mod_consts[733];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[734];
            tmp_called_value_73 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(tstate);
            if (unlikely(tmp_called_value_73 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[11]);
            }

            if (tmp_called_value_73 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 1284;

                goto dict_build_exception_4;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 1284;
            tmp_tuple_element_9 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_73, mod_consts[360]);

            if (tmp_tuple_element_9 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 1284;

                goto dict_build_exception_4;
            }
            tmp_dict_value_4 = MAKE_TUPLE_EMPTY(tstate, 1);
            PyTuple_SET_ITEM(tmp_dict_value_4, 0, tmp_tuple_element_9);
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[735];
            tmp_dict_value_4 = mod_consts[736];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[737];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[375], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[738];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[739], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[740];
            tmp_dict_value_4 = mod_consts[462];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[741];
            tmp_dict_value_4 = mod_consts[352];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[742];
            tmp_dict_value_4 = mod_consts[743];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[744];
            tmp_dict_value_4 = mod_consts[745];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[746];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[747], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[748];
            tmp_called_value_74 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(tstate);
            if (unlikely(tmp_called_value_74 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[11]);
            }

            if (tmp_called_value_74 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 1319;

                goto dict_build_exception_4;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 1319;
            tmp_tuple_element_10 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_74, mod_consts[491]);

            if (tmp_tuple_element_10 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 1319;

                goto dict_build_exception_4;
            }
            tmp_dict_value_4 = MAKE_TUPLE_EMPTY(tstate, 1);
            PyTuple_SET_ITEM(tmp_dict_value_4, 0, tmp_tuple_element_10);
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[749];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[203], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[750];
            tmp_dict_value_4 = mod_consts[751];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[752];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[753], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[754];
            tmp_called_value_75 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(tstate);
            if (unlikely(tmp_called_value_75 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[11]);
            }

            if (tmp_called_value_75 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 1331;

                goto dict_build_exception_4;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 1331;
            tmp_tuple_element_11 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_75, mod_consts[360]);

            if (tmp_tuple_element_11 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 1331;

                goto dict_build_exception_4;
            }
            tmp_dict_value_4 = MAKE_TUPLE_EMPTY(tstate, 1);
            PyTuple_SET_ITEM(tmp_dict_value_4, 0, tmp_tuple_element_11);
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[755];
            tmp_dict_value_4 = mod_consts[756];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[757];
            tmp_dict_value_4 = mod_consts[758];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[759];
            tmp_dict_value_4 = mod_consts[760];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[761];
            tmp_dict_value_4 = mod_consts[762];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[763];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[764], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[765];
            tmp_dict_value_4 = mod_consts[766];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[767];
            tmp_dict_value_4 = mod_consts[221];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[768];
            tmp_dict_value_4 = mod_consts[769];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[770];
            tmp_dict_value_4 = mod_consts[409];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[771];
            tmp_dict_value_4 = mod_consts[772];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[773];
            tmp_dict_value_4 = mod_consts[774];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[775];
            tmp_dict_value_4 = mod_consts[776];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[777];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[325], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[778];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[779], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[780];
            tmp_dict_value_4 = mod_consts[781];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[782];
            tmp_dict_value_4 = mod_consts[783];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[784];
            tmp_dict_value_4 = mod_consts[785];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[786];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[787], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[788];
            tmp_dict_value_4 = mod_consts[789];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[790];
            tmp_dict_value_4 = mod_consts[205];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[791];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[792], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[793];
            tmp_dict_value_4 = mod_consts[385];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[794];
            tmp_dict_value_4 = mod_consts[448];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[795];
            tmp_dict_value_4 = mod_consts[280];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[796];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[797], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[798];
            tmp_dict_value_4 = mod_consts[799];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[800];
            tmp_dict_value_4 = mod_consts[229];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[801];
            tmp_dict_value_4 = mod_consts[312];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[802];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[803], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[804];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[805], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[806];
            tmp_dict_value_4 = mod_consts[807];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[808];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[809], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[810];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[811], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[812];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[813], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[814];
            tmp_dict_value_4 = mod_consts[815];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[816];
            tmp_dict_value_4 = mod_consts[817];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[818];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[819], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[820];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[325], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[821];
            tmp_dict_value_4 = mod_consts[822];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[823];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[824], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[825];
            tmp_dict_value_4 = mod_consts[826];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[827];
            tmp_dict_value_4 = mod_consts[772];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[828];
            tmp_dict_value_4 = mod_consts[829];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[830];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[831], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[832];
            tmp_dict_value_4 = mod_consts[833];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[834];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[835], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[836];
            tmp_dict_value_4 = mod_consts[837];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[838];
            tmp_dict_value_4 = mod_consts[839];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[840];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[841], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[842];
            tmp_dict_value_4 = mod_consts[215];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[843];
            tmp_dict_value_4 = mod_consts[844];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[845];
            tmp_dict_value_4 = mod_consts[846];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[847];
            tmp_dict_value_4 = mod_consts[848];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[849];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[850], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[851];
            tmp_dict_value_4 = mod_consts[852];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[853];
            tmp_dict_value_4 = mod_consts[854];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[855];
            tmp_called_value_76 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(tstate);
            if (unlikely(tmp_called_value_76 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[11]);
            }

            if (tmp_called_value_76 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 1504;

                goto dict_build_exception_4;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 1504;
            tmp_tuple_element_12 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_76, mod_consts[856]);

            if (tmp_tuple_element_12 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 1504;

                goto dict_build_exception_4;
            }
            tmp_dict_value_4 = MAKE_TUPLE_EMPTY(tstate, 1);
            PyTuple_SET_ITEM(tmp_dict_value_4, 0, tmp_tuple_element_12);
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[857];
            tmp_dict_value_4 = mod_consts[858];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[859];
            tmp_dict_value_4 = mod_consts[860];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[861];
            tmp_called_value_77 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(tstate);
            if (unlikely(tmp_called_value_77 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[11]);
            }

            if (tmp_called_value_77 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 1507;

                goto dict_build_exception_4;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 1507;
            tmp_tuple_element_13 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_77, mod_consts[360]);

            if (tmp_tuple_element_13 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 1507;

                goto dict_build_exception_4;
            }
            tmp_dict_value_4 = MAKE_TUPLE_EMPTY(tstate, 1);
            PyTuple_SET_ITEM(tmp_dict_value_4, 0, tmp_tuple_element_13);
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[862];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[863], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[864];
            tmp_dict_value_4 = mod_consts[280];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[865];
            tmp_dict_value_4 = mod_consts[312];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[866];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[867], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[868];
            tmp_dict_value_4 = mod_consts[869];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[870];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[871], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[872];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[873], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[874];
            tmp_dict_value_4 = mod_consts[239];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[875];
            tmp_dict_value_4 = mod_consts[876];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[877];
            tmp_dict_value_4 = mod_consts[205];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[878];
            tmp_dict_value_4 = mod_consts[879];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[880];
            tmp_dict_value_4 = mod_consts[205];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[881];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[882], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[883];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[884], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[885];
            tmp_dict_value_4 = mod_consts[591];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[886];
            tmp_dict_value_4 = mod_consts[409];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[887];
            tmp_dict_value_4 = mod_consts[888];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[889];
            tmp_dict_value_4 = mod_consts[890];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[891];
            tmp_called_value_78 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(tstate);
            if (unlikely(tmp_called_value_78 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[11]);
            }

            if (tmp_called_value_78 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 1580;

                goto dict_build_exception_4;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 1580;
            tmp_tuple_element_14 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_78, mod_consts[856]);

            if (tmp_tuple_element_14 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 1580;

                goto dict_build_exception_4;
            }
            tmp_dict_value_4 = MAKE_TUPLE_EMPTY(tstate, 1);
            PyTuple_SET_ITEM(tmp_dict_value_4, 0, tmp_tuple_element_14);
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[892];
            tmp_called_value_79 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(tstate);
            if (unlikely(tmp_called_value_79 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[11]);
            }

            if (tmp_called_value_79 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 1581;

                goto dict_build_exception_4;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 1581;
            tmp_tuple_element_15 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_79, mod_consts[360]);

            if (tmp_tuple_element_15 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 1581;

                goto dict_build_exception_4;
            }
            tmp_dict_value_4 = MAKE_TUPLE_EMPTY(tstate, 1);
            PyTuple_SET_ITEM(tmp_dict_value_4, 0, tmp_tuple_element_15);
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[893];
            tmp_dict_value_4 = mod_consts[860];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[894];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[895], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[896];
            tmp_called_value_80 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(tstate);
            if (unlikely(tmp_called_value_80 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[11]);
            }

            if (tmp_called_value_80 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 1593;

                goto dict_build_exception_4;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 1593;
            tmp_tuple_element_16 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_80, mod_consts[491]);

            if (tmp_tuple_element_16 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 1593;

                goto dict_build_exception_4;
            }
            tmp_dict_value_4 = MAKE_TUPLE_EMPTY(tstate, 1);
            PyTuple_SET_ITEM(tmp_dict_value_4, 0, tmp_tuple_element_16);
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[897];
            tmp_dict_value_4 = mod_consts[898];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[899];
            tmp_dict_value_4 = mod_consts[348];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[900];
            tmp_dict_value_4 = mod_consts[591];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[901];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[902], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[903];
            tmp_dict_value_4 = mod_consts[221];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[904];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[905], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[906];
            tmp_called_value_81 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(tstate);
            if (unlikely(tmp_called_value_81 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[11]);
            }

            if (tmp_called_value_81 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 1625;

                goto dict_build_exception_4;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 1625;
            tmp_tuple_element_17 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_81, mod_consts[360]);

            if (tmp_tuple_element_17 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 1625;

                goto dict_build_exception_4;
            }
            tmp_dict_value_4 = MAKE_TUPLE_EMPTY(tstate, 1);
            PyTuple_SET_ITEM(tmp_dict_value_4, 0, tmp_tuple_element_17);
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[907];
            tmp_dict_value_4 = mod_consts[685];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[908];
            tmp_dict_value_4 = mod_consts[909];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[910];
            tmp_dict_value_4 = mod_consts[352];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[911];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[912], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[913];
            tmp_dict_value_4 = mod_consts[914];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[915];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[916], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[917];
            tmp_dict_value_4 = mod_consts[205];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[918];
            tmp_dict_value_4 = mod_consts[919];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[920];
            tmp_dict_value_4 = mod_consts[221];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[921];
            tmp_dict_value_4 = mod_consts[603];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[922];
            tmp_dict_value_4 = mod_consts[923];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[924];
            tmp_dict_value_4 = mod_consts[514];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[925];
            tmp_dict_value_4 = mod_consts[485];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[926];
            tmp_dict_value_4 = mod_consts[501];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[927];
            tmp_dict_value_4 = mod_consts[280];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[928];
            tmp_dict_value_4 = mod_consts[929];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[930];
            tmp_dict_value_4 = mod_consts[931];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[932];
            tmp_dict_value_4 = mod_consts[608];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[933];
            tmp_dict_value_4 = mod_consts[514];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[934];
            tmp_dict_value_4 = mod_consts[229];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[935];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[936], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[937];
            tmp_dict_value_4 = mod_consts[938];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[939];
            tmp_dict_value_4 = mod_consts[760];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[940];
            tmp_dict_value_4 = mod_consts[941];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[942];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[943], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[944];
            tmp_dict_value_4 = mod_consts[485];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[945];
            tmp_dict_value_4 = mod_consts[455];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[946];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[947], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[948];
            tmp_dict_value_4 = mod_consts[259];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[949];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[950], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[951];
            tmp_dict_value_4 = mod_consts[685];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[952];
            tmp_dict_value_4 = mod_consts[953];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[954];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[955], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[956];
            tmp_dict_value_4 = mod_consts[957];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[958];
            tmp_dict_value_4 = mod_consts[346];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[959];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[960], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[961];
            tmp_dict_value_4 = mod_consts[962];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[963];
            tmp_dict_value_4 = mod_consts[758];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[964];
            tmp_dict_value_4 = mod_consts[965];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[966];
            tmp_dict_value_4 = mod_consts[967];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[968];
            tmp_dict_value_4 = mod_consts[603];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[969];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[970], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[971];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[972], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[973];
            tmp_dict_value_4 = mod_consts[239];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[974];
            tmp_dict_value_4 = mod_consts[229];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[975];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[976], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[977];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[978], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[979];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[325], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[980];
            tmp_dict_value_4 = mod_consts[610];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[981];
            tmp_dict_value_4 = mod_consts[982];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[983];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[984], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[985];
            tmp_dict_value_4 = mod_consts[986];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[987];
            tmp_dict_value_4 = mod_consts[988];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[989];
            tmp_dict_value_4 = mod_consts[990];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[991];
            tmp_dict_value_4 = mod_consts[807];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[992];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[993], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[994];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[995], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[996];
            tmp_dict_value_4 = mod_consts[997];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[998];
            tmp_dict_value_4 = mod_consts[358];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[999];
            tmp_dict_value_4 = mod_consts[348];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1000];
            tmp_called_value_82 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(tstate);
            if (unlikely(tmp_called_value_82 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[11]);
            }

            if (tmp_called_value_82 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 1796;

                goto dict_build_exception_4;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 1796;
            tmp_tuple_element_18 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_82, mod_consts[1001]);

            if (tmp_tuple_element_18 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 1796;

                goto dict_build_exception_4;
            }
            tmp_dict_value_4 = MAKE_TUPLE_EMPTY(tstate, 3);
            PyTuple_SET_ITEM(tmp_dict_value_4, 0, tmp_tuple_element_18);
            tmp_tuple_element_18 = mod_consts[289];
            PyTuple_SET_ITEM0(tmp_dict_value_4, 1, tmp_tuple_element_18);
            tmp_tuple_element_18 = DEEP_COPY_DICT(tstate, mod_consts[1002]);
            PyTuple_SET_ITEM(tmp_dict_value_4, 2, tmp_tuple_element_18);
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1003];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1004], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1005];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1006], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1007];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1008], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1009];
            tmp_dict_value_4 = mod_consts[1010];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1011];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1012], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1013];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1014], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1015];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1016], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1017];
            tmp_dict_value_4 = mod_consts[438];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1018];
            tmp_dict_value_4 = mod_consts[1019];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1020];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1021], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1022];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1023], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1024];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1025], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1026];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1027], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1028];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1029], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1030];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1031], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1032];
            tmp_dict_value_4 = mod_consts[346];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1033];
            tmp_dict_value_4 = mod_consts[514];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1034];
            tmp_dict_value_4 = mod_consts[617];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1035];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1036], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1037];
            tmp_dict_value_4 = mod_consts[247];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1038];
            tmp_dict_value_4 = mod_consts[1039];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1040];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[689], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1041];
            tmp_dict_value_4 = mod_consts[1042];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1043];
            tmp_dict_value_4 = mod_consts[485];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1044];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[803], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1045];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1046], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1047];
            tmp_dict_value_4 = mod_consts[1048];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1049];
            tmp_dict_value_4 = mod_consts[617];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1050];
            tmp_dict_value_4 = mod_consts[1051];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1052];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1053], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1054];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1055], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1056];
            tmp_dict_value_4 = mod_consts[1057];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1058];
            tmp_dict_value_4 = mod_consts[1059];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1060];
            tmp_dict_value_4 = mod_consts[197];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1061];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1062], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1063];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1064], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1065];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1066], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1067];
            tmp_dict_value_4 = mod_consts[1068];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1069];
            tmp_dict_value_4 = mod_consts[1070];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1071];
            tmp_dict_value_4 = mod_consts[205];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1072];
            tmp_dict_value_4 = mod_consts[685];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1073];
            tmp_dict_value_4 = mod_consts[1074];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1075];
            tmp_dict_value_4 = mod_consts[591];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1076];
            tmp_dict_value_4 = mod_consts[1077];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1078];
            tmp_dict_value_4 = mod_consts[1079];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1080];
            tmp_dict_value_4 = mod_consts[603];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1081];
            tmp_dict_value_4 = mod_consts[389];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1082];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[350], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1083];
            tmp_dict_value_4 = mod_consts[1084];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1085];
            tmp_dict_value_4 = mod_consts[1086];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1087];
            tmp_dict_value_4 = mod_consts[1088];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1089];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1090], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1091];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[978], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1092];
            tmp_called_value_83 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(tstate);
            if (unlikely(tmp_called_value_83 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[11]);
            }

            if (tmp_called_value_83 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 2001;

                goto dict_build_exception_4;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 2001;
            tmp_tuple_element_19 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_83, mod_consts[360]);

            if (tmp_tuple_element_19 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 2001;

                goto dict_build_exception_4;
            }
            tmp_dict_value_4 = MAKE_TUPLE_EMPTY(tstate, 1);
            PyTuple_SET_ITEM(tmp_dict_value_4, 0, tmp_tuple_element_19);
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1093];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1025], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1094];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1095], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1096];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1097], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1098];
            tmp_dict_value_4 = mod_consts[879];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1099];
            tmp_dict_value_4 = mod_consts[448];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1100];
            tmp_dict_value_4 = mod_consts[1101];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1102];
            tmp_dict_value_4 = mod_consts[205];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1103];
            tmp_dict_value_4 = mod_consts[1104];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1105];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1106], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1107];
            tmp_dict_value_4 = mod_consts[199];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1108];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1109], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1110];
            tmp_dict_value_4 = mod_consts[409];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1111];
            tmp_dict_value_4 = mod_consts[1112];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1113];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1114], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1115];
            tmp_dict_value_4 = mod_consts[1116];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1117];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1118], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1119];
            tmp_dict_value_4 = mod_consts[1120];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1121];
            tmp_dict_value_4 = mod_consts[346];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1122];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1123], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1124];
            tmp_dict_value_4 = mod_consts[1125];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1126];
            tmp_dict_value_4 = mod_consts[205];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1127];
            tmp_dict_value_4 = mod_consts[1070];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1128];
            tmp_dict_value_4 = mod_consts[1129];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1130];
            tmp_dict_value_4 = mod_consts[760];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1131];
            tmp_dict_value_4 = mod_consts[1132];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1133];
            tmp_dict_value_4 = mod_consts[1134];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1135];
            tmp_dict_value_4 = mod_consts[1136];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1137];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1138], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1139];
            tmp_dict_value_4 = mod_consts[312];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1140];
            tmp_dict_value_4 = mod_consts[1141];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1142];
            tmp_dict_value_4 = mod_consts[1143];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1144];
            tmp_dict_value_4 = mod_consts[1145];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1146];
            tmp_dict_value_4 = mod_consts[1147];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1148];
            tmp_called_value_84 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(tstate);
            if (unlikely(tmp_called_value_84 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[11]);
            }

            if (tmp_called_value_84 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 2101;

                goto dict_build_exception_4;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 2101;
            tmp_tuple_element_20 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_84, mod_consts[360]);

            if (tmp_tuple_element_20 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 2101;

                goto dict_build_exception_4;
            }
            tmp_dict_value_4 = MAKE_TUPLE_EMPTY(tstate, 1);
            PyTuple_SET_ITEM(tmp_dict_value_4, 0, tmp_tuple_element_20);
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1149];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1150], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1151];
            tmp_dict_value_4 = mod_consts[380];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1152];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[325], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1153];
            tmp_dict_value_4 = mod_consts[1154];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1155];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1156], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1157];
            tmp_dict_value_4 = mod_consts[1158];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1159];
            tmp_called_value_85 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(tstate);
            if (unlikely(tmp_called_value_85 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[11]);
            }

            if (tmp_called_value_85 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 2120;

                goto dict_build_exception_4;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 2120;
            tmp_tuple_element_21 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_85, mod_consts[856]);

            if (tmp_tuple_element_21 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 2120;

                goto dict_build_exception_4;
            }
            tmp_dict_value_4 = MAKE_TUPLE_EMPTY(tstate, 1);
            PyTuple_SET_ITEM(tmp_dict_value_4, 0, tmp_tuple_element_21);
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1160];
            tmp_called_value_86 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(tstate);
            if (unlikely(tmp_called_value_86 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[11]);
            }

            if (tmp_called_value_86 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 2122;

                goto dict_build_exception_4;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 2122;
            tmp_tuple_element_22 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_86, mod_consts[1161]);

            if (tmp_tuple_element_22 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 2122;

                goto dict_build_exception_4;
            }
            tmp_dict_value_4 = MAKE_TUPLE_EMPTY(tstate, 3);
            PyTuple_SET_ITEM(tmp_dict_value_4, 0, tmp_tuple_element_22);
            tmp_tuple_element_22 = mod_consts[289];
            PyTuple_SET_ITEM0(tmp_dict_value_4, 1, tmp_tuple_element_22);
            tmp_tuple_element_22 = DICT_COPY(tstate, mod_consts[1162]);
            PyTuple_SET_ITEM(tmp_dict_value_4, 2, tmp_tuple_element_22);
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1163];
            tmp_dict_value_4 = mod_consts[1164];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1165];
            tmp_dict_value_4 = mod_consts[608];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1166];
            tmp_dict_value_4 = mod_consts[603];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1167];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1168], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1169];
            tmp_dict_value_4 = mod_consts[1170];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1171];
            tmp_dict_value_4 = mod_consts[591];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1172];
            tmp_dict_value_4 = mod_consts[1173];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1174];
            tmp_dict_value_4 = mod_consts[1175];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1176];
            tmp_dict_value_4 = mod_consts[608];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1177];
            tmp_dict_value_4 = mod_consts[1178];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1179];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1180], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1181];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1182], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1183];
            tmp_dict_value_4 = mod_consts[448];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1184];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1185], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1186];
            tmp_dict_value_4 = mod_consts[448];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1187];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[728], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1188];
            tmp_dict_value_4 = mod_consts[591];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1189];
            tmp_dict_value_4 = mod_consts[438];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1190];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1191], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1192];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[570], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1193];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1194], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1195];
            tmp_dict_value_4 = mod_consts[591];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1196];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1197], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1198];
            tmp_dict_value_4 = mod_consts[1199];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1200];
            tmp_dict_value_4 = mod_consts[1154];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1201];
            tmp_dict_value_4 = mod_consts[1202];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1203];
            tmp_dict_value_4 = mod_consts[610];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1204];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1205], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1206];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1207], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1208];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1209], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1210];
            tmp_dict_value_4 = mod_consts[1211];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1212];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1213], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1214];
            tmp_dict_value_4 = mod_consts[1215];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1216];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1217], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1218];
            tmp_dict_value_4 = mod_consts[229];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1219];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1036], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1220];
            tmp_dict_value_4 = mod_consts[1221];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1222];
            tmp_dict_value_4 = mod_consts[1048];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1223];
            tmp_dict_value_4 = mod_consts[1224];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1225];
            tmp_dict_value_4 = mod_consts[358];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1226];
            tmp_dict_value_4 = mod_consts[221];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1227];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[325], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1228];
            tmp_dict_value_4 = mod_consts[455];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1229];
            tmp_dict_value_4 = mod_consts[685];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1230];
            tmp_dict_value_4 = mod_consts[1231];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1232];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1233], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1234];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1235], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1236];
            tmp_dict_value_4 = mod_consts[221];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1237];
            tmp_dict_value_4 = mod_consts[1238];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1239];
            tmp_dict_value_4 = mod_consts[1141];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1240];
            tmp_dict_value_4 = mod_consts[221];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1241];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1242], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1243];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[803], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1244];
            tmp_dict_value_4 = mod_consts[259];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1245];
            tmp_dict_value_4 = mod_consts[605];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1246];
            tmp_dict_value_4 = mod_consts[221];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1247];
            tmp_dict_value_4 = mod_consts[1248];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1249];
            tmp_dict_value_4 = mod_consts[1231];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1250];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1251], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1252];
            tmp_dict_value_4 = mod_consts[1253];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1254];
            tmp_dict_value_4 = mod_consts[608];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1255];
            tmp_dict_value_4 = mod_consts[409];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1256];
            tmp_dict_value_4 = mod_consts[1257];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1258];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1259], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1260];
            tmp_dict_value_4 = mod_consts[1261];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1262];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[960], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1263];
            tmp_dict_value_4 = mod_consts[501];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1264];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1265], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1266];
            tmp_dict_value_4 = mod_consts[617];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1267];
            tmp_dict_value_4 = mod_consts[221];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1268];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1269], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1270];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1271], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1272];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1273], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1274];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1275], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1276];
            tmp_called_value_87 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(tstate);
            if (unlikely(tmp_called_value_87 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[11]);
            }

            if (tmp_called_value_87 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 2382;

                goto dict_build_exception_4;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 2382;
            tmp_tuple_element_23 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_87, mod_consts[360]);

            if (tmp_tuple_element_23 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 2382;

                goto dict_build_exception_4;
            }
            tmp_dict_value_4 = MAKE_TUPLE_EMPTY(tstate, 1);
            PyTuple_SET_ITEM(tmp_dict_value_4, 0, tmp_tuple_element_23);
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1277];
            tmp_dict_value_4 = mod_consts[1278];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1279];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1280], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1281];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1282], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1283];
            tmp_dict_value_4 = mod_consts[848];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1284];
            tmp_dict_value_4 = mod_consts[1285];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1286];
            tmp_dict_value_4 = mod_consts[1287];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1288];
            tmp_dict_value_4 = mod_consts[1289];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1290];
            tmp_dict_value_4 = mod_consts[229];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1291];
            tmp_dict_value_4 = mod_consts[221];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1292];
            tmp_dict_value_4 = mod_consts[1104];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1293];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1294], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1295];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1296], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1297];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1298], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1299];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[570], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1300];
            tmp_dict_value_4 = mod_consts[1301];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1302];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1303], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1304];
            tmp_dict_value_4 = mod_consts[229];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1305];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1306], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1307];
            tmp_dict_value_4 = mod_consts[1308];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1309];
            tmp_dict_value_4 = mod_consts[352];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1310];
            tmp_dict_value_4 = mod_consts[229];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1311];
            tmp_dict_value_4 = mod_consts[205];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1312];
            tmp_dict_value_4 = mod_consts[205];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1313];
            tmp_dict_value_4 = mod_consts[1314];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1315];
            tmp_dict_value_4 = mod_consts[1316];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1317];
            tmp_dict_value_4 = mod_consts[1318];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1319];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1320], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1321];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1322], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1323];
            tmp_dict_value_4 = mod_consts[1324];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1325];
            tmp_dict_value_4 = mod_consts[514];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1326];
            tmp_dict_value_4 = mod_consts[1327];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1328];
            tmp_dict_value_4 = mod_consts[1329];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1330];
            tmp_dict_value_4 = mod_consts[259];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1331];
            tmp_dict_value_4 = mod_consts[1332];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1333];
            tmp_dict_value_4 = mod_consts[1334];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1335];
            tmp_dict_value_4 = mod_consts[1132];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1336];
            tmp_dict_value_4 = mod_consts[617];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1337];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1338], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1339];
            tmp_dict_value_4 = mod_consts[205];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1340];
            tmp_dict_value_4 = mod_consts[346];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1341];
            tmp_dict_value_4 = mod_consts[1215];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1342];
            tmp_dict_value_4 = mod_consts[205];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1343];
            tmp_dict_value_4 = mod_consts[1344];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1345];
            tmp_dict_value_4 = mod_consts[358];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1346];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[403], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1347];
            tmp_dict_value_4 = mod_consts[1348];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1349];
            tmp_dict_value_4 = mod_consts[205];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1350];
            tmp_dict_value_4 = mod_consts[1351];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1352];
            tmp_dict_value_4 = mod_consts[608];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1353];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1354], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1355];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1138], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1356];
            tmp_dict_value_4 = mod_consts[243];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1357];
            tmp_dict_value_4 = mod_consts[745];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1358];
            tmp_dict_value_4 = mod_consts[462];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1359];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1360], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1361];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1362], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1363];
            tmp_dict_value_4 = mod_consts[462];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1364];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1365], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1366];
            tmp_dict_value_4 = mod_consts[1367];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1368];
            tmp_dict_value_4 = mod_consts[239];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1369];
            tmp_dict_value_4 = mod_consts[205];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1370];
            tmp_dict_value_4 = mod_consts[568];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1371];
            tmp_dict_value_4 = mod_consts[448];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1372];
            tmp_dict_value_4 = mod_consts[448];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1373];
            tmp_dict_value_4 = mod_consts[1374];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1375];
            tmp_dict_value_4 = mod_consts[685];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1376];
            tmp_dict_value_4 = mod_consts[221];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1377];
            tmp_dict_value_4 = mod_consts[1378];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1379];
            tmp_dict_value_4 = mod_consts[1380];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1381];
            tmp_dict_value_4 = mod_consts[314];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1382];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1383], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1384];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1385], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1386];
            tmp_dict_value_4 = mod_consts[358];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1387];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[570], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1388];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1389], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1390];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1391], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1392];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1393], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1394];
            tmp_dict_value_4 = mod_consts[1395];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1396];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[570], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1397];
            tmp_dict_value_4 = mod_consts[1398];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1399];
            tmp_dict_value_4 = mod_consts[409];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1400];
            tmp_dict_value_4 = mod_consts[1401];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1402];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[407], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1403];
            tmp_dict_value_4 = mod_consts[535];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1404];
            tmp_called_value_88 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$selAorI(tstate);
            if (unlikely(tmp_called_value_88 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[11]);
            }

            if (tmp_called_value_88 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 2640;

                goto dict_build_exception_4;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 2640;
            tmp_tuple_element_24 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_88, mod_consts[1405]);

            if (tmp_tuple_element_24 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 2640;

                goto dict_build_exception_4;
            }
            tmp_dict_value_4 = MAKE_TUPLE_EMPTY(tstate, 1);
            PyTuple_SET_ITEM(tmp_dict_value_4, 0, tmp_tuple_element_24);
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1406];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[570], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1407];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1408], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1409];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[207], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1410];
            tmp_dict_value_4 = mod_consts[205];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1411];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1412], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1413];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1414], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1415];
            tmp_dict_value_4 = mod_consts[229];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1416];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[714], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1417];
            tmp_dict_value_4 = mod_consts[603];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1418];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1419], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1420];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1421], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1422];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1423], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1424];
            tmp_dict_value_4 = mod_consts[1057];
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            assert(!(tmp_res != 0));
            tmp_dict_key_4 = mod_consts[1425];
            tmp_dict_value_4 = DEEP_COPY_TUPLE_GUIDED(tstate, mod_consts[1426], "iiD");
            tmp_res = PyDict_SetItem(tmp_assign_source_13, tmp_dict_key_4, tmp_dict_value_4);
            Py_DECREF(tmp_dict_value_4);
            assert(!(tmp_res != 0));
        }
        goto dict_build_noexception_4;
        // Exception handling pass through code for dict_build:
        dict_build_exception_4:;
        Py_DECREF(tmp_assign_source_13);
        goto frame_exception_exit_1;
        // Finished with no exception for dict_build:
        dict_build_noexception_4:;
        UPDATE_STRING_DICT1(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[1427], tmp_assign_source_13);
    }
    {
        PyObject *tmp_assign_source_14;
        tmp_assign_source_14 = DICT_COPY(tstate, mod_consts[1428]);
        UPDATE_STRING_DICT1(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[1429], tmp_assign_source_14);
    }
    {
        PyObject *tmp_assign_source_15;
        tmp_assign_source_15 = LIST_COPY(tstate, mod_consts[1430]);
        UPDATE_STRING_DICT1(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[1431], tmp_assign_source_15);
    }
    {
        PyObject *tmp_called_value_89;
        PyObject *tmp_expression_value_5;
        PyObject *tmp_call_result_4;
        PyObject *tmp_args_element_value_3;
        PyObject *tmp_dict_key_5;
        PyObject *tmp_dict_value_5;
        PyObject *tmp_called_instance_15;
        tmp_expression_value_5 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$misc(tstate);
        if (unlikely(tmp_expression_value_5 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[12]);
        }

        if (tmp_expression_value_5 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 2796;

            goto frame_exception_exit_1;
        }
        tmp_called_value_89 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_5, mod_consts[70]);
        if (tmp_called_value_89 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 2796;

            goto frame_exception_exit_1;
        }
        tmp_dict_key_5 = mod_consts[1432];
        tmp_called_instance_15 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
        if (unlikely(tmp_called_instance_15 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
        }

        if (tmp_called_instance_15 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));

            Py_DECREF(tmp_called_value_89);

            exception_lineno = 2798;

            goto frame_exception_exit_1;
        }
        frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 2798;
        tmp_dict_value_5 = CALL_METHOD_WITH_ARGS2(
            tstate,
            tmp_called_instance_15,
            mod_consts[1433],
            &PyTuple_GET_ITEM(mod_consts[1434], 0)
        );

        if (tmp_dict_value_5 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);
            Py_DECREF(tmp_called_value_89);

            exception_lineno = 2798;

            goto frame_exception_exit_1;
        }
        tmp_args_element_value_3 = _PyDict_NewPresized( 11 );
        {
            PyObject *tmp_called_instance_16;
            PyObject *tmp_called_instance_17;
            PyObject *tmp_called_instance_18;
            PyObject *tmp_called_instance_19;
            PyObject *tmp_called_instance_20;
            PyObject *tmp_called_instance_21;
            PyObject *tmp_called_instance_22;
            PyObject *tmp_called_instance_23;
            PyObject *tmp_called_instance_24;
            PyObject *tmp_called_instance_25;
            tmp_res = PyDict_SetItem(tmp_args_element_value_3, tmp_dict_key_5, tmp_dict_value_5);
            Py_DECREF(tmp_dict_value_5);
            assert(!(tmp_res != 0));
            tmp_dict_key_5 = mod_consts[1435];
            tmp_called_instance_16 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
            if (unlikely(tmp_called_instance_16 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_called_instance_16 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 2801;

                goto dict_build_exception_5;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 2801;
            tmp_dict_value_5 = CALL_METHOD_WITH_ARGS2(
                tstate,
                tmp_called_instance_16,
                mod_consts[1433],
                &PyTuple_GET_ITEM(mod_consts[1436], 0)
            );

            if (tmp_dict_value_5 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 2801;

                goto dict_build_exception_5;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_3, tmp_dict_key_5, tmp_dict_value_5);
            Py_DECREF(tmp_dict_value_5);
            assert(!(tmp_res != 0));
            tmp_dict_key_5 = mod_consts[1437];
            tmp_called_instance_17 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
            if (unlikely(tmp_called_instance_17 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_called_instance_17 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 2804;

                goto dict_build_exception_5;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 2804;
            tmp_dict_value_5 = CALL_METHOD_WITH_ARGS2(
                tstate,
                tmp_called_instance_17,
                mod_consts[1433],
                &PyTuple_GET_ITEM(mod_consts[1438], 0)
            );

            if (tmp_dict_value_5 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 2804;

                goto dict_build_exception_5;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_3, tmp_dict_key_5, tmp_dict_value_5);
            Py_DECREF(tmp_dict_value_5);
            assert(!(tmp_res != 0));
            tmp_dict_key_5 = mod_consts[1439];
            tmp_called_instance_18 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
            if (unlikely(tmp_called_instance_18 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_called_instance_18 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 2807;

                goto dict_build_exception_5;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 2807;
            tmp_dict_value_5 = CALL_METHOD_WITH_ARGS2(
                tstate,
                tmp_called_instance_18,
                mod_consts[1433],
                &PyTuple_GET_ITEM(mod_consts[1440], 0)
            );

            if (tmp_dict_value_5 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 2807;

                goto dict_build_exception_5;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_3, tmp_dict_key_5, tmp_dict_value_5);
            Py_DECREF(tmp_dict_value_5);
            assert(!(tmp_res != 0));
            tmp_dict_key_5 = mod_consts[1441];
            tmp_called_instance_19 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
            if (unlikely(tmp_called_instance_19 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_called_instance_19 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 2810;

                goto dict_build_exception_5;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 2810;
            tmp_dict_value_5 = CALL_METHOD_WITH_ARGS2(
                tstate,
                tmp_called_instance_19,
                mod_consts[1433],
                &PyTuple_GET_ITEM(mod_consts[1442], 0)
            );

            if (tmp_dict_value_5 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 2810;

                goto dict_build_exception_5;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_3, tmp_dict_key_5, tmp_dict_value_5);
            Py_DECREF(tmp_dict_value_5);
            assert(!(tmp_res != 0));
            tmp_dict_key_5 = mod_consts[1443];
            tmp_called_instance_20 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
            if (unlikely(tmp_called_instance_20 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_called_instance_20 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 2811;

                goto dict_build_exception_5;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 2811;
            tmp_dict_value_5 = CALL_METHOD_WITH_ARGS2(
                tstate,
                tmp_called_instance_20,
                mod_consts[1433],
                &PyTuple_GET_ITEM(mod_consts[1444], 0)
            );

            if (tmp_dict_value_5 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 2811;

                goto dict_build_exception_5;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_3, tmp_dict_key_5, tmp_dict_value_5);
            Py_DECREF(tmp_dict_value_5);
            assert(!(tmp_res != 0));
            tmp_dict_key_5 = mod_consts[1445];
            tmp_called_instance_21 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
            if (unlikely(tmp_called_instance_21 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_called_instance_21 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 2814;

                goto dict_build_exception_5;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 2814;
            tmp_dict_value_5 = CALL_METHOD_WITH_ARGS2(
                tstate,
                tmp_called_instance_21,
                mod_consts[1433],
                &PyTuple_GET_ITEM(mod_consts[1446], 0)
            );

            if (tmp_dict_value_5 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 2814;

                goto dict_build_exception_5;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_3, tmp_dict_key_5, tmp_dict_value_5);
            Py_DECREF(tmp_dict_value_5);
            assert(!(tmp_res != 0));
            tmp_dict_key_5 = mod_consts[1447];
            tmp_called_instance_22 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
            if (unlikely(tmp_called_instance_22 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_called_instance_22 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 2817;

                goto dict_build_exception_5;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 2817;
            tmp_dict_value_5 = CALL_METHOD_WITH_ARGS2(
                tstate,
                tmp_called_instance_22,
                mod_consts[1433],
                &PyTuple_GET_ITEM(mod_consts[1448], 0)
            );

            if (tmp_dict_value_5 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 2817;

                goto dict_build_exception_5;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_3, tmp_dict_key_5, tmp_dict_value_5);
            Py_DECREF(tmp_dict_value_5);
            assert(!(tmp_res != 0));
            tmp_dict_key_5 = mod_consts[1449];
            tmp_called_instance_23 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
            if (unlikely(tmp_called_instance_23 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_called_instance_23 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 2820;

                goto dict_build_exception_5;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 2820;
            tmp_dict_value_5 = CALL_METHOD_WITH_ARGS2(
                tstate,
                tmp_called_instance_23,
                mod_consts[1433],
                &PyTuple_GET_ITEM(mod_consts[1450], 0)
            );

            if (tmp_dict_value_5 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 2820;

                goto dict_build_exception_5;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_3, tmp_dict_key_5, tmp_dict_value_5);
            Py_DECREF(tmp_dict_value_5);
            assert(!(tmp_res != 0));
            tmp_dict_key_5 = mod_consts[1451];
            tmp_called_instance_24 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
            if (unlikely(tmp_called_instance_24 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_called_instance_24 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 2823;

                goto dict_build_exception_5;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 2823;
            tmp_dict_value_5 = CALL_METHOD_WITH_ARGS2(
                tstate,
                tmp_called_instance_24,
                mod_consts[1433],
                &PyTuple_GET_ITEM(mod_consts[1452], 0)
            );

            if (tmp_dict_value_5 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 2823;

                goto dict_build_exception_5;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_3, tmp_dict_key_5, tmp_dict_value_5);
            Py_DECREF(tmp_dict_value_5);
            assert(!(tmp_res != 0));
            tmp_dict_key_5 = mod_consts[1453];
            tmp_called_instance_25 = module_var_accessor_Quartz$$36$CoreGraphics$$36$_metadata$objc(tstate);
            if (unlikely(tmp_called_instance_25 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_called_instance_25 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 2826;

                goto dict_build_exception_5;
            }
            frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 2826;
            tmp_dict_value_5 = CALL_METHOD_WITH_ARGS2(
                tstate,
                tmp_called_instance_25,
                mod_consts[1433],
                &PyTuple_GET_ITEM(mod_consts[1454], 0)
            );

            if (tmp_dict_value_5 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 2826;

                goto dict_build_exception_5;
            }
            tmp_res = PyDict_SetItem(tmp_args_element_value_3, tmp_dict_key_5, tmp_dict_value_5);
            Py_DECREF(tmp_dict_value_5);
            assert(!(tmp_res != 0));
        }
        goto dict_build_noexception_5;
        // Exception handling pass through code for dict_build:
        dict_build_exception_5:;
        Py_DECREF(tmp_called_value_89);
        Py_DECREF(tmp_args_element_value_3);
        goto frame_exception_exit_1;
        // Finished with no exception for dict_build:
        dict_build_noexception_5:;
        frame_frame_Quartz$CoreGraphics$_metadata->m_frame.f_lineno = 2796;
        tmp_call_result_4 = CALL_FUNCTION_WITH_SINGLE_ARG(tstate, tmp_called_value_89, tmp_args_element_value_3);
        Py_DECREF(tmp_called_value_89);
        Py_DECREF(tmp_args_element_value_3);
        if (tmp_call_result_4 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 2796;

            goto frame_exception_exit_1;
        }
        Py_DECREF(tmp_call_result_4);
    }


    // Put the previous frame back on top.
    popFrameStack(tstate);

    goto frame_no_exception_1;
    frame_exception_exit_1:


    {
        PyTracebackObject *exception_tb = GET_EXCEPTION_STATE_TRACEBACK(&exception_state);
        if (exception_tb == NULL) {
            exception_tb = MAKE_TRACEBACK(frame_frame_Quartz$CoreGraphics$_metadata, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        } else if (exception_tb->tb_frame != &frame_frame_Quartz$CoreGraphics$_metadata->m_frame) {
            exception_tb = ADD_TRACEBACK(exception_tb, frame_frame_Quartz$CoreGraphics$_metadata, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        }
    }



    assertFrameObject(frame_frame_Quartz$CoreGraphics$_metadata);

    // Put the previous frame back on top.
    popFrameStack(tstate);

    // Return the error.
    goto module_exception_exit;
    frame_no_exception_1:;
    {
        PyObject *tmp_assign_source_16;
        tmp_assign_source_16 = DICT_COPY(tstate, mod_consts[1455]);
        UPDATE_STRING_DICT1(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)mod_consts[1456], tmp_assign_source_16);
    }

    // Report to PGO about leaving the module without error.
    PGO_onModuleExit("Quartz$CoreGraphics$_metadata", false);

#if _NUITKA_MODULE_MODE && 0
    {
        PyObject *post_load = IMPORT_EMBEDDED_MODULE(tstate, "Quartz.CoreGraphics._metadata" "-postLoad");
        if (post_load == NULL) {
            return NULL;
        }
    }
#endif

    Py_INCREF(module_Quartz$CoreGraphics$_metadata);
    return module_Quartz$CoreGraphics$_metadata;
    module_exception_exit:

#if _NUITKA_MODULE_MODE && 0
    {
        PyObject *module_name = GET_STRING_DICT_VALUE(moduledict_Quartz$CoreGraphics$_metadata, (Nuitka_StringObject *)const_str_plain___name__);

        if (module_name != NULL) {
            Nuitka_DelModule(tstate, module_name);
        }
    }
#endif
    PGO_onModuleExit("Quartz$CoreGraphics$_metadata", false);

    RESTORE_ERROR_OCCURRED_STATE(tstate, &exception_state);
    return NULL;
}
