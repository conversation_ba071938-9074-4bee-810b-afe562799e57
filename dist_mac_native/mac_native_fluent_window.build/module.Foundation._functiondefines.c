/* Generated code for Python module 'Foundation$_functiondefines'
 * created by Nuitka version 2.7.5
 *
 * This code is in part copyright 2025 <PERSON>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "nuitka/prelude.h"

#include "nuitka/unfreezing.h"

#include "__helpers.h"

/* The "module_Foundation$_functiondefines" is a Python object pointer of module type.
 *
 * Note: For full compatibility with CPython, every module variable access
 * needs to go through it except for cases where the module cannot possibly
 * have changed in the mean time.
 */

PyObject *module_Foundation$_functiondefines;
PyDictObject *moduledict_Foundation$_functiondefines;

/* The declarations of module constants used, if any. */
static PyObject *mod_consts[33];
#ifndef __NUITKA_NO_ASSERT__
static Py_hash_t mod_consts_hash[33];
#endif

static PyObject *module_filename_obj = NULL;

/* Indicator if this modules private constants were created yet. */
static bool constants_created = false;

/* Function to create module private constants. */
static void createModuleConstants(PyThreadState *tstate) {
    if (constants_created == false) {
        loadConstantsBlob(tstate, &mod_consts[0], UN_TRANSLATE("Foundation._functiondefines"));
        constants_created = true;

#ifndef __NUITKA_NO_ASSERT__
        for (int i = 0; i < 33; i++) {
            mod_consts_hash[i] = DEEP_HASH(tstate, mod_consts[i]);
        }
#endif
    }
}

// We want to be able to initialize the "__main__" constants in any case.
#if 0
void createMainModuleConstants(PyThreadState *tstate) {
    createModuleConstants(tstate);
}
#endif

/* Function to verify module private constants for non-corruption. */
#ifndef __NUITKA_NO_ASSERT__
void checkModuleConstants_Foundation$_functiondefines(PyThreadState *tstate) {
    // The module may not have been used at all, then ignore this.
    if (constants_created == false) return;

    for (int i = 0; i < 33; i++) {
        assert(mod_consts_hash[i] == DEEP_HASH(tstate, mod_consts[i]));
        CHECK_OBJECT_DEEP(mod_consts[i]);
    }
}
#endif

// Helper to preserving module variables for Python3.11+
#if 2
#if PYTHON_VERSION >= 0x3c0
NUITKA_MAY_BE_UNUSED static uint32_t _Nuitka_PyDictKeys_GetVersionForCurrentState(PyInterpreterState *interp, PyDictKeysObject *dk)
{
    if (dk->dk_version != 0) {
        return dk->dk_version;
    }
    uint32_t result = interp->dict_state.next_keys_version++;
    dk->dk_version = result;
    return result;
}
#elif PYTHON_VERSION >= 0x3b0
static uint32_t _Nuitka_next_dict_keys_version = 2;

NUITKA_MAY_BE_UNUSED static uint32_t _Nuitka_PyDictKeys_GetVersionForCurrentState(PyDictKeysObject *dk)
{
    if (dk->dk_version != 0) {
        return dk->dk_version;
    }
    uint32_t result = _Nuitka_next_dict_keys_version++;
    dk->dk_version = result;
    return result;
}
#endif
#endif

// Accessors to module variables.
static PyObject *module_var_accessor_Foundation$$36$_functiondefines$_Foundation(PyThreadState *tstate) {
#if 1
    PyObject *result;

#if PYTHON_VERSION < 0x3b0
    static uint64_t dict_version = 0;
    static PyObject *cache_value = NULL;

    if (moduledict_Foundation$_functiondefines->ma_version_tag == dict_version) {
        CHECK_OBJECT_X(cache_value);
        result = cache_value;
    } else {
        dict_version = moduledict_Foundation$_functiondefines->ma_version_tag;

        result = GET_STRING_DICT_VALUE(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)mod_consts[0]);
        cache_value = result;
    }
#else
    static uint32_t dict_keys_version = 0xFFFFFFFF;
    static Py_ssize_t cache_dk_index = 0;

    PyDictKeysObject *dk = moduledict_Foundation$_functiondefines->ma_keys;
    if (likely(DK_IS_UNICODE(dk))) {

#if PYTHON_VERSION >= 0x3c0
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(tstate->interp, dk);
#else
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(dk);
#endif

        if (current_dk_version != dict_keys_version) {
            dict_keys_version = current_dk_version;
            Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[0]);
            assert(hash != -1);

            cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[0], hash);
        }

        if (cache_dk_index >= 0) {
            assert(dk->dk_kind != DICT_KEYS_SPLIT);

            PyDictUnicodeEntry *entries = DK_UNICODE_ENTRIES(dk);

            result = entries[cache_dk_index].me_value;

            if (unlikely(result == NULL)) {
                Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[0]);
                assert(hash != -1);

                cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[0], hash);

                if (cache_dk_index >= 0) {
                    result = entries[cache_dk_index].me_value;
                }
            }
        } else {
            result = NULL;
        }
    } else {
        result = GET_STRING_DICT_VALUE(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)mod_consts[0]);
    }
#endif

#else
    PyObject *result = GET_STRING_DICT_VALUE(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)mod_consts[0]);
#endif

    if (unlikely(result == NULL)) {
        result = GET_STRING_DICT_VALUE(dict_builtin, (Nuitka_StringObject *)mod_consts[0]);
    }

    return result;
}

static PyObject *module_var_accessor_Foundation$$36$_functiondefines$__spec__(PyThreadState *tstate) {
#if 0
    PyObject *result;

#if PYTHON_VERSION < 0x3b0
    static uint64_t dict_version = 0;
    static PyObject *cache_value = NULL;

    if (moduledict_Foundation$_functiondefines->ma_version_tag == dict_version) {
        CHECK_OBJECT_X(cache_value);
        result = cache_value;
    } else {
        dict_version = moduledict_Foundation$_functiondefines->ma_version_tag;

        result = GET_STRING_DICT_VALUE(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)mod_consts[32]);
        cache_value = result;
    }
#else
    static uint32_t dict_keys_version = 0xFFFFFFFF;
    static Py_ssize_t cache_dk_index = 0;

    PyDictKeysObject *dk = moduledict_Foundation$_functiondefines->ma_keys;
    if (likely(DK_IS_UNICODE(dk))) {

#if PYTHON_VERSION >= 0x3c0
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(tstate->interp, dk);
#else
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(dk);
#endif

        if (current_dk_version != dict_keys_version) {
            dict_keys_version = current_dk_version;
            Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[32]);
            assert(hash != -1);

            cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[32], hash);
        }

        if (cache_dk_index >= 0) {
            assert(dk->dk_kind != DICT_KEYS_SPLIT);

            PyDictUnicodeEntry *entries = DK_UNICODE_ENTRIES(dk);

            result = entries[cache_dk_index].me_value;

            if (unlikely(result == NULL)) {
                Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[32]);
                assert(hash != -1);

                cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[32], hash);

                if (cache_dk_index >= 0) {
                    result = entries[cache_dk_index].me_value;
                }
            }
        } else {
            result = NULL;
        }
    } else {
        result = GET_STRING_DICT_VALUE(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)mod_consts[32]);
    }
#endif

#else
    PyObject *result = GET_STRING_DICT_VALUE(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)mod_consts[32]);
#endif

    if (unlikely(result == NULL)) {
        result = GET_STRING_DICT_VALUE(dict_builtin, (Nuitka_StringObject *)mod_consts[32]);
    }

    return result;
}


#if !defined(_NUITKA_EXPERIMENTAL_NEW_CODE_OBJECTS)
// The module code objects.
static PyCodeObject *code_objects_537c01b3428486143a3021263f2b2bbb;
static PyCodeObject *code_objects_9b59f8c9df2709f187a064c7ddc82bdd;
static PyCodeObject *code_objects_631961034f3de6340bae8cc03d50fbca;
static PyCodeObject *code_objects_4115cb562da719834ffab329b3033ac0;
static PyCodeObject *code_objects_9b4c5f6b5519eaf6735cbe6ebdcb8362;
static PyCodeObject *code_objects_f62dc5309fe296676192c3873b801af2;
static PyCodeObject *code_objects_1d0b2877beefde48d485808a30060640;
static PyCodeObject *code_objects_bcdc77864900a2c1fd6c9b0162803611;
static PyCodeObject *code_objects_41832c82cd0798a3261f1f6f6b884c89;
static PyCodeObject *code_objects_6d40c2d060afb3d398dea5db151bd651;
static PyCodeObject *code_objects_2ebfd55b853d64cfb6381839f74ed3e0;

static void createModuleCodeObjects(void) {
    module_filename_obj = MAKE_RELATIVE_PATH(mod_consts[25]); CHECK_OBJECT(module_filename_obj);
    code_objects_537c01b3428486143a3021263f2b2bbb = MAKE_CODE_OBJECT(module_filename_obj, 1, 0, mod_consts[26], mod_consts[26], NULL, NULL, 0, 0, 0);
    code_objects_9b59f8c9df2709f187a064c7ddc82bdd = MAKE_CODE_OBJECT(module_filename_obj, 57, CO_OPTIMIZED | CO_NEWLOCALS, mod_consts[22], mod_consts[22], mod_consts[27], NULL, 2, 0, 0);
    code_objects_631961034f3de6340bae8cc03d50fbca = MAKE_CODE_OBJECT(module_filename_obj, 50, CO_OPTIMIZED | CO_NEWLOCALS, mod_consts[21], mod_consts[21], mod_consts[27], NULL, 2, 0, 0);
    code_objects_4115cb562da719834ffab329b3033ac0 = MAKE_CODE_OBJECT(module_filename_obj, 28, CO_OPTIMIZED | CO_NEWLOCALS, mod_consts[17], mod_consts[17], mod_consts[28], NULL, 2, 0, 0);
    code_objects_9b4c5f6b5519eaf6735cbe6ebdcb8362 = MAKE_CODE_OBJECT(module_filename_obj, 36, CO_OPTIMIZED | CO_NEWLOCALS, mod_consts[18], mod_consts[18], mod_consts[29], NULL, 3, 0, 0);
    code_objects_f62dc5309fe296676192c3873b801af2 = MAKE_CODE_OBJECT(module_filename_obj, 42, CO_OPTIMIZED | CO_NEWLOCALS, mod_consts[19], mod_consts[19], mod_consts[30], NULL, 4, 0, 0);
    code_objects_1d0b2877beefde48d485808a30060640 = MAKE_CODE_OBJECT(module_filename_obj, 46, CO_OPTIMIZED | CO_NEWLOCALS, mod_consts[20], mod_consts[20], mod_consts[31], NULL, 5, 0, 0);
    code_objects_bcdc77864900a2c1fd6c9b0162803611 = MAKE_CODE_OBJECT(module_filename_obj, 8, CO_OPTIMIZED | CO_NEWLOCALS, mod_consts[13], mod_consts[13], mod_consts[28], NULL, 2, 0, 0);
    code_objects_41832c82cd0798a3261f1f6f6b884c89 = MAKE_CODE_OBJECT(module_filename_obj, 14, CO_OPTIMIZED | CO_NEWLOCALS, mod_consts[14], mod_consts[14], mod_consts[29], NULL, 3, 0, 0);
    code_objects_6d40c2d060afb3d398dea5db151bd651 = MAKE_CODE_OBJECT(module_filename_obj, 20, CO_OPTIMIZED | CO_NEWLOCALS, mod_consts[15], mod_consts[15], mod_consts[30], NULL, 4, 0, 0);
    code_objects_2ebfd55b853d64cfb6381839f74ed3e0 = MAKE_CODE_OBJECT(module_filename_obj, 24, CO_OPTIMIZED | CO_NEWLOCALS, mod_consts[16], mod_consts[16], mod_consts[31], NULL, 5, 0, 0);
}
#endif

// The module function declarations.
static PyObject *MAKE_FUNCTION_Foundation$_functiondefines$$$function__10_MAX(PyThreadState *tstate);


static PyObject *MAKE_FUNCTION_Foundation$_functiondefines$$$function__1_NSLocalizedString(PyThreadState *tstate);


static PyObject *MAKE_FUNCTION_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable(PyThreadState *tstate);


static PyObject *MAKE_FUNCTION_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle(PyThreadState *tstate);


static PyObject *MAKE_FUNCTION_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue(PyThreadState *tstate);


static PyObject *MAKE_FUNCTION_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString(PyThreadState *tstate);


static PyObject *MAKE_FUNCTION_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable(PyThreadState *tstate);


static PyObject *MAKE_FUNCTION_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle(PyThreadState *tstate);


static PyObject *MAKE_FUNCTION_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue(PyThreadState *tstate);


static PyObject *MAKE_FUNCTION_Foundation$_functiondefines$$$function__9_MIN(PyThreadState *tstate);


// The module function definitions.
static PyObject *impl_Foundation$_functiondefines$$$function__1_NSLocalizedString(PyThreadState *tstate, struct Nuitka_FunctionObject const *self, PyObject **python_pars) {
    // Preserve error status for checks
#ifndef __NUITKA_NO_ASSERT__
    NUITKA_MAY_BE_UNUSED bool had_error = HAS_ERROR_OCCURRED(tstate);
#endif

    // Local variable declarations.
    PyObject *par_key = python_pars[0];
    PyObject *par_comment = python_pars[1];
    struct Nuitka_FrameObject *frame_frame_Foundation$_functiondefines$$$function__1_NSLocalizedString;
    NUITKA_MAY_BE_UNUSED char const *type_description_1 = NULL;
    PyObject *tmp_return_value = NULL;
    struct Nuitka_ExceptionPreservationItem exception_state = Empty_Nuitka_ExceptionPreservationItem;
    NUITKA_MAY_BE_UNUSED int exception_lineno = 0;
    static struct Nuitka_FrameObject *cache_frame_frame_Foundation$_functiondefines$$$function__1_NSLocalizedString = NULL;

    // Actual function body.
    if (isFrameUnusable(cache_frame_frame_Foundation$_functiondefines$$$function__1_NSLocalizedString)) {
        Py_XDECREF(cache_frame_frame_Foundation$_functiondefines$$$function__1_NSLocalizedString);

#if _DEBUG_REFCOUNTS
        if (cache_frame_frame_Foundation$_functiondefines$$$function__1_NSLocalizedString == NULL) {
            count_active_frame_cache_instances += 1;
        } else {
            count_released_frame_cache_instances += 1;
        }
        count_allocated_frame_cache_instances += 1;
#endif
        cache_frame_frame_Foundation$_functiondefines$$$function__1_NSLocalizedString = MAKE_FUNCTION_FRAME(tstate, code_objects_bcdc77864900a2c1fd6c9b0162803611, module_Foundation$_functiondefines, sizeof(void *)+sizeof(void *));
#if _DEBUG_REFCOUNTS
    } else {
        count_hit_frame_cache_instances += 1;
#endif
    }

    assert(cache_frame_frame_Foundation$_functiondefines$$$function__1_NSLocalizedString->m_type_description == NULL);
    frame_frame_Foundation$_functiondefines$$$function__1_NSLocalizedString = cache_frame_frame_Foundation$_functiondefines$$$function__1_NSLocalizedString;

    // Push the new frame as the currently active one, and we should be exclusively
    // owning it.
    pushFrameStackCompiledFrame(tstate, frame_frame_Foundation$_functiondefines$$$function__1_NSLocalizedString);
    assert(Py_REFCNT(frame_frame_Foundation$_functiondefines$$$function__1_NSLocalizedString) == 2);

    // Framed code:
    {
        PyObject *tmp_called_instance_1;
        PyObject *tmp_called_instance_2;
        PyObject *tmp_expression_value_1;
        PyObject *tmp_args_element_value_1;
        PyObject *tmp_args_element_value_2;
        PyObject *tmp_args_element_value_3;
        tmp_expression_value_1 = module_var_accessor_Foundation$$36$_functiondefines$_Foundation(tstate);
        if (unlikely(tmp_expression_value_1 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[0]);
        }

        if (tmp_expression_value_1 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 9;
            type_description_1 = "oo";
            goto frame_exception_exit_1;
        }
        tmp_called_instance_2 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_1, mod_consts[1]);
        if (tmp_called_instance_2 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 9;
            type_description_1 = "oo";
            goto frame_exception_exit_1;
        }
        frame_frame_Foundation$_functiondefines$$$function__1_NSLocalizedString->m_frame.f_lineno = 9;
        tmp_called_instance_1 = CALL_METHOD_NO_ARGS(tstate, tmp_called_instance_2, mod_consts[2]);
        Py_DECREF(tmp_called_instance_2);
        if (tmp_called_instance_1 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 9;
            type_description_1 = "oo";
            goto frame_exception_exit_1;
        }
        CHECK_OBJECT(par_key);
        tmp_args_element_value_1 = par_key;
        tmp_args_element_value_2 = mod_consts[4];
        tmp_args_element_value_3 = Py_None;
        frame_frame_Foundation$_functiondefines$$$function__1_NSLocalizedString->m_frame.f_lineno = 9;
        {
            PyObject *call_args[] = {tmp_args_element_value_1, tmp_args_element_value_2, tmp_args_element_value_3};
            tmp_return_value = CALL_METHOD_WITH_ARGS3(
                tstate,
                tmp_called_instance_1,
                mod_consts[3],
                call_args
            );
        }

        Py_DECREF(tmp_called_instance_1);
        if (tmp_return_value == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 9;
            type_description_1 = "oo";
            goto frame_exception_exit_1;
        }
        goto frame_return_exit_1;
    }


    // Put the previous frame back on top.
    popFrameStack(tstate);

    goto frame_no_exception_1;
    frame_return_exit_1:

    // Put the previous frame back on top.
    popFrameStack(tstate);

    goto function_return_exit;
    frame_exception_exit_1:


    {
        PyTracebackObject *exception_tb = GET_EXCEPTION_STATE_TRACEBACK(&exception_state);
        if (exception_tb == NULL) {
            exception_tb = MAKE_TRACEBACK(frame_frame_Foundation$_functiondefines$$$function__1_NSLocalizedString, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        } else if (exception_tb->tb_frame != &frame_frame_Foundation$_functiondefines$$$function__1_NSLocalizedString->m_frame) {
            exception_tb = ADD_TRACEBACK(exception_tb, frame_frame_Foundation$_functiondefines$$$function__1_NSLocalizedString, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        }
    }

    // Attaches locals to frame if any.
    Nuitka_Frame_AttachLocals(
        frame_frame_Foundation$_functiondefines$$$function__1_NSLocalizedString,
        type_description_1,
        par_key,
        par_comment
    );


    // Release cached frame if used for exception.
    if (frame_frame_Foundation$_functiondefines$$$function__1_NSLocalizedString == cache_frame_frame_Foundation$_functiondefines$$$function__1_NSLocalizedString) {
#if _DEBUG_REFCOUNTS
        count_active_frame_cache_instances -= 1;
        count_released_frame_cache_instances += 1;
#endif
        Py_DECREF(cache_frame_frame_Foundation$_functiondefines$$$function__1_NSLocalizedString);
        cache_frame_frame_Foundation$_functiondefines$$$function__1_NSLocalizedString = NULL;
    }

    assertFrameObject(frame_frame_Foundation$_functiondefines$$$function__1_NSLocalizedString);

    // Put the previous frame back on top.
    popFrameStack(tstate);

    // Return the error.
    goto function_exception_exit;
    frame_no_exception_1:;

    NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
    return NULL;

function_exception_exit:
    CHECK_OBJECT(par_key);
    Py_DECREF(par_key);
    CHECK_OBJECT(par_comment);
    Py_DECREF(par_comment);
    CHECK_EXCEPTION_STATE(&exception_state);
    RESTORE_ERROR_OCCURRED_STATE(tstate, &exception_state);

    return NULL;

function_return_exit:
   // Function cleanup code if any.
    CHECK_OBJECT(par_key);
    Py_DECREF(par_key);
    CHECK_OBJECT(par_comment);
    Py_DECREF(par_comment);

   // Actual function exit with return value, making sure we did not make
   // the error status worse despite non-NULL return.
   CHECK_OBJECT(tmp_return_value);
   assert(had_error || !HAS_ERROR_OCCURRED(tstate));
   return tmp_return_value;
}


static PyObject *impl_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable(PyThreadState *tstate, struct Nuitka_FunctionObject const *self, PyObject **python_pars) {
    // Preserve error status for checks
#ifndef __NUITKA_NO_ASSERT__
    NUITKA_MAY_BE_UNUSED bool had_error = HAS_ERROR_OCCURRED(tstate);
#endif

    // Local variable declarations.
    PyObject *par_key = python_pars[0];
    PyObject *par_tbl = python_pars[1];
    PyObject *par_comment = python_pars[2];
    struct Nuitka_FrameObject *frame_frame_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable;
    NUITKA_MAY_BE_UNUSED char const *type_description_1 = NULL;
    PyObject *tmp_return_value = NULL;
    struct Nuitka_ExceptionPreservationItem exception_state = Empty_Nuitka_ExceptionPreservationItem;
    NUITKA_MAY_BE_UNUSED int exception_lineno = 0;
    static struct Nuitka_FrameObject *cache_frame_frame_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable = NULL;

    // Actual function body.
    if (isFrameUnusable(cache_frame_frame_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable)) {
        Py_XDECREF(cache_frame_frame_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable);

#if _DEBUG_REFCOUNTS
        if (cache_frame_frame_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable == NULL) {
            count_active_frame_cache_instances += 1;
        } else {
            count_released_frame_cache_instances += 1;
        }
        count_allocated_frame_cache_instances += 1;
#endif
        cache_frame_frame_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable = MAKE_FUNCTION_FRAME(tstate, code_objects_41832c82cd0798a3261f1f6f6b884c89, module_Foundation$_functiondefines, sizeof(void *)+sizeof(void *)+sizeof(void *));
#if _DEBUG_REFCOUNTS
    } else {
        count_hit_frame_cache_instances += 1;
#endif
    }

    assert(cache_frame_frame_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable->m_type_description == NULL);
    frame_frame_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable = cache_frame_frame_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable;

    // Push the new frame as the currently active one, and we should be exclusively
    // owning it.
    pushFrameStackCompiledFrame(tstate, frame_frame_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable);
    assert(Py_REFCNT(frame_frame_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable) == 2);

    // Framed code:
    {
        PyObject *tmp_called_instance_1;
        PyObject *tmp_called_instance_2;
        PyObject *tmp_expression_value_1;
        PyObject *tmp_args_element_value_1;
        PyObject *tmp_args_element_value_2;
        PyObject *tmp_args_element_value_3;
        tmp_expression_value_1 = module_var_accessor_Foundation$$36$_functiondefines$_Foundation(tstate);
        if (unlikely(tmp_expression_value_1 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[0]);
        }

        if (tmp_expression_value_1 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 15;
            type_description_1 = "ooo";
            goto frame_exception_exit_1;
        }
        tmp_called_instance_2 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_1, mod_consts[1]);
        if (tmp_called_instance_2 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 15;
            type_description_1 = "ooo";
            goto frame_exception_exit_1;
        }
        frame_frame_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable->m_frame.f_lineno = 15;
        tmp_called_instance_1 = CALL_METHOD_NO_ARGS(tstate, tmp_called_instance_2, mod_consts[2]);
        Py_DECREF(tmp_called_instance_2);
        if (tmp_called_instance_1 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 15;
            type_description_1 = "ooo";
            goto frame_exception_exit_1;
        }
        CHECK_OBJECT(par_key);
        tmp_args_element_value_1 = par_key;
        tmp_args_element_value_2 = mod_consts[4];
        CHECK_OBJECT(par_tbl);
        tmp_args_element_value_3 = par_tbl;
        frame_frame_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable->m_frame.f_lineno = 15;
        {
            PyObject *call_args[] = {tmp_args_element_value_1, tmp_args_element_value_2, tmp_args_element_value_3};
            tmp_return_value = CALL_METHOD_WITH_ARGS3(
                tstate,
                tmp_called_instance_1,
                mod_consts[3],
                call_args
            );
        }

        Py_DECREF(tmp_called_instance_1);
        if (tmp_return_value == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 15;
            type_description_1 = "ooo";
            goto frame_exception_exit_1;
        }
        goto frame_return_exit_1;
    }


    // Put the previous frame back on top.
    popFrameStack(tstate);

    goto frame_no_exception_1;
    frame_return_exit_1:

    // Put the previous frame back on top.
    popFrameStack(tstate);

    goto function_return_exit;
    frame_exception_exit_1:


    {
        PyTracebackObject *exception_tb = GET_EXCEPTION_STATE_TRACEBACK(&exception_state);
        if (exception_tb == NULL) {
            exception_tb = MAKE_TRACEBACK(frame_frame_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        } else if (exception_tb->tb_frame != &frame_frame_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable->m_frame) {
            exception_tb = ADD_TRACEBACK(exception_tb, frame_frame_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        }
    }

    // Attaches locals to frame if any.
    Nuitka_Frame_AttachLocals(
        frame_frame_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable,
        type_description_1,
        par_key,
        par_tbl,
        par_comment
    );


    // Release cached frame if used for exception.
    if (frame_frame_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable == cache_frame_frame_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable) {
#if _DEBUG_REFCOUNTS
        count_active_frame_cache_instances -= 1;
        count_released_frame_cache_instances += 1;
#endif
        Py_DECREF(cache_frame_frame_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable);
        cache_frame_frame_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable = NULL;
    }

    assertFrameObject(frame_frame_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable);

    // Put the previous frame back on top.
    popFrameStack(tstate);

    // Return the error.
    goto function_exception_exit;
    frame_no_exception_1:;

    NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
    return NULL;

function_exception_exit:
    CHECK_OBJECT(par_key);
    Py_DECREF(par_key);
    CHECK_OBJECT(par_tbl);
    Py_DECREF(par_tbl);
    CHECK_OBJECT(par_comment);
    Py_DECREF(par_comment);
    CHECK_EXCEPTION_STATE(&exception_state);
    RESTORE_ERROR_OCCURRED_STATE(tstate, &exception_state);

    return NULL;

function_return_exit:
   // Function cleanup code if any.
    CHECK_OBJECT(par_key);
    Py_DECREF(par_key);
    CHECK_OBJECT(par_tbl);
    Py_DECREF(par_tbl);
    CHECK_OBJECT(par_comment);
    Py_DECREF(par_comment);

   // Actual function exit with return value, making sure we did not make
   // the error status worse despite non-NULL return.
   CHECK_OBJECT(tmp_return_value);
   assert(had_error || !HAS_ERROR_OCCURRED(tstate));
   return tmp_return_value;
}


static PyObject *impl_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle(PyThreadState *tstate, struct Nuitka_FunctionObject const *self, PyObject **python_pars) {
    // Preserve error status for checks
#ifndef __NUITKA_NO_ASSERT__
    NUITKA_MAY_BE_UNUSED bool had_error = HAS_ERROR_OCCURRED(tstate);
#endif

    // Local variable declarations.
    PyObject *par_key = python_pars[0];
    PyObject *par_tbl = python_pars[1];
    PyObject *par_bundle = python_pars[2];
    PyObject *par_comment = python_pars[3];
    struct Nuitka_FrameObject *frame_frame_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle;
    NUITKA_MAY_BE_UNUSED char const *type_description_1 = NULL;
    PyObject *tmp_return_value = NULL;
    struct Nuitka_ExceptionPreservationItem exception_state = Empty_Nuitka_ExceptionPreservationItem;
    NUITKA_MAY_BE_UNUSED int exception_lineno = 0;
    static struct Nuitka_FrameObject *cache_frame_frame_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle = NULL;

    // Actual function body.
    if (isFrameUnusable(cache_frame_frame_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle)) {
        Py_XDECREF(cache_frame_frame_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle);

#if _DEBUG_REFCOUNTS
        if (cache_frame_frame_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle == NULL) {
            count_active_frame_cache_instances += 1;
        } else {
            count_released_frame_cache_instances += 1;
        }
        count_allocated_frame_cache_instances += 1;
#endif
        cache_frame_frame_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle = MAKE_FUNCTION_FRAME(tstate, code_objects_6d40c2d060afb3d398dea5db151bd651, module_Foundation$_functiondefines, sizeof(void *)+sizeof(void *)+sizeof(void *)+sizeof(void *));
#if _DEBUG_REFCOUNTS
    } else {
        count_hit_frame_cache_instances += 1;
#endif
    }

    assert(cache_frame_frame_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle->m_type_description == NULL);
    frame_frame_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle = cache_frame_frame_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle;

    // Push the new frame as the currently active one, and we should be exclusively
    // owning it.
    pushFrameStackCompiledFrame(tstate, frame_frame_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle);
    assert(Py_REFCNT(frame_frame_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle) == 2);

    // Framed code:
    {
        PyObject *tmp_called_instance_1;
        PyObject *tmp_args_element_value_1;
        PyObject *tmp_args_element_value_2;
        PyObject *tmp_args_element_value_3;
        CHECK_OBJECT(par_bundle);
        tmp_called_instance_1 = par_bundle;
        CHECK_OBJECT(par_key);
        tmp_args_element_value_1 = par_key;
        tmp_args_element_value_2 = mod_consts[4];
        CHECK_OBJECT(par_tbl);
        tmp_args_element_value_3 = par_tbl;
        frame_frame_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle->m_frame.f_lineno = 21;
        {
            PyObject *call_args[] = {tmp_args_element_value_1, tmp_args_element_value_2, tmp_args_element_value_3};
            tmp_return_value = CALL_METHOD_WITH_ARGS3(
                tstate,
                tmp_called_instance_1,
                mod_consts[3],
                call_args
            );
        }

        if (tmp_return_value == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 21;
            type_description_1 = "oooo";
            goto frame_exception_exit_1;
        }
        goto frame_return_exit_1;
    }


    // Put the previous frame back on top.
    popFrameStack(tstate);

    goto frame_no_exception_1;
    frame_return_exit_1:

    // Put the previous frame back on top.
    popFrameStack(tstate);

    goto function_return_exit;
    frame_exception_exit_1:


    {
        PyTracebackObject *exception_tb = GET_EXCEPTION_STATE_TRACEBACK(&exception_state);
        if (exception_tb == NULL) {
            exception_tb = MAKE_TRACEBACK(frame_frame_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        } else if (exception_tb->tb_frame != &frame_frame_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle->m_frame) {
            exception_tb = ADD_TRACEBACK(exception_tb, frame_frame_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        }
    }

    // Attaches locals to frame if any.
    Nuitka_Frame_AttachLocals(
        frame_frame_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle,
        type_description_1,
        par_key,
        par_tbl,
        par_bundle,
        par_comment
    );


    // Release cached frame if used for exception.
    if (frame_frame_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle == cache_frame_frame_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle) {
#if _DEBUG_REFCOUNTS
        count_active_frame_cache_instances -= 1;
        count_released_frame_cache_instances += 1;
#endif
        Py_DECREF(cache_frame_frame_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle);
        cache_frame_frame_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle = NULL;
    }

    assertFrameObject(frame_frame_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle);

    // Put the previous frame back on top.
    popFrameStack(tstate);

    // Return the error.
    goto function_exception_exit;
    frame_no_exception_1:;

    NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
    return NULL;

function_exception_exit:
    CHECK_OBJECT(par_key);
    Py_DECREF(par_key);
    CHECK_OBJECT(par_tbl);
    Py_DECREF(par_tbl);
    CHECK_OBJECT(par_bundle);
    Py_DECREF(par_bundle);
    CHECK_OBJECT(par_comment);
    Py_DECREF(par_comment);
    CHECK_EXCEPTION_STATE(&exception_state);
    RESTORE_ERROR_OCCURRED_STATE(tstate, &exception_state);

    return NULL;

function_return_exit:
   // Function cleanup code if any.
    CHECK_OBJECT(par_key);
    Py_DECREF(par_key);
    CHECK_OBJECT(par_tbl);
    Py_DECREF(par_tbl);
    CHECK_OBJECT(par_bundle);
    Py_DECREF(par_bundle);
    CHECK_OBJECT(par_comment);
    Py_DECREF(par_comment);

   // Actual function exit with return value, making sure we did not make
   // the error status worse despite non-NULL return.
   CHECK_OBJECT(tmp_return_value);
   assert(had_error || !HAS_ERROR_OCCURRED(tstate));
   return tmp_return_value;
}


static PyObject *impl_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue(PyThreadState *tstate, struct Nuitka_FunctionObject const *self, PyObject **python_pars) {
    // Preserve error status for checks
#ifndef __NUITKA_NO_ASSERT__
    NUITKA_MAY_BE_UNUSED bool had_error = HAS_ERROR_OCCURRED(tstate);
#endif

    // Local variable declarations.
    PyObject *par_key = python_pars[0];
    PyObject *par_tbl = python_pars[1];
    PyObject *par_bundle = python_pars[2];
    PyObject *par_val = python_pars[3];
    PyObject *par_comment = python_pars[4];
    struct Nuitka_FrameObject *frame_frame_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue;
    NUITKA_MAY_BE_UNUSED char const *type_description_1 = NULL;
    PyObject *tmp_return_value = NULL;
    struct Nuitka_ExceptionPreservationItem exception_state = Empty_Nuitka_ExceptionPreservationItem;
    NUITKA_MAY_BE_UNUSED int exception_lineno = 0;
    static struct Nuitka_FrameObject *cache_frame_frame_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue = NULL;

    // Actual function body.
    if (isFrameUnusable(cache_frame_frame_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue)) {
        Py_XDECREF(cache_frame_frame_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue);

#if _DEBUG_REFCOUNTS
        if (cache_frame_frame_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue == NULL) {
            count_active_frame_cache_instances += 1;
        } else {
            count_released_frame_cache_instances += 1;
        }
        count_allocated_frame_cache_instances += 1;
#endif
        cache_frame_frame_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue = MAKE_FUNCTION_FRAME(tstate, code_objects_2ebfd55b853d64cfb6381839f74ed3e0, module_Foundation$_functiondefines, sizeof(void *)+sizeof(void *)+sizeof(void *)+sizeof(void *)+sizeof(void *));
#if _DEBUG_REFCOUNTS
    } else {
        count_hit_frame_cache_instances += 1;
#endif
    }

    assert(cache_frame_frame_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue->m_type_description == NULL);
    frame_frame_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue = cache_frame_frame_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue;

    // Push the new frame as the currently active one, and we should be exclusively
    // owning it.
    pushFrameStackCompiledFrame(tstate, frame_frame_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue);
    assert(Py_REFCNT(frame_frame_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue) == 2);

    // Framed code:
    {
        PyObject *tmp_called_instance_1;
        PyObject *tmp_args_element_value_1;
        PyObject *tmp_args_element_value_2;
        PyObject *tmp_args_element_value_3;
        CHECK_OBJECT(par_bundle);
        tmp_called_instance_1 = par_bundle;
        CHECK_OBJECT(par_key);
        tmp_args_element_value_1 = par_key;
        CHECK_OBJECT(par_val);
        tmp_args_element_value_2 = par_val;
        CHECK_OBJECT(par_tbl);
        tmp_args_element_value_3 = par_tbl;
        frame_frame_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue->m_frame.f_lineno = 25;
        {
            PyObject *call_args[] = {tmp_args_element_value_1, tmp_args_element_value_2, tmp_args_element_value_3};
            tmp_return_value = CALL_METHOD_WITH_ARGS3(
                tstate,
                tmp_called_instance_1,
                mod_consts[3],
                call_args
            );
        }

        if (tmp_return_value == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 25;
            type_description_1 = "ooooo";
            goto frame_exception_exit_1;
        }
        goto frame_return_exit_1;
    }


    // Put the previous frame back on top.
    popFrameStack(tstate);

    goto frame_no_exception_1;
    frame_return_exit_1:

    // Put the previous frame back on top.
    popFrameStack(tstate);

    goto function_return_exit;
    frame_exception_exit_1:


    {
        PyTracebackObject *exception_tb = GET_EXCEPTION_STATE_TRACEBACK(&exception_state);
        if (exception_tb == NULL) {
            exception_tb = MAKE_TRACEBACK(frame_frame_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        } else if (exception_tb->tb_frame != &frame_frame_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue->m_frame) {
            exception_tb = ADD_TRACEBACK(exception_tb, frame_frame_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        }
    }

    // Attaches locals to frame if any.
    Nuitka_Frame_AttachLocals(
        frame_frame_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue,
        type_description_1,
        par_key,
        par_tbl,
        par_bundle,
        par_val,
        par_comment
    );


    // Release cached frame if used for exception.
    if (frame_frame_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue == cache_frame_frame_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue) {
#if _DEBUG_REFCOUNTS
        count_active_frame_cache_instances -= 1;
        count_released_frame_cache_instances += 1;
#endif
        Py_DECREF(cache_frame_frame_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue);
        cache_frame_frame_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue = NULL;
    }

    assertFrameObject(frame_frame_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue);

    // Put the previous frame back on top.
    popFrameStack(tstate);

    // Return the error.
    goto function_exception_exit;
    frame_no_exception_1:;

    NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
    return NULL;

function_exception_exit:
    CHECK_OBJECT(par_key);
    Py_DECREF(par_key);
    CHECK_OBJECT(par_tbl);
    Py_DECREF(par_tbl);
    CHECK_OBJECT(par_bundle);
    Py_DECREF(par_bundle);
    CHECK_OBJECT(par_val);
    Py_DECREF(par_val);
    CHECK_OBJECT(par_comment);
    Py_DECREF(par_comment);
    CHECK_EXCEPTION_STATE(&exception_state);
    RESTORE_ERROR_OCCURRED_STATE(tstate, &exception_state);

    return NULL;

function_return_exit:
   // Function cleanup code if any.
    CHECK_OBJECT(par_key);
    Py_DECREF(par_key);
    CHECK_OBJECT(par_tbl);
    Py_DECREF(par_tbl);
    CHECK_OBJECT(par_bundle);
    Py_DECREF(par_bundle);
    CHECK_OBJECT(par_val);
    Py_DECREF(par_val);
    CHECK_OBJECT(par_comment);
    Py_DECREF(par_comment);

   // Actual function exit with return value, making sure we did not make
   // the error status worse despite non-NULL return.
   CHECK_OBJECT(tmp_return_value);
   assert(had_error || !HAS_ERROR_OCCURRED(tstate));
   return tmp_return_value;
}


static PyObject *impl_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString(PyThreadState *tstate, struct Nuitka_FunctionObject const *self, PyObject **python_pars) {
    // Preserve error status for checks
#ifndef __NUITKA_NO_ASSERT__
    NUITKA_MAY_BE_UNUSED bool had_error = HAS_ERROR_OCCURRED(tstate);
#endif

    // Local variable declarations.
    PyObject *par_key = python_pars[0];
    PyObject *par_comment = python_pars[1];
    struct Nuitka_FrameObject *frame_frame_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString;
    NUITKA_MAY_BE_UNUSED char const *type_description_1 = NULL;
    PyObject *tmp_return_value = NULL;
    struct Nuitka_ExceptionPreservationItem exception_state = Empty_Nuitka_ExceptionPreservationItem;
    NUITKA_MAY_BE_UNUSED int exception_lineno = 0;
    static struct Nuitka_FrameObject *cache_frame_frame_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString = NULL;

    // Actual function body.
    if (isFrameUnusable(cache_frame_frame_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString)) {
        Py_XDECREF(cache_frame_frame_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString);

#if _DEBUG_REFCOUNTS
        if (cache_frame_frame_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString == NULL) {
            count_active_frame_cache_instances += 1;
        } else {
            count_released_frame_cache_instances += 1;
        }
        count_allocated_frame_cache_instances += 1;
#endif
        cache_frame_frame_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString = MAKE_FUNCTION_FRAME(tstate, code_objects_4115cb562da719834ffab329b3033ac0, module_Foundation$_functiondefines, sizeof(void *)+sizeof(void *));
#if _DEBUG_REFCOUNTS
    } else {
        count_hit_frame_cache_instances += 1;
#endif
    }

    assert(cache_frame_frame_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString->m_type_description == NULL);
    frame_frame_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString = cache_frame_frame_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString;

    // Push the new frame as the currently active one, and we should be exclusively
    // owning it.
    pushFrameStackCompiledFrame(tstate, frame_frame_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString);
    assert(Py_REFCNT(frame_frame_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString) == 2);

    // Framed code:
    {
        PyObject *tmp_called_instance_1;
        PyObject *tmp_called_instance_2;
        PyObject *tmp_expression_value_1;
        PyObject *tmp_args_element_value_1;
        PyObject *tmp_args_element_value_2;
        PyObject *tmp_args_element_value_3;
        tmp_expression_value_1 = module_var_accessor_Foundation$$36$_functiondefines$_Foundation(tstate);
        if (unlikely(tmp_expression_value_1 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[0]);
        }

        if (tmp_expression_value_1 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 30;
            type_description_1 = "oo";
            goto frame_exception_exit_1;
        }
        tmp_called_instance_2 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_1, mod_consts[1]);
        if (tmp_called_instance_2 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 30;
            type_description_1 = "oo";
            goto frame_exception_exit_1;
        }
        frame_frame_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString->m_frame.f_lineno = 30;
        tmp_called_instance_1 = CALL_METHOD_NO_ARGS(tstate, tmp_called_instance_2, mod_consts[2]);
        Py_DECREF(tmp_called_instance_2);
        if (tmp_called_instance_1 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 30;
            type_description_1 = "oo";
            goto frame_exception_exit_1;
        }
        CHECK_OBJECT(par_key);
        tmp_args_element_value_1 = par_key;
        tmp_args_element_value_2 = mod_consts[4];
        tmp_args_element_value_3 = Py_None;
        frame_frame_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString->m_frame.f_lineno = 30;
        {
            PyObject *call_args[] = {tmp_args_element_value_1, tmp_args_element_value_2, tmp_args_element_value_3};
            tmp_return_value = CALL_METHOD_WITH_ARGS3(
                tstate,
                tmp_called_instance_1,
                mod_consts[5],
                call_args
            );
        }

        Py_DECREF(tmp_called_instance_1);
        if (tmp_return_value == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 30;
            type_description_1 = "oo";
            goto frame_exception_exit_1;
        }
        goto frame_return_exit_1;
    }


    // Put the previous frame back on top.
    popFrameStack(tstate);

    goto frame_no_exception_1;
    frame_return_exit_1:

    // Put the previous frame back on top.
    popFrameStack(tstate);

    goto function_return_exit;
    frame_exception_exit_1:


    {
        PyTracebackObject *exception_tb = GET_EXCEPTION_STATE_TRACEBACK(&exception_state);
        if (exception_tb == NULL) {
            exception_tb = MAKE_TRACEBACK(frame_frame_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        } else if (exception_tb->tb_frame != &frame_frame_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString->m_frame) {
            exception_tb = ADD_TRACEBACK(exception_tb, frame_frame_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        }
    }

    // Attaches locals to frame if any.
    Nuitka_Frame_AttachLocals(
        frame_frame_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString,
        type_description_1,
        par_key,
        par_comment
    );


    // Release cached frame if used for exception.
    if (frame_frame_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString == cache_frame_frame_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString) {
#if _DEBUG_REFCOUNTS
        count_active_frame_cache_instances -= 1;
        count_released_frame_cache_instances += 1;
#endif
        Py_DECREF(cache_frame_frame_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString);
        cache_frame_frame_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString = NULL;
    }

    assertFrameObject(frame_frame_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString);

    // Put the previous frame back on top.
    popFrameStack(tstate);

    // Return the error.
    goto function_exception_exit;
    frame_no_exception_1:;

    NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
    return NULL;

function_exception_exit:
    CHECK_OBJECT(par_key);
    Py_DECREF(par_key);
    CHECK_OBJECT(par_comment);
    Py_DECREF(par_comment);
    CHECK_EXCEPTION_STATE(&exception_state);
    RESTORE_ERROR_OCCURRED_STATE(tstate, &exception_state);

    return NULL;

function_return_exit:
   // Function cleanup code if any.
    CHECK_OBJECT(par_key);
    Py_DECREF(par_key);
    CHECK_OBJECT(par_comment);
    Py_DECREF(par_comment);

   // Actual function exit with return value, making sure we did not make
   // the error status worse despite non-NULL return.
   CHECK_OBJECT(tmp_return_value);
   assert(had_error || !HAS_ERROR_OCCURRED(tstate));
   return tmp_return_value;
}


static PyObject *impl_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable(PyThreadState *tstate, struct Nuitka_FunctionObject const *self, PyObject **python_pars) {
    // Preserve error status for checks
#ifndef __NUITKA_NO_ASSERT__
    NUITKA_MAY_BE_UNUSED bool had_error = HAS_ERROR_OCCURRED(tstate);
#endif

    // Local variable declarations.
    PyObject *par_key = python_pars[0];
    PyObject *par_tbl = python_pars[1];
    PyObject *par_comment = python_pars[2];
    struct Nuitka_FrameObject *frame_frame_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable;
    NUITKA_MAY_BE_UNUSED char const *type_description_1 = NULL;
    PyObject *tmp_return_value = NULL;
    struct Nuitka_ExceptionPreservationItem exception_state = Empty_Nuitka_ExceptionPreservationItem;
    NUITKA_MAY_BE_UNUSED int exception_lineno = 0;
    static struct Nuitka_FrameObject *cache_frame_frame_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable = NULL;

    // Actual function body.
    if (isFrameUnusable(cache_frame_frame_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable)) {
        Py_XDECREF(cache_frame_frame_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable);

#if _DEBUG_REFCOUNTS
        if (cache_frame_frame_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable == NULL) {
            count_active_frame_cache_instances += 1;
        } else {
            count_released_frame_cache_instances += 1;
        }
        count_allocated_frame_cache_instances += 1;
#endif
        cache_frame_frame_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable = MAKE_FUNCTION_FRAME(tstate, code_objects_9b4c5f6b5519eaf6735cbe6ebdcb8362, module_Foundation$_functiondefines, sizeof(void *)+sizeof(void *)+sizeof(void *));
#if _DEBUG_REFCOUNTS
    } else {
        count_hit_frame_cache_instances += 1;
#endif
    }

    assert(cache_frame_frame_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable->m_type_description == NULL);
    frame_frame_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable = cache_frame_frame_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable;

    // Push the new frame as the currently active one, and we should be exclusively
    // owning it.
    pushFrameStackCompiledFrame(tstate, frame_frame_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable);
    assert(Py_REFCNT(frame_frame_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable) == 2);

    // Framed code:
    {
        PyObject *tmp_called_instance_1;
        PyObject *tmp_expression_value_1;
        PyObject *tmp_expression_value_2;
        PyObject *tmp_args_element_value_1;
        PyObject *tmp_args_element_value_2;
        PyObject *tmp_args_element_value_3;
        tmp_expression_value_2 = module_var_accessor_Foundation$$36$_functiondefines$_Foundation(tstate);
        if (unlikely(tmp_expression_value_2 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[0]);
        }

        if (tmp_expression_value_2 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 37;
            type_description_1 = "ooo";
            goto frame_exception_exit_1;
        }
        tmp_expression_value_1 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_2, mod_consts[1]);
        if (tmp_expression_value_1 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 37;
            type_description_1 = "ooo";
            goto frame_exception_exit_1;
        }
        tmp_called_instance_1 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_1, mod_consts[2]);
        Py_DECREF(tmp_expression_value_1);
        if (tmp_called_instance_1 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 37;
            type_description_1 = "ooo";
            goto frame_exception_exit_1;
        }
        CHECK_OBJECT(par_key);
        tmp_args_element_value_1 = par_key;
        tmp_args_element_value_2 = mod_consts[4];
        CHECK_OBJECT(par_tbl);
        tmp_args_element_value_3 = par_tbl;
        frame_frame_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable->m_frame.f_lineno = 37;
        {
            PyObject *call_args[] = {tmp_args_element_value_1, tmp_args_element_value_2, tmp_args_element_value_3};
            tmp_return_value = CALL_METHOD_WITH_ARGS3(
                tstate,
                tmp_called_instance_1,
                mod_consts[5],
                call_args
            );
        }

        Py_DECREF(tmp_called_instance_1);
        if (tmp_return_value == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 37;
            type_description_1 = "ooo";
            goto frame_exception_exit_1;
        }
        goto frame_return_exit_1;
    }


    // Put the previous frame back on top.
    popFrameStack(tstate);

    goto frame_no_exception_1;
    frame_return_exit_1:

    // Put the previous frame back on top.
    popFrameStack(tstate);

    goto function_return_exit;
    frame_exception_exit_1:


    {
        PyTracebackObject *exception_tb = GET_EXCEPTION_STATE_TRACEBACK(&exception_state);
        if (exception_tb == NULL) {
            exception_tb = MAKE_TRACEBACK(frame_frame_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        } else if (exception_tb->tb_frame != &frame_frame_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable->m_frame) {
            exception_tb = ADD_TRACEBACK(exception_tb, frame_frame_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        }
    }

    // Attaches locals to frame if any.
    Nuitka_Frame_AttachLocals(
        frame_frame_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable,
        type_description_1,
        par_key,
        par_tbl,
        par_comment
    );


    // Release cached frame if used for exception.
    if (frame_frame_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable == cache_frame_frame_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable) {
#if _DEBUG_REFCOUNTS
        count_active_frame_cache_instances -= 1;
        count_released_frame_cache_instances += 1;
#endif
        Py_DECREF(cache_frame_frame_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable);
        cache_frame_frame_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable = NULL;
    }

    assertFrameObject(frame_frame_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable);

    // Put the previous frame back on top.
    popFrameStack(tstate);

    // Return the error.
    goto function_exception_exit;
    frame_no_exception_1:;

    NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
    return NULL;

function_exception_exit:
    CHECK_OBJECT(par_key);
    Py_DECREF(par_key);
    CHECK_OBJECT(par_tbl);
    Py_DECREF(par_tbl);
    CHECK_OBJECT(par_comment);
    Py_DECREF(par_comment);
    CHECK_EXCEPTION_STATE(&exception_state);
    RESTORE_ERROR_OCCURRED_STATE(tstate, &exception_state);

    return NULL;

function_return_exit:
   // Function cleanup code if any.
    CHECK_OBJECT(par_key);
    Py_DECREF(par_key);
    CHECK_OBJECT(par_tbl);
    Py_DECREF(par_tbl);
    CHECK_OBJECT(par_comment);
    Py_DECREF(par_comment);

   // Actual function exit with return value, making sure we did not make
   // the error status worse despite non-NULL return.
   CHECK_OBJECT(tmp_return_value);
   assert(had_error || !HAS_ERROR_OCCURRED(tstate));
   return tmp_return_value;
}


static PyObject *impl_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle(PyThreadState *tstate, struct Nuitka_FunctionObject const *self, PyObject **python_pars) {
    // Preserve error status for checks
#ifndef __NUITKA_NO_ASSERT__
    NUITKA_MAY_BE_UNUSED bool had_error = HAS_ERROR_OCCURRED(tstate);
#endif

    // Local variable declarations.
    PyObject *par_key = python_pars[0];
    PyObject *par_tbl = python_pars[1];
    PyObject *par_bundle = python_pars[2];
    PyObject *par_comment = python_pars[3];
    struct Nuitka_FrameObject *frame_frame_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle;
    NUITKA_MAY_BE_UNUSED char const *type_description_1 = NULL;
    PyObject *tmp_return_value = NULL;
    struct Nuitka_ExceptionPreservationItem exception_state = Empty_Nuitka_ExceptionPreservationItem;
    NUITKA_MAY_BE_UNUSED int exception_lineno = 0;
    static struct Nuitka_FrameObject *cache_frame_frame_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle = NULL;

    // Actual function body.
    if (isFrameUnusable(cache_frame_frame_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle)) {
        Py_XDECREF(cache_frame_frame_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle);

#if _DEBUG_REFCOUNTS
        if (cache_frame_frame_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle == NULL) {
            count_active_frame_cache_instances += 1;
        } else {
            count_released_frame_cache_instances += 1;
        }
        count_allocated_frame_cache_instances += 1;
#endif
        cache_frame_frame_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle = MAKE_FUNCTION_FRAME(tstate, code_objects_f62dc5309fe296676192c3873b801af2, module_Foundation$_functiondefines, sizeof(void *)+sizeof(void *)+sizeof(void *)+sizeof(void *));
#if _DEBUG_REFCOUNTS
    } else {
        count_hit_frame_cache_instances += 1;
#endif
    }

    assert(cache_frame_frame_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle->m_type_description == NULL);
    frame_frame_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle = cache_frame_frame_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle;

    // Push the new frame as the currently active one, and we should be exclusively
    // owning it.
    pushFrameStackCompiledFrame(tstate, frame_frame_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle);
    assert(Py_REFCNT(frame_frame_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle) == 2);

    // Framed code:
    {
        PyObject *tmp_called_instance_1;
        PyObject *tmp_args_element_value_1;
        PyObject *tmp_args_element_value_2;
        PyObject *tmp_args_element_value_3;
        CHECK_OBJECT(par_bundle);
        tmp_called_instance_1 = par_bundle;
        CHECK_OBJECT(par_key);
        tmp_args_element_value_1 = par_key;
        tmp_args_element_value_2 = mod_consts[4];
        CHECK_OBJECT(par_tbl);
        tmp_args_element_value_3 = par_tbl;
        frame_frame_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle->m_frame.f_lineno = 43;
        {
            PyObject *call_args[] = {tmp_args_element_value_1, tmp_args_element_value_2, tmp_args_element_value_3};
            tmp_return_value = CALL_METHOD_WITH_ARGS3(
                tstate,
                tmp_called_instance_1,
                mod_consts[5],
                call_args
            );
        }

        if (tmp_return_value == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 43;
            type_description_1 = "oooo";
            goto frame_exception_exit_1;
        }
        goto frame_return_exit_1;
    }


    // Put the previous frame back on top.
    popFrameStack(tstate);

    goto frame_no_exception_1;
    frame_return_exit_1:

    // Put the previous frame back on top.
    popFrameStack(tstate);

    goto function_return_exit;
    frame_exception_exit_1:


    {
        PyTracebackObject *exception_tb = GET_EXCEPTION_STATE_TRACEBACK(&exception_state);
        if (exception_tb == NULL) {
            exception_tb = MAKE_TRACEBACK(frame_frame_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        } else if (exception_tb->tb_frame != &frame_frame_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle->m_frame) {
            exception_tb = ADD_TRACEBACK(exception_tb, frame_frame_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        }
    }

    // Attaches locals to frame if any.
    Nuitka_Frame_AttachLocals(
        frame_frame_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle,
        type_description_1,
        par_key,
        par_tbl,
        par_bundle,
        par_comment
    );


    // Release cached frame if used for exception.
    if (frame_frame_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle == cache_frame_frame_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle) {
#if _DEBUG_REFCOUNTS
        count_active_frame_cache_instances -= 1;
        count_released_frame_cache_instances += 1;
#endif
        Py_DECREF(cache_frame_frame_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle);
        cache_frame_frame_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle = NULL;
    }

    assertFrameObject(frame_frame_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle);

    // Put the previous frame back on top.
    popFrameStack(tstate);

    // Return the error.
    goto function_exception_exit;
    frame_no_exception_1:;

    NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
    return NULL;

function_exception_exit:
    CHECK_OBJECT(par_key);
    Py_DECREF(par_key);
    CHECK_OBJECT(par_tbl);
    Py_DECREF(par_tbl);
    CHECK_OBJECT(par_bundle);
    Py_DECREF(par_bundle);
    CHECK_OBJECT(par_comment);
    Py_DECREF(par_comment);
    CHECK_EXCEPTION_STATE(&exception_state);
    RESTORE_ERROR_OCCURRED_STATE(tstate, &exception_state);

    return NULL;

function_return_exit:
   // Function cleanup code if any.
    CHECK_OBJECT(par_key);
    Py_DECREF(par_key);
    CHECK_OBJECT(par_tbl);
    Py_DECREF(par_tbl);
    CHECK_OBJECT(par_bundle);
    Py_DECREF(par_bundle);
    CHECK_OBJECT(par_comment);
    Py_DECREF(par_comment);

   // Actual function exit with return value, making sure we did not make
   // the error status worse despite non-NULL return.
   CHECK_OBJECT(tmp_return_value);
   assert(had_error || !HAS_ERROR_OCCURRED(tstate));
   return tmp_return_value;
}


static PyObject *impl_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue(PyThreadState *tstate, struct Nuitka_FunctionObject const *self, PyObject **python_pars) {
    // Preserve error status for checks
#ifndef __NUITKA_NO_ASSERT__
    NUITKA_MAY_BE_UNUSED bool had_error = HAS_ERROR_OCCURRED(tstate);
#endif

    // Local variable declarations.
    PyObject *par_key = python_pars[0];
    PyObject *par_tbl = python_pars[1];
    PyObject *par_bundle = python_pars[2];
    PyObject *par_val = python_pars[3];
    PyObject *par_comment = python_pars[4];
    struct Nuitka_FrameObject *frame_frame_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue;
    NUITKA_MAY_BE_UNUSED char const *type_description_1 = NULL;
    PyObject *tmp_return_value = NULL;
    struct Nuitka_ExceptionPreservationItem exception_state = Empty_Nuitka_ExceptionPreservationItem;
    NUITKA_MAY_BE_UNUSED int exception_lineno = 0;
    static struct Nuitka_FrameObject *cache_frame_frame_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue = NULL;

    // Actual function body.
    if (isFrameUnusable(cache_frame_frame_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue)) {
        Py_XDECREF(cache_frame_frame_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue);

#if _DEBUG_REFCOUNTS
        if (cache_frame_frame_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue == NULL) {
            count_active_frame_cache_instances += 1;
        } else {
            count_released_frame_cache_instances += 1;
        }
        count_allocated_frame_cache_instances += 1;
#endif
        cache_frame_frame_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue = MAKE_FUNCTION_FRAME(tstate, code_objects_1d0b2877beefde48d485808a30060640, module_Foundation$_functiondefines, sizeof(void *)+sizeof(void *)+sizeof(void *)+sizeof(void *)+sizeof(void *));
#if _DEBUG_REFCOUNTS
    } else {
        count_hit_frame_cache_instances += 1;
#endif
    }

    assert(cache_frame_frame_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue->m_type_description == NULL);
    frame_frame_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue = cache_frame_frame_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue;

    // Push the new frame as the currently active one, and we should be exclusively
    // owning it.
    pushFrameStackCompiledFrame(tstate, frame_frame_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue);
    assert(Py_REFCNT(frame_frame_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue) == 2);

    // Framed code:
    {
        PyObject *tmp_called_instance_1;
        PyObject *tmp_args_element_value_1;
        PyObject *tmp_args_element_value_2;
        PyObject *tmp_args_element_value_3;
        CHECK_OBJECT(par_bundle);
        tmp_called_instance_1 = par_bundle;
        CHECK_OBJECT(par_key);
        tmp_args_element_value_1 = par_key;
        CHECK_OBJECT(par_val);
        tmp_args_element_value_2 = par_val;
        CHECK_OBJECT(par_tbl);
        tmp_args_element_value_3 = par_tbl;
        frame_frame_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue->m_frame.f_lineno = 47;
        {
            PyObject *call_args[] = {tmp_args_element_value_1, tmp_args_element_value_2, tmp_args_element_value_3};
            tmp_return_value = CALL_METHOD_WITH_ARGS3(
                tstate,
                tmp_called_instance_1,
                mod_consts[5],
                call_args
            );
        }

        if (tmp_return_value == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 47;
            type_description_1 = "ooooo";
            goto frame_exception_exit_1;
        }
        goto frame_return_exit_1;
    }


    // Put the previous frame back on top.
    popFrameStack(tstate);

    goto frame_no_exception_1;
    frame_return_exit_1:

    // Put the previous frame back on top.
    popFrameStack(tstate);

    goto function_return_exit;
    frame_exception_exit_1:


    {
        PyTracebackObject *exception_tb = GET_EXCEPTION_STATE_TRACEBACK(&exception_state);
        if (exception_tb == NULL) {
            exception_tb = MAKE_TRACEBACK(frame_frame_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        } else if (exception_tb->tb_frame != &frame_frame_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue->m_frame) {
            exception_tb = ADD_TRACEBACK(exception_tb, frame_frame_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        }
    }

    // Attaches locals to frame if any.
    Nuitka_Frame_AttachLocals(
        frame_frame_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue,
        type_description_1,
        par_key,
        par_tbl,
        par_bundle,
        par_val,
        par_comment
    );


    // Release cached frame if used for exception.
    if (frame_frame_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue == cache_frame_frame_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue) {
#if _DEBUG_REFCOUNTS
        count_active_frame_cache_instances -= 1;
        count_released_frame_cache_instances += 1;
#endif
        Py_DECREF(cache_frame_frame_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue);
        cache_frame_frame_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue = NULL;
    }

    assertFrameObject(frame_frame_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue);

    // Put the previous frame back on top.
    popFrameStack(tstate);

    // Return the error.
    goto function_exception_exit;
    frame_no_exception_1:;

    NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
    return NULL;

function_exception_exit:
    CHECK_OBJECT(par_key);
    Py_DECREF(par_key);
    CHECK_OBJECT(par_tbl);
    Py_DECREF(par_tbl);
    CHECK_OBJECT(par_bundle);
    Py_DECREF(par_bundle);
    CHECK_OBJECT(par_val);
    Py_DECREF(par_val);
    CHECK_OBJECT(par_comment);
    Py_DECREF(par_comment);
    CHECK_EXCEPTION_STATE(&exception_state);
    RESTORE_ERROR_OCCURRED_STATE(tstate, &exception_state);

    return NULL;

function_return_exit:
   // Function cleanup code if any.
    CHECK_OBJECT(par_key);
    Py_DECREF(par_key);
    CHECK_OBJECT(par_tbl);
    Py_DECREF(par_tbl);
    CHECK_OBJECT(par_bundle);
    Py_DECREF(par_bundle);
    CHECK_OBJECT(par_val);
    Py_DECREF(par_val);
    CHECK_OBJECT(par_comment);
    Py_DECREF(par_comment);

   // Actual function exit with return value, making sure we did not make
   // the error status worse despite non-NULL return.
   CHECK_OBJECT(tmp_return_value);
   assert(had_error || !HAS_ERROR_OCCURRED(tstate));
   return tmp_return_value;
}


static PyObject *impl_Foundation$_functiondefines$$$function__9_MIN(PyThreadState *tstate, struct Nuitka_FunctionObject const *self, PyObject **python_pars) {
    // Preserve error status for checks
#ifndef __NUITKA_NO_ASSERT__
    NUITKA_MAY_BE_UNUSED bool had_error = HAS_ERROR_OCCURRED(tstate);
#endif

    // Local variable declarations.
    PyObject *par_a = python_pars[0];
    PyObject *par_b = python_pars[1];
    struct Nuitka_FrameObject *frame_frame_Foundation$_functiondefines$$$function__9_MIN;
    NUITKA_MAY_BE_UNUSED char const *type_description_1 = NULL;
    struct Nuitka_ExceptionPreservationItem exception_state = Empty_Nuitka_ExceptionPreservationItem;
    NUITKA_MAY_BE_UNUSED int exception_lineno = 0;
    PyObject *tmp_return_value = NULL;
    static struct Nuitka_FrameObject *cache_frame_frame_Foundation$_functiondefines$$$function__9_MIN = NULL;

    // Actual function body.
    if (isFrameUnusable(cache_frame_frame_Foundation$_functiondefines$$$function__9_MIN)) {
        Py_XDECREF(cache_frame_frame_Foundation$_functiondefines$$$function__9_MIN);

#if _DEBUG_REFCOUNTS
        if (cache_frame_frame_Foundation$_functiondefines$$$function__9_MIN == NULL) {
            count_active_frame_cache_instances += 1;
        } else {
            count_released_frame_cache_instances += 1;
        }
        count_allocated_frame_cache_instances += 1;
#endif
        cache_frame_frame_Foundation$_functiondefines$$$function__9_MIN = MAKE_FUNCTION_FRAME(tstate, code_objects_631961034f3de6340bae8cc03d50fbca, module_Foundation$_functiondefines, sizeof(void *)+sizeof(void *));
#if _DEBUG_REFCOUNTS
    } else {
        count_hit_frame_cache_instances += 1;
#endif
    }

    assert(cache_frame_frame_Foundation$_functiondefines$$$function__9_MIN->m_type_description == NULL);
    frame_frame_Foundation$_functiondefines$$$function__9_MIN = cache_frame_frame_Foundation$_functiondefines$$$function__9_MIN;

    // Push the new frame as the currently active one, and we should be exclusively
    // owning it.
    pushFrameStackCompiledFrame(tstate, frame_frame_Foundation$_functiondefines$$$function__9_MIN);
    assert(Py_REFCNT(frame_frame_Foundation$_functiondefines$$$function__9_MIN) == 2);

    // Framed code:
    {
        nuitka_bool tmp_condition_result_1;
        PyObject *tmp_cmp_expr_left_1;
        PyObject *tmp_cmp_expr_right_1;
        CHECK_OBJECT(par_a);
        tmp_cmp_expr_left_1 = par_a;
        CHECK_OBJECT(par_b);
        tmp_cmp_expr_right_1 = par_b;
        tmp_condition_result_1 = RICH_COMPARE_LT_NBOOL_OBJECT_OBJECT(tmp_cmp_expr_left_1, tmp_cmp_expr_right_1);
        if (tmp_condition_result_1 == NUITKA_BOOL_EXCEPTION) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 51;
            type_description_1 = "oo";
            goto frame_exception_exit_1;
        }
        if (tmp_condition_result_1 == NUITKA_BOOL_TRUE) {
            goto branch_yes_1;
        } else {
            goto branch_no_1;
        }
    }
    branch_yes_1:;
    CHECK_OBJECT(par_a);
    tmp_return_value = par_a;
    Py_INCREF(tmp_return_value);
    goto frame_return_exit_1;
    goto branch_end_1;
    branch_no_1:;
    CHECK_OBJECT(par_b);
    tmp_return_value = par_b;
    Py_INCREF(tmp_return_value);
    goto frame_return_exit_1;
    branch_end_1:;


    // Put the previous frame back on top.
    popFrameStack(tstate);

    goto frame_no_exception_1;
    frame_return_exit_1:

    // Put the previous frame back on top.
    popFrameStack(tstate);

    goto function_return_exit;
    frame_exception_exit_1:


    {
        PyTracebackObject *exception_tb = GET_EXCEPTION_STATE_TRACEBACK(&exception_state);
        if (exception_tb == NULL) {
            exception_tb = MAKE_TRACEBACK(frame_frame_Foundation$_functiondefines$$$function__9_MIN, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        } else if (exception_tb->tb_frame != &frame_frame_Foundation$_functiondefines$$$function__9_MIN->m_frame) {
            exception_tb = ADD_TRACEBACK(exception_tb, frame_frame_Foundation$_functiondefines$$$function__9_MIN, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        }
    }

    // Attaches locals to frame if any.
    Nuitka_Frame_AttachLocals(
        frame_frame_Foundation$_functiondefines$$$function__9_MIN,
        type_description_1,
        par_a,
        par_b
    );


    // Release cached frame if used for exception.
    if (frame_frame_Foundation$_functiondefines$$$function__9_MIN == cache_frame_frame_Foundation$_functiondefines$$$function__9_MIN) {
#if _DEBUG_REFCOUNTS
        count_active_frame_cache_instances -= 1;
        count_released_frame_cache_instances += 1;
#endif
        Py_DECREF(cache_frame_frame_Foundation$_functiondefines$$$function__9_MIN);
        cache_frame_frame_Foundation$_functiondefines$$$function__9_MIN = NULL;
    }

    assertFrameObject(frame_frame_Foundation$_functiondefines$$$function__9_MIN);

    // Put the previous frame back on top.
    popFrameStack(tstate);

    // Return the error.
    goto function_exception_exit;
    frame_no_exception_1:;

    NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
    return NULL;

function_exception_exit:
    CHECK_OBJECT(par_a);
    Py_DECREF(par_a);
    CHECK_OBJECT(par_b);
    Py_DECREF(par_b);
    CHECK_EXCEPTION_STATE(&exception_state);
    RESTORE_ERROR_OCCURRED_STATE(tstate, &exception_state);

    return NULL;

function_return_exit:
   // Function cleanup code if any.
    CHECK_OBJECT(par_a);
    Py_DECREF(par_a);
    CHECK_OBJECT(par_b);
    Py_DECREF(par_b);

   // Actual function exit with return value, making sure we did not make
   // the error status worse despite non-NULL return.
   CHECK_OBJECT(tmp_return_value);
   assert(had_error || !HAS_ERROR_OCCURRED(tstate));
   return tmp_return_value;
}


static PyObject *impl_Foundation$_functiondefines$$$function__10_MAX(PyThreadState *tstate, struct Nuitka_FunctionObject const *self, PyObject **python_pars) {
    // Preserve error status for checks
#ifndef __NUITKA_NO_ASSERT__
    NUITKA_MAY_BE_UNUSED bool had_error = HAS_ERROR_OCCURRED(tstate);
#endif

    // Local variable declarations.
    PyObject *par_a = python_pars[0];
    PyObject *par_b = python_pars[1];
    struct Nuitka_FrameObject *frame_frame_Foundation$_functiondefines$$$function__10_MAX;
    NUITKA_MAY_BE_UNUSED char const *type_description_1 = NULL;
    struct Nuitka_ExceptionPreservationItem exception_state = Empty_Nuitka_ExceptionPreservationItem;
    NUITKA_MAY_BE_UNUSED int exception_lineno = 0;
    PyObject *tmp_return_value = NULL;
    static struct Nuitka_FrameObject *cache_frame_frame_Foundation$_functiondefines$$$function__10_MAX = NULL;

    // Actual function body.
    if (isFrameUnusable(cache_frame_frame_Foundation$_functiondefines$$$function__10_MAX)) {
        Py_XDECREF(cache_frame_frame_Foundation$_functiondefines$$$function__10_MAX);

#if _DEBUG_REFCOUNTS
        if (cache_frame_frame_Foundation$_functiondefines$$$function__10_MAX == NULL) {
            count_active_frame_cache_instances += 1;
        } else {
            count_released_frame_cache_instances += 1;
        }
        count_allocated_frame_cache_instances += 1;
#endif
        cache_frame_frame_Foundation$_functiondefines$$$function__10_MAX = MAKE_FUNCTION_FRAME(tstate, code_objects_9b59f8c9df2709f187a064c7ddc82bdd, module_Foundation$_functiondefines, sizeof(void *)+sizeof(void *));
#if _DEBUG_REFCOUNTS
    } else {
        count_hit_frame_cache_instances += 1;
#endif
    }

    assert(cache_frame_frame_Foundation$_functiondefines$$$function__10_MAX->m_type_description == NULL);
    frame_frame_Foundation$_functiondefines$$$function__10_MAX = cache_frame_frame_Foundation$_functiondefines$$$function__10_MAX;

    // Push the new frame as the currently active one, and we should be exclusively
    // owning it.
    pushFrameStackCompiledFrame(tstate, frame_frame_Foundation$_functiondefines$$$function__10_MAX);
    assert(Py_REFCNT(frame_frame_Foundation$_functiondefines$$$function__10_MAX) == 2);

    // Framed code:
    {
        nuitka_bool tmp_condition_result_1;
        PyObject *tmp_cmp_expr_left_1;
        PyObject *tmp_cmp_expr_right_1;
        CHECK_OBJECT(par_a);
        tmp_cmp_expr_left_1 = par_a;
        CHECK_OBJECT(par_b);
        tmp_cmp_expr_right_1 = par_b;
        tmp_condition_result_1 = RICH_COMPARE_LT_NBOOL_OBJECT_OBJECT(tmp_cmp_expr_left_1, tmp_cmp_expr_right_1);
        if (tmp_condition_result_1 == NUITKA_BOOL_EXCEPTION) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 58;
            type_description_1 = "oo";
            goto frame_exception_exit_1;
        }
        if (tmp_condition_result_1 == NUITKA_BOOL_TRUE) {
            goto branch_yes_1;
        } else {
            goto branch_no_1;
        }
    }
    branch_yes_1:;
    CHECK_OBJECT(par_b);
    tmp_return_value = par_b;
    Py_INCREF(tmp_return_value);
    goto frame_return_exit_1;
    goto branch_end_1;
    branch_no_1:;
    CHECK_OBJECT(par_a);
    tmp_return_value = par_a;
    Py_INCREF(tmp_return_value);
    goto frame_return_exit_1;
    branch_end_1:;


    // Put the previous frame back on top.
    popFrameStack(tstate);

    goto frame_no_exception_1;
    frame_return_exit_1:

    // Put the previous frame back on top.
    popFrameStack(tstate);

    goto function_return_exit;
    frame_exception_exit_1:


    {
        PyTracebackObject *exception_tb = GET_EXCEPTION_STATE_TRACEBACK(&exception_state);
        if (exception_tb == NULL) {
            exception_tb = MAKE_TRACEBACK(frame_frame_Foundation$_functiondefines$$$function__10_MAX, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        } else if (exception_tb->tb_frame != &frame_frame_Foundation$_functiondefines$$$function__10_MAX->m_frame) {
            exception_tb = ADD_TRACEBACK(exception_tb, frame_frame_Foundation$_functiondefines$$$function__10_MAX, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        }
    }

    // Attaches locals to frame if any.
    Nuitka_Frame_AttachLocals(
        frame_frame_Foundation$_functiondefines$$$function__10_MAX,
        type_description_1,
        par_a,
        par_b
    );


    // Release cached frame if used for exception.
    if (frame_frame_Foundation$_functiondefines$$$function__10_MAX == cache_frame_frame_Foundation$_functiondefines$$$function__10_MAX) {
#if _DEBUG_REFCOUNTS
        count_active_frame_cache_instances -= 1;
        count_released_frame_cache_instances += 1;
#endif
        Py_DECREF(cache_frame_frame_Foundation$_functiondefines$$$function__10_MAX);
        cache_frame_frame_Foundation$_functiondefines$$$function__10_MAX = NULL;
    }

    assertFrameObject(frame_frame_Foundation$_functiondefines$$$function__10_MAX);

    // Put the previous frame back on top.
    popFrameStack(tstate);

    // Return the error.
    goto function_exception_exit;
    frame_no_exception_1:;

    NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
    return NULL;

function_exception_exit:
    CHECK_OBJECT(par_a);
    Py_DECREF(par_a);
    CHECK_OBJECT(par_b);
    Py_DECREF(par_b);
    CHECK_EXCEPTION_STATE(&exception_state);
    RESTORE_ERROR_OCCURRED_STATE(tstate, &exception_state);

    return NULL;

function_return_exit:
   // Function cleanup code if any.
    CHECK_OBJECT(par_a);
    Py_DECREF(par_a);
    CHECK_OBJECT(par_b);
    Py_DECREF(par_b);

   // Actual function exit with return value, making sure we did not make
   // the error status worse despite non-NULL return.
   CHECK_OBJECT(tmp_return_value);
   assert(had_error || !HAS_ERROR_OCCURRED(tstate));
   return tmp_return_value;
}



static PyObject *MAKE_FUNCTION_Foundation$_functiondefines$$$function__10_MAX(PyThreadState *tstate) {
    struct Nuitka_FunctionObject *result = Nuitka_Function_New(
        impl_Foundation$_functiondefines$$$function__10_MAX,
        mod_consts[22],
#if PYTHON_VERSION >= 0x300
        NULL,
#endif
        code_objects_9b59f8c9df2709f187a064c7ddc82bdd,
        NULL,
#if PYTHON_VERSION >= 0x300
        NULL,
        NULL,
#endif
        module_Foundation$_functiondefines,
        NULL,
        NULL,
        0
    );


    return (PyObject *)result;
}



static PyObject *MAKE_FUNCTION_Foundation$_functiondefines$$$function__1_NSLocalizedString(PyThreadState *tstate) {
    struct Nuitka_FunctionObject *result = Nuitka_Function_New(
        impl_Foundation$_functiondefines$$$function__1_NSLocalizedString,
        mod_consts[13],
#if PYTHON_VERSION >= 0x300
        NULL,
#endif
        code_objects_bcdc77864900a2c1fd6c9b0162803611,
        NULL,
#if PYTHON_VERSION >= 0x300
        NULL,
        NULL,
#endif
        module_Foundation$_functiondefines,
        NULL,
        NULL,
        0
    );


    return (PyObject *)result;
}



static PyObject *MAKE_FUNCTION_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable(PyThreadState *tstate) {
    struct Nuitka_FunctionObject *result = Nuitka_Function_New(
        impl_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable,
        mod_consts[14],
#if PYTHON_VERSION >= 0x300
        NULL,
#endif
        code_objects_41832c82cd0798a3261f1f6f6b884c89,
        NULL,
#if PYTHON_VERSION >= 0x300
        NULL,
        NULL,
#endif
        module_Foundation$_functiondefines,
        NULL,
        NULL,
        0
    );


    return (PyObject *)result;
}



static PyObject *MAKE_FUNCTION_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle(PyThreadState *tstate) {
    struct Nuitka_FunctionObject *result = Nuitka_Function_New(
        impl_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle,
        mod_consts[15],
#if PYTHON_VERSION >= 0x300
        NULL,
#endif
        code_objects_6d40c2d060afb3d398dea5db151bd651,
        NULL,
#if PYTHON_VERSION >= 0x300
        NULL,
        NULL,
#endif
        module_Foundation$_functiondefines,
        NULL,
        NULL,
        0
    );


    return (PyObject *)result;
}



static PyObject *MAKE_FUNCTION_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue(PyThreadState *tstate) {
    struct Nuitka_FunctionObject *result = Nuitka_Function_New(
        impl_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue,
        mod_consts[16],
#if PYTHON_VERSION >= 0x300
        NULL,
#endif
        code_objects_2ebfd55b853d64cfb6381839f74ed3e0,
        NULL,
#if PYTHON_VERSION >= 0x300
        NULL,
        NULL,
#endif
        module_Foundation$_functiondefines,
        NULL,
        NULL,
        0
    );


    return (PyObject *)result;
}



static PyObject *MAKE_FUNCTION_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString(PyThreadState *tstate) {
    struct Nuitka_FunctionObject *result = Nuitka_Function_New(
        impl_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString,
        mod_consts[17],
#if PYTHON_VERSION >= 0x300
        NULL,
#endif
        code_objects_4115cb562da719834ffab329b3033ac0,
        NULL,
#if PYTHON_VERSION >= 0x300
        NULL,
        NULL,
#endif
        module_Foundation$_functiondefines,
        NULL,
        NULL,
        0
    );


    return (PyObject *)result;
}



static PyObject *MAKE_FUNCTION_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable(PyThreadState *tstate) {
    struct Nuitka_FunctionObject *result = Nuitka_Function_New(
        impl_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable,
        mod_consts[18],
#if PYTHON_VERSION >= 0x300
        NULL,
#endif
        code_objects_9b4c5f6b5519eaf6735cbe6ebdcb8362,
        NULL,
#if PYTHON_VERSION >= 0x300
        NULL,
        NULL,
#endif
        module_Foundation$_functiondefines,
        NULL,
        NULL,
        0
    );


    return (PyObject *)result;
}



static PyObject *MAKE_FUNCTION_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle(PyThreadState *tstate) {
    struct Nuitka_FunctionObject *result = Nuitka_Function_New(
        impl_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle,
        mod_consts[19],
#if PYTHON_VERSION >= 0x300
        NULL,
#endif
        code_objects_f62dc5309fe296676192c3873b801af2,
        NULL,
#if PYTHON_VERSION >= 0x300
        NULL,
        NULL,
#endif
        module_Foundation$_functiondefines,
        NULL,
        NULL,
        0
    );


    return (PyObject *)result;
}



static PyObject *MAKE_FUNCTION_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue(PyThreadState *tstate) {
    struct Nuitka_FunctionObject *result = Nuitka_Function_New(
        impl_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue,
        mod_consts[20],
#if PYTHON_VERSION >= 0x300
        NULL,
#endif
        code_objects_1d0b2877beefde48d485808a30060640,
        NULL,
#if PYTHON_VERSION >= 0x300
        NULL,
        NULL,
#endif
        module_Foundation$_functiondefines,
        NULL,
        NULL,
        0
    );


    return (PyObject *)result;
}



static PyObject *MAKE_FUNCTION_Foundation$_functiondefines$$$function__9_MIN(PyThreadState *tstate) {
    struct Nuitka_FunctionObject *result = Nuitka_Function_New(
        impl_Foundation$_functiondefines$$$function__9_MIN,
        mod_consts[21],
#if PYTHON_VERSION >= 0x300
        NULL,
#endif
        code_objects_631961034f3de6340bae8cc03d50fbca,
        NULL,
#if PYTHON_VERSION >= 0x300
        NULL,
        NULL,
#endif
        module_Foundation$_functiondefines,
        NULL,
        NULL,
        0
    );


    return (PyObject *)result;
}


extern void _initCompiledCellType();
extern void _initCompiledGeneratorType();
extern void _initCompiledFunctionType();
extern void _initCompiledMethodType();
extern void _initCompiledFrameType();

extern PyTypeObject Nuitka_Loader_Type;

#ifdef _NUITKA_PLUGIN_DILL_ENABLED
// Provide a way to create find a function via its C code and create it back
// in another process, useful for multiprocessing extensions like dill
extern void registerDillPluginTables(PyThreadState *tstate, char const *module_name, PyMethodDef *reduce_compiled_function, PyMethodDef *create_compiled_function);

static function_impl_code const function_table_Foundation$_functiondefines[] = {
    impl_Foundation$_functiondefines$$$function__1_NSLocalizedString,
    impl_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable,
    impl_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle,
    impl_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue,
    impl_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString,
    impl_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable,
    impl_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle,
    impl_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue,
    impl_Foundation$_functiondefines$$$function__9_MIN,
    impl_Foundation$_functiondefines$$$function__10_MAX,
    NULL
};

static PyObject *_reduce_compiled_function(PyObject *self, PyObject *args, PyObject *kwds) {
    PyObject *func;

    if (!PyArg_ParseTuple(args, "O:reduce_compiled_function", &func, NULL)) {
        return NULL;
    }

    if (Nuitka_Function_Check(func) == false) {
        PyThreadState *tstate = PyThreadState_GET();

        SET_CURRENT_EXCEPTION_TYPE0_STR(tstate, PyExc_TypeError, "not a compiled function");
        return NULL;
    }

    struct Nuitka_FunctionObject *function = (struct Nuitka_FunctionObject *)func;

    return Nuitka_Function_GetFunctionState(function, function_table_Foundation$_functiondefines);
}

static PyMethodDef _method_def_reduce_compiled_function = {"reduce_compiled_function", (PyCFunction)_reduce_compiled_function,
                                                           METH_VARARGS, NULL};


static PyObject *_create_compiled_function(PyObject *self, PyObject *args, PyObject *kwds) {
    CHECK_OBJECT_DEEP(args);

    PyObject *function_index;
    PyObject *code_object_desc;
    PyObject *defaults;
    PyObject *kw_defaults;
    PyObject *doc;
    PyObject *constant_return_value;
    PyObject *function_qualname;
    PyObject *closure;
    PyObject *annotations;
    PyObject *func_dict;

    if (!PyArg_ParseTuple(args, "OOOOOOOOOO:create_compiled_function", &function_index, &code_object_desc, &defaults, &kw_defaults, &doc, &constant_return_value, &function_qualname, &closure, &annotations, &func_dict, NULL)) {
        return NULL;
    }

    return (PyObject *)Nuitka_Function_CreateFunctionViaCodeIndex(
        module_Foundation$_functiondefines,
        function_qualname,
        function_index,
        code_object_desc,
        constant_return_value,
        defaults,
        kw_defaults,
        doc,
        closure,
        annotations,
        func_dict,
        function_table_Foundation$_functiondefines,
        sizeof(function_table_Foundation$_functiondefines) / sizeof(function_impl_code)
    );
}

static PyMethodDef _method_def_create_compiled_function = {
    "create_compiled_function",
    (PyCFunction)_create_compiled_function,
    METH_VARARGS, NULL
};


#endif

// Actual name might be different when loaded as a package.
#if _NUITKA_MODULE_MODE && 0
static char const *module_full_name = "Foundation._functiondefines";
#endif

// Internal entry point for module code.
PyObject *modulecode_Foundation$_functiondefines(PyThreadState *tstate, PyObject *module, struct Nuitka_MetaPathBasedLoaderEntry const *loader_entry) {
    // Report entry to PGO.
    PGO_onModuleEntered("Foundation$_functiondefines");

    // Store the module for future use.
    module_Foundation$_functiondefines = module;

    moduledict_Foundation$_functiondefines = MODULE_DICT(module_Foundation$_functiondefines);

    // Modules can be loaded again in case of errors, avoid the init being done again.
    static bool init_done = false;

    if (init_done == false) {
#if _NUITKA_MODULE_MODE && 0
        // In case of an extension module loaded into a process, we need to call
        // initialization here because that's the first and potentially only time
        // we are going called.
#if PYTHON_VERSION > 0x350 && !defined(_NUITKA_EXPERIMENTAL_DISABLE_ALLOCATORS)
        initNuitkaAllocators();
#endif
        // Initialize the constant values used.
        _initBuiltinModule();

        PyObject *real_module_name = PyObject_GetAttrString(module, "__name__");
        CHECK_OBJECT(real_module_name);
        module_full_name = strdup(Nuitka_String_AsString(real_module_name));

        createGlobalConstants(tstate, real_module_name);

        /* Initialize the compiled types of Nuitka. */
        _initCompiledCellType();
        _initCompiledGeneratorType();
        _initCompiledFunctionType();
        _initCompiledMethodType();
        _initCompiledFrameType();

        _initSlotCompare();
#if PYTHON_VERSION >= 0x270
        _initSlotIterNext();
#endif

        patchTypeComparison();

        // Enable meta path based loader if not already done.
#ifdef _NUITKA_TRACE
        PRINT_STRING("Foundation$_functiondefines: Calling setupMetaPathBasedLoader().\n");
#endif
        setupMetaPathBasedLoader(tstate);
#if 0 >= 0
#ifdef _NUITKA_TRACE
        PRINT_STRING("Foundation$_functiondefines: Calling updateMetaPathBasedLoaderModuleRoot().\n");
#endif
        updateMetaPathBasedLoaderModuleRoot(module_full_name);
#endif


#if PYTHON_VERSION >= 0x300
        patchInspectModule(tstate);
#endif

#endif

        /* The constants only used by this module are created now. */
        NUITKA_PRINT_TRACE("Foundation$_functiondefines: Calling createModuleConstants().\n");
        createModuleConstants(tstate);

#if !defined(_NUITKA_EXPERIMENTAL_NEW_CODE_OBJECTS)
        createModuleCodeObjects();
#endif
        init_done = true;
    }

#if _NUITKA_MODULE_MODE && 0
    PyObject *pre_load = IMPORT_EMBEDDED_MODULE(tstate, "Foundation._functiondefines" "-preLoad");
    if (pre_load == NULL) {
        return NULL;
    }
#endif

    // PRINT_STRING("in initFoundation$_functiondefines\n");

#ifdef _NUITKA_PLUGIN_DILL_ENABLED
    {
        char const *module_name_c;
        if (loader_entry != NULL) {
            module_name_c = loader_entry->name;
        } else {
            PyObject *module_name = GET_STRING_DICT_VALUE(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)const_str_plain___name__);
            module_name_c = Nuitka_String_AsString(module_name);
        }

        registerDillPluginTables(tstate, module_name_c, &_method_def_reduce_compiled_function, &_method_def_create_compiled_function);
    }
#endif

    // Set "__compiled__" to what version information we have.
    UPDATE_STRING_DICT0(
        moduledict_Foundation$_functiondefines,
        (Nuitka_StringObject *)const_str_plain___compiled__,
        Nuitka_dunder_compiled_value
    );

    // Update "__package__" value to what it ought to be.
    {
#if 0
        UPDATE_STRING_DICT0(
            moduledict_Foundation$_functiondefines,
            (Nuitka_StringObject *)const_str_plain___package__,
            mod_consts[4]
        );
#elif 0
        PyObject *module_name = GET_STRING_DICT_VALUE(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)const_str_plain___name__);

        UPDATE_STRING_DICT0(
            moduledict_Foundation$_functiondefines,
            (Nuitka_StringObject *)const_str_plain___package__,
            module_name
        );
#else

#if PYTHON_VERSION < 0x300
        PyObject *module_name = GET_STRING_DICT_VALUE(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)const_str_plain___name__);
        char const *module_name_cstr = PyString_AS_STRING(module_name);

        char const *last_dot = strrchr(module_name_cstr, '.');

        if (last_dot != NULL) {
            UPDATE_STRING_DICT1(
                moduledict_Foundation$_functiondefines,
                (Nuitka_StringObject *)const_str_plain___package__,
                PyString_FromStringAndSize(module_name_cstr, last_dot - module_name_cstr)
            );
        }
#else
        PyObject *module_name = GET_STRING_DICT_VALUE(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)const_str_plain___name__);
        Py_ssize_t dot_index = PyUnicode_Find(module_name, const_str_dot, 0, PyUnicode_GetLength(module_name), -1);

        if (dot_index != -1) {
            UPDATE_STRING_DICT1(
                moduledict_Foundation$_functiondefines,
                (Nuitka_StringObject *)const_str_plain___package__,
                PyUnicode_Substring(module_name, 0, dot_index)
            );
        }
#endif
#endif
    }

    CHECK_OBJECT(module_Foundation$_functiondefines);

    // For deep importing of a module we need to have "__builtins__", so we set
    // it ourselves in the same way than CPython does. Note: This must be done
    // before the frame object is allocated, or else it may fail.

    if (GET_STRING_DICT_VALUE(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)const_str_plain___builtins__) == NULL) {
        PyObject *value = (PyObject *)builtin_module;

        // Check if main module, not a dict then but the module itself.
#if _NUITKA_MODULE_MODE || !0
        value = PyModule_GetDict(value);
#endif

        UPDATE_STRING_DICT0(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)const_str_plain___builtins__, value);
    }

    PyObject *module_loader = Nuitka_Loader_New(loader_entry);
    UPDATE_STRING_DICT0(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)const_str_plain___loader__, module_loader);

#if PYTHON_VERSION >= 0x300
// Set the "__spec__" value

#if 0
    // Main modules just get "None" as spec.
    UPDATE_STRING_DICT0(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)const_str_plain___spec__, Py_None);
#else
    // Other modules get a "ModuleSpec" from the standard mechanism.
    {
        PyObject *bootstrap_module = getImportLibBootstrapModule();
        CHECK_OBJECT(bootstrap_module);

        PyObject *_spec_from_module = PyObject_GetAttrString(bootstrap_module, "_spec_from_module");
        CHECK_OBJECT(_spec_from_module);

        PyObject *spec_value = CALL_FUNCTION_WITH_SINGLE_ARG(tstate, _spec_from_module, module_Foundation$_functiondefines);
        Py_DECREF(_spec_from_module);

        // We can assume this to never fail, or else we are in trouble anyway.
        // CHECK_OBJECT(spec_value);

        if (spec_value == NULL) {
            PyErr_PrintEx(0);
            abort();
        }

        // Mark the execution in the "__spec__" value.
        SET_ATTRIBUTE(tstate, spec_value, const_str_plain__initializing, Py_True);

#if _NUITKA_MODULE_MODE && 0 && 0 >= 0
        // Set our loader object in the "__spec__" value.
        SET_ATTRIBUTE(tstate, spec_value, const_str_plain_loader, module_loader);
#endif

        UPDATE_STRING_DICT1(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)const_str_plain___spec__, spec_value);
    }
#endif
#endif

    // Temp variables if any
    struct Nuitka_FrameObject *frame_frame_Foundation$_functiondefines;
    NUITKA_MAY_BE_UNUSED char const *type_description_1 = NULL;
    bool tmp_result;
    struct Nuitka_ExceptionPreservationItem exception_state = Empty_Nuitka_ExceptionPreservationItem;
    NUITKA_MAY_BE_UNUSED int exception_lineno = 0;

    // Module init code if any


    // Module code.
    {
        PyObject *tmp_assign_source_1;
        tmp_assign_source_1 = mod_consts[6];
        UPDATE_STRING_DICT0(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)mod_consts[7], tmp_assign_source_1);
    }
    {
        PyObject *tmp_assign_source_2;
        tmp_assign_source_2 = module_filename_obj;
        UPDATE_STRING_DICT0(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)mod_consts[8], tmp_assign_source_2);
    }
    frame_frame_Foundation$_functiondefines = MAKE_MODULE_FRAME(code_objects_537c01b3428486143a3021263f2b2bbb, module_Foundation$_functiondefines);

    // Push the new frame as the currently active one, and we should be exclusively
    // owning it.
    pushFrameStackCompiledFrame(tstate, frame_frame_Foundation$_functiondefines);
    assert(Py_REFCNT(frame_frame_Foundation$_functiondefines) == 2);

    // Framed code:
    {
        PyObject *tmp_assattr_value_1;
        PyObject *tmp_assattr_target_1;
        tmp_assattr_value_1 = module_filename_obj;
        tmp_assattr_target_1 = module_var_accessor_Foundation$$36$_functiondefines$__spec__(tstate);
        assert(!(tmp_assattr_target_1 == NULL));
        tmp_result = SET_ATTRIBUTE(tstate, tmp_assattr_target_1, mod_consts[9], tmp_assattr_value_1);
        if (tmp_result == false) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 1;

            goto frame_exception_exit_1;
        }
    }
    {
        PyObject *tmp_assattr_value_2;
        PyObject *tmp_assattr_target_2;
        tmp_assattr_value_2 = Py_True;
        tmp_assattr_target_2 = module_var_accessor_Foundation$$36$_functiondefines$__spec__(tstate);
        assert(!(tmp_assattr_target_2 == NULL));
        tmp_result = SET_ATTRIBUTE(tstate, tmp_assattr_target_2, mod_consts[10], tmp_assattr_value_2);
        if (tmp_result == false) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 1;

            goto frame_exception_exit_1;
        }
    }
    {
        PyObject *tmp_assign_source_3;
        tmp_assign_source_3 = Py_None;
        UPDATE_STRING_DICT0(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)mod_consts[11], tmp_assign_source_3);
    }
    {
        PyObject *tmp_assign_source_4;
        PyObject *tmp_name_value_1;
        PyObject *tmp_globals_arg_value_1;
        PyObject *tmp_locals_arg_value_1;
        PyObject *tmp_fromlist_value_1;
        PyObject *tmp_level_value_1;
        tmp_name_value_1 = mod_consts[12];
        tmp_globals_arg_value_1 = (PyObject *)moduledict_Foundation$_functiondefines;
        tmp_locals_arg_value_1 = Py_None;
        tmp_fromlist_value_1 = Py_None;
        tmp_level_value_1 = const_int_0;
        frame_frame_Foundation$_functiondefines->m_frame.f_lineno = 5;
        tmp_assign_source_4 = IMPORT_MODULE5(tstate, tmp_name_value_1, tmp_globals_arg_value_1, tmp_locals_arg_value_1, tmp_fromlist_value_1, tmp_level_value_1);
        if (tmp_assign_source_4 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 5;

            goto frame_exception_exit_1;
        }
        UPDATE_STRING_DICT1(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)mod_consts[0], tmp_assign_source_4);
    }


    // Put the previous frame back on top.
    popFrameStack(tstate);

    goto frame_no_exception_1;
    frame_exception_exit_1:


    {
        PyTracebackObject *exception_tb = GET_EXCEPTION_STATE_TRACEBACK(&exception_state);
        if (exception_tb == NULL) {
            exception_tb = MAKE_TRACEBACK(frame_frame_Foundation$_functiondefines, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        } else if (exception_tb->tb_frame != &frame_frame_Foundation$_functiondefines->m_frame) {
            exception_tb = ADD_TRACEBACK(exception_tb, frame_frame_Foundation$_functiondefines, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        }
    }



    assertFrameObject(frame_frame_Foundation$_functiondefines);

    // Put the previous frame back on top.
    popFrameStack(tstate);

    // Return the error.
    goto module_exception_exit;
    frame_no_exception_1:;
    {
        PyObject *tmp_assign_source_5;


        tmp_assign_source_5 = MAKE_FUNCTION_Foundation$_functiondefines$$$function__1_NSLocalizedString(tstate);

        UPDATE_STRING_DICT1(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)mod_consts[13], tmp_assign_source_5);
    }
    {
        PyObject *tmp_assign_source_6;


        tmp_assign_source_6 = MAKE_FUNCTION_Foundation$_functiondefines$$$function__2_NSLocalizedStringFromTable(tstate);

        UPDATE_STRING_DICT1(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)mod_consts[14], tmp_assign_source_6);
    }
    {
        PyObject *tmp_assign_source_7;


        tmp_assign_source_7 = MAKE_FUNCTION_Foundation$_functiondefines$$$function__3_NSLocalizedStringFromTableInBundle(tstate);

        UPDATE_STRING_DICT1(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)mod_consts[15], tmp_assign_source_7);
    }
    {
        PyObject *tmp_assign_source_8;


        tmp_assign_source_8 = MAKE_FUNCTION_Foundation$_functiondefines$$$function__4_NSLocalizedStringWithDefaultValue(tstate);

        UPDATE_STRING_DICT1(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)mod_consts[16], tmp_assign_source_8);
    }
    {
        PyObject *tmp_assign_source_9;


        tmp_assign_source_9 = MAKE_FUNCTION_Foundation$_functiondefines$$$function__5_NSLocalizedAttributedString(tstate);

        UPDATE_STRING_DICT1(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)mod_consts[17], tmp_assign_source_9);
    }
    {
        PyObject *tmp_assign_source_10;


        tmp_assign_source_10 = MAKE_FUNCTION_Foundation$_functiondefines$$$function__6_NSLocalizedAttributedStringFromTable(tstate);

        UPDATE_STRING_DICT1(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)mod_consts[18], tmp_assign_source_10);
    }
    {
        PyObject *tmp_assign_source_11;


        tmp_assign_source_11 = MAKE_FUNCTION_Foundation$_functiondefines$$$function__7_NSLocalizedAttributedStringFromTableInBundle(tstate);

        UPDATE_STRING_DICT1(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)mod_consts[19], tmp_assign_source_11);
    }
    {
        PyObject *tmp_assign_source_12;


        tmp_assign_source_12 = MAKE_FUNCTION_Foundation$_functiondefines$$$function__8_NSLocalizedAttributedStringWithDefaultValue(tstate);

        UPDATE_STRING_DICT1(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)mod_consts[20], tmp_assign_source_12);
    }
    {
        PyObject *tmp_assign_source_13;


        tmp_assign_source_13 = MAKE_FUNCTION_Foundation$_functiondefines$$$function__9_MIN(tstate);

        UPDATE_STRING_DICT1(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)mod_consts[21], tmp_assign_source_13);
    }
    {
        PyObject *tmp_assign_source_14;


        tmp_assign_source_14 = MAKE_FUNCTION_Foundation$_functiondefines$$$function__10_MAX(tstate);

        UPDATE_STRING_DICT1(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)mod_consts[22], tmp_assign_source_14);
    }
    {
        PyObject *tmp_assign_source_15;
        tmp_assign_source_15 = LOOKUP_BUILTIN(mod_consts[23]);
        assert(tmp_assign_source_15 != NULL);
        UPDATE_STRING_DICT0(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)mod_consts[24], tmp_assign_source_15);
    }

    // Report to PGO about leaving the module without error.
    PGO_onModuleExit("Foundation$_functiondefines", false);

#if _NUITKA_MODULE_MODE && 0
    {
        PyObject *post_load = IMPORT_EMBEDDED_MODULE(tstate, "Foundation._functiondefines" "-postLoad");
        if (post_load == NULL) {
            return NULL;
        }
    }
#endif

    Py_INCREF(module_Foundation$_functiondefines);
    return module_Foundation$_functiondefines;
    module_exception_exit:

#if _NUITKA_MODULE_MODE && 0
    {
        PyObject *module_name = GET_STRING_DICT_VALUE(moduledict_Foundation$_functiondefines, (Nuitka_StringObject *)const_str_plain___name__);

        if (module_name != NULL) {
            Nuitka_DelModule(tstate, module_name);
        }
    }
#endif
    PGO_onModuleExit("Foundation$_functiondefines", false);

    RESTORE_ERROR_OCCURRED_STATE(tstate, &exception_state);
    return NULL;
}
