/* Generated code for Python module 'Quartz$ImageKit$_metadata'
 * created by Nuitka version 2.7.5
 *
 * This code is in part copyright 2025 Kay <PERSON>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "nuitka/prelude.h"

#include "nuitka/unfreezing.h"

#include "__helpers.h"

/* The "module_Quartz$ImageKit$_metadata" is a Python object pointer of module type.
 *
 * Note: For full compatibility with CPython, every module variable access
 * needs to go through it except for cases where the module cannot possibly
 * have changed in the mean time.
 */

PyObject *module_Quartz$ImageKit$_metadata;
PyDictObject *moduledict_Quartz$ImageKit$_metadata;

/* The declarations of module constants used, if any. */
static PyObject *mod_consts[255];
#ifndef __NUITKA_NO_ASSERT__
static Py_hash_t mod_consts_hash[255];
#endif

static PyObject *module_filename_obj = NULL;

/* Indicator if this modules private constants were created yet. */
static bool constants_created = false;

/* Function to create module private constants. */
static void createModuleConstants(PyThreadState *tstate) {
    if (constants_created == false) {
        loadConstantsBlob(tstate, &mod_consts[0], UN_TRANSLATE("Quartz.ImageKit._metadata"));
        constants_created = true;

#ifndef __NUITKA_NO_ASSERT__
        for (int i = 0; i < 255; i++) {
            mod_consts_hash[i] = DEEP_HASH(tstate, mod_consts[i]);
        }
#endif
    }
}

// We want to be able to initialize the "__main__" constants in any case.
#if 0
void createMainModuleConstants(PyThreadState *tstate) {
    createModuleConstants(tstate);
}
#endif

/* Function to verify module private constants for non-corruption. */
#ifndef __NUITKA_NO_ASSERT__
void checkModuleConstants_Quartz$ImageKit$_metadata(PyThreadState *tstate) {
    // The module may not have been used at all, then ignore this.
    if (constants_created == false) return;

    for (int i = 0; i < 255; i++) {
        assert(mod_consts_hash[i] == DEEP_HASH(tstate, mod_consts[i]));
        CHECK_OBJECT_DEEP(mod_consts[i]);
    }
}
#endif

// Helper to preserving module variables for Python3.11+
#if 5
#if PYTHON_VERSION >= 0x3c0
NUITKA_MAY_BE_UNUSED static uint32_t _Nuitka_PyDictKeys_GetVersionForCurrentState(PyInterpreterState *interp, PyDictKeysObject *dk)
{
    if (dk->dk_version != 0) {
        return dk->dk_version;
    }
    uint32_t result = interp->dict_state.next_keys_version++;
    dk->dk_version = result;
    return result;
}
#elif PYTHON_VERSION >= 0x3b0
static uint32_t _Nuitka_next_dict_keys_version = 2;

NUITKA_MAY_BE_UNUSED static uint32_t _Nuitka_PyDictKeys_GetVersionForCurrentState(PyDictKeysObject *dk)
{
    if (dk->dk_version != 0) {
        return dk->dk_version;
    }
    uint32_t result = _Nuitka_next_dict_keys_version++;
    dk->dk_version = result;
    return result;
}
#endif
#endif

// Accessors to module variables.
static PyObject *module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$NewType(PyThreadState *tstate) {
#if 0
    PyObject *result;

#if PYTHON_VERSION < 0x3b0
    static uint64_t dict_version = 0;
    static PyObject *cache_value = NULL;

    if (moduledict_Quartz$ImageKit$_metadata->ma_version_tag == dict_version) {
        CHECK_OBJECT_X(cache_value);
        result = cache_value;
    } else {
        dict_version = moduledict_Quartz$ImageKit$_metadata->ma_version_tag;

        result = GET_STRING_DICT_VALUE(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[7]);
        cache_value = result;
    }
#else
    static uint32_t dict_keys_version = 0xFFFFFFFF;
    static Py_ssize_t cache_dk_index = 0;

    PyDictKeysObject *dk = moduledict_Quartz$ImageKit$_metadata->ma_keys;
    if (likely(DK_IS_UNICODE(dk))) {

#if PYTHON_VERSION >= 0x3c0
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(tstate->interp, dk);
#else
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(dk);
#endif

        if (current_dk_version != dict_keys_version) {
            dict_keys_version = current_dk_version;
            Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[7]);
            assert(hash != -1);

            cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[7], hash);
        }

        if (cache_dk_index >= 0) {
            assert(dk->dk_kind != DICT_KEYS_SPLIT);

            PyDictUnicodeEntry *entries = DK_UNICODE_ENTRIES(dk);

            result = entries[cache_dk_index].me_value;

            if (unlikely(result == NULL)) {
                Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[7]);
                assert(hash != -1);

                cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[7], hash);

                if (cache_dk_index >= 0) {
                    result = entries[cache_dk_index].me_value;
                }
            }
        } else {
            result = NULL;
        }
    } else {
        result = GET_STRING_DICT_VALUE(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[7]);
    }
#endif

#else
    PyObject *result = GET_STRING_DICT_VALUE(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[7]);
#endif

    if (unlikely(result == NULL)) {
        result = GET_STRING_DICT_VALUE(dict_builtin, (Nuitka_StringObject *)mod_consts[7]);
    }

    return result;
}

static PyObject *module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$__spec__(PyThreadState *tstate) {
#if 0
    PyObject *result;

#if PYTHON_VERSION < 0x3b0
    static uint64_t dict_version = 0;
    static PyObject *cache_value = NULL;

    if (moduledict_Quartz$ImageKit$_metadata->ma_version_tag == dict_version) {
        CHECK_OBJECT_X(cache_value);
        result = cache_value;
    } else {
        dict_version = moduledict_Quartz$ImageKit$_metadata->ma_version_tag;

        result = GET_STRING_DICT_VALUE(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[254]);
        cache_value = result;
    }
#else
    static uint32_t dict_keys_version = 0xFFFFFFFF;
    static Py_ssize_t cache_dk_index = 0;

    PyDictKeysObject *dk = moduledict_Quartz$ImageKit$_metadata->ma_keys;
    if (likely(DK_IS_UNICODE(dk))) {

#if PYTHON_VERSION >= 0x3c0
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(tstate->interp, dk);
#else
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(dk);
#endif

        if (current_dk_version != dict_keys_version) {
            dict_keys_version = current_dk_version;
            Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[254]);
            assert(hash != -1);

            cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[254], hash);
        }

        if (cache_dk_index >= 0) {
            assert(dk->dk_kind != DICT_KEYS_SPLIT);

            PyDictUnicodeEntry *entries = DK_UNICODE_ENTRIES(dk);

            result = entries[cache_dk_index].me_value;

            if (unlikely(result == NULL)) {
                Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[254]);
                assert(hash != -1);

                cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[254], hash);

                if (cache_dk_index >= 0) {
                    result = entries[cache_dk_index].me_value;
                }
            }
        } else {
            result = NULL;
        }
    } else {
        result = GET_STRING_DICT_VALUE(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[254]);
    }
#endif

#else
    PyObject *result = GET_STRING_DICT_VALUE(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[254]);
#endif

    if (unlikely(result == NULL)) {
        result = GET_STRING_DICT_VALUE(dict_builtin, (Nuitka_StringObject *)mod_consts[254]);
    }

    return result;
}

static PyObject *module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$misc(PyThreadState *tstate) {
#if 0
    PyObject *result;

#if PYTHON_VERSION < 0x3b0
    static uint64_t dict_version = 0;
    static PyObject *cache_value = NULL;

    if (moduledict_Quartz$ImageKit$_metadata->ma_version_tag == dict_version) {
        CHECK_OBJECT_X(cache_value);
        result = cache_value;
    } else {
        dict_version = moduledict_Quartz$ImageKit$_metadata->ma_version_tag;

        result = GET_STRING_DICT_VALUE(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[12]);
        cache_value = result;
    }
#else
    static uint32_t dict_keys_version = 0xFFFFFFFF;
    static Py_ssize_t cache_dk_index = 0;

    PyDictKeysObject *dk = moduledict_Quartz$ImageKit$_metadata->ma_keys;
    if (likely(DK_IS_UNICODE(dk))) {

#if PYTHON_VERSION >= 0x3c0
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(tstate->interp, dk);
#else
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(dk);
#endif

        if (current_dk_version != dict_keys_version) {
            dict_keys_version = current_dk_version;
            Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[12]);
            assert(hash != -1);

            cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[12], hash);
        }

        if (cache_dk_index >= 0) {
            assert(dk->dk_kind != DICT_KEYS_SPLIT);

            PyDictUnicodeEntry *entries = DK_UNICODE_ENTRIES(dk);

            result = entries[cache_dk_index].me_value;

            if (unlikely(result == NULL)) {
                Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[12]);
                assert(hash != -1);

                cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[12], hash);

                if (cache_dk_index >= 0) {
                    result = entries[cache_dk_index].me_value;
                }
            }
        } else {
            result = NULL;
        }
    } else {
        result = GET_STRING_DICT_VALUE(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[12]);
    }
#endif

#else
    PyObject *result = GET_STRING_DICT_VALUE(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[12]);
#endif

    if (unlikely(result == NULL)) {
        result = GET_STRING_DICT_VALUE(dict_builtin, (Nuitka_StringObject *)mod_consts[12]);
    }

    return result;
}

static PyObject *module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(PyThreadState *tstate) {
#if 0
    PyObject *result;

#if PYTHON_VERSION < 0x3b0
    static uint64_t dict_version = 0;
    static PyObject *cache_value = NULL;

    if (moduledict_Quartz$ImageKit$_metadata->ma_version_tag == dict_version) {
        CHECK_OBJECT_X(cache_value);
        result = cache_value;
    } else {
        dict_version = moduledict_Quartz$ImageKit$_metadata->ma_version_tag;

        result = GET_STRING_DICT_VALUE(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[5]);
        cache_value = result;
    }
#else
    static uint32_t dict_keys_version = 0xFFFFFFFF;
    static Py_ssize_t cache_dk_index = 0;

    PyDictKeysObject *dk = moduledict_Quartz$ImageKit$_metadata->ma_keys;
    if (likely(DK_IS_UNICODE(dk))) {

#if PYTHON_VERSION >= 0x3c0
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(tstate->interp, dk);
#else
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(dk);
#endif

        if (current_dk_version != dict_keys_version) {
            dict_keys_version = current_dk_version;
            Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[5]);
            assert(hash != -1);

            cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[5], hash);
        }

        if (cache_dk_index >= 0) {
            assert(dk->dk_kind != DICT_KEYS_SPLIT);

            PyDictUnicodeEntry *entries = DK_UNICODE_ENTRIES(dk);

            result = entries[cache_dk_index].me_value;

            if (unlikely(result == NULL)) {
                Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[5]);
                assert(hash != -1);

                cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[5], hash);

                if (cache_dk_index >= 0) {
                    result = entries[cache_dk_index].me_value;
                }
            }
        } else {
            result = NULL;
        }
    } else {
        result = GET_STRING_DICT_VALUE(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[5]);
    }
#endif

#else
    PyObject *result = GET_STRING_DICT_VALUE(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[5]);
#endif

    if (unlikely(result == NULL)) {
        result = GET_STRING_DICT_VALUE(dict_builtin, (Nuitka_StringObject *)mod_consts[5]);
    }

    return result;
}

static PyObject *module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(PyThreadState *tstate) {
#if 0
    PyObject *result;

#if PYTHON_VERSION < 0x3b0
    static uint64_t dict_version = 0;
    static PyObject *cache_value = NULL;

    if (moduledict_Quartz$ImageKit$_metadata->ma_version_tag == dict_version) {
        CHECK_OBJECT_X(cache_value);
        result = cache_value;
    } else {
        dict_version = moduledict_Quartz$ImageKit$_metadata->ma_version_tag;

        result = GET_STRING_DICT_VALUE(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[35]);
        cache_value = result;
    }
#else
    static uint32_t dict_keys_version = 0xFFFFFFFF;
    static Py_ssize_t cache_dk_index = 0;

    PyDictKeysObject *dk = moduledict_Quartz$ImageKit$_metadata->ma_keys;
    if (likely(DK_IS_UNICODE(dk))) {

#if PYTHON_VERSION >= 0x3c0
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(tstate->interp, dk);
#else
        uint32_t current_dk_version = _Nuitka_PyDictKeys_GetVersionForCurrentState(dk);
#endif

        if (current_dk_version != dict_keys_version) {
            dict_keys_version = current_dk_version;
            Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[35]);
            assert(hash != -1);

            cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[35], hash);
        }

        if (cache_dk_index >= 0) {
            assert(dk->dk_kind != DICT_KEYS_SPLIT);

            PyDictUnicodeEntry *entries = DK_UNICODE_ENTRIES(dk);

            result = entries[cache_dk_index].me_value;

            if (unlikely(result == NULL)) {
                Py_hash_t hash = Nuitka_Py_unicode_get_hash(mod_consts[35]);
                assert(hash != -1);

                cache_dk_index = Nuitka_Py_unicodekeys_lookup_unicode(dk, mod_consts[35], hash);

                if (cache_dk_index >= 0) {
                    result = entries[cache_dk_index].me_value;
                }
            }
        } else {
            result = NULL;
        }
    } else {
        result = GET_STRING_DICT_VALUE(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[35]);
    }
#endif

#else
    PyObject *result = GET_STRING_DICT_VALUE(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[35]);
#endif

    if (unlikely(result == NULL)) {
        result = GET_STRING_DICT_VALUE(dict_builtin, (Nuitka_StringObject *)mod_consts[35]);
    }

    return result;
}


#if !defined(_NUITKA_EXPERIMENTAL_NEW_CODE_OBJECTS)
// The module code objects.
static PyCodeObject *code_objects_da92bafc5c82fc84207b1679639494b3;
static PyCodeObject *code_objects_57504da7f33b2a868ff9d017bf6faf05;
static PyCodeObject *code_objects_f284234150946c31ce6f72ffb74ce62f;
static PyCodeObject *code_objects_720b7a5ccc066117d2a54c8bd3255dbe;

static void createModuleCodeObjects(void) {
    module_filename_obj = MAKE_RELATIVE_PATH(mod_consts[250]); CHECK_OBJECT(module_filename_obj);
    code_objects_da92bafc5c82fc84207b1679639494b3 = MAKE_CODE_OBJECT(module_filename_obj, 1, 0, mod_consts[251], mod_consts[251], NULL, NULL, 0, 0, 0);
    code_objects_57504da7f33b2a868ff9d017bf6faf05 = MAKE_CODE_OBJECT(module_filename_obj, 12, CO_OPTIMIZED | CO_NEWLOCALS, mod_consts[8], mod_consts[8], mod_consts[252], NULL, 2, 0, 0);
    code_objects_f284234150946c31ce6f72ffb74ce62f = MAKE_CODE_OBJECT(module_filename_obj, 23, CO_OPTIMIZED | CO_NEWLOCALS, mod_consts[11], mod_consts[11], mod_consts[252], NULL, 2, 0, 0);
    code_objects_720b7a5ccc066117d2a54c8bd3255dbe = MAKE_CODE_OBJECT(module_filename_obj, 28, CO_OPTIMIZED | CO_NEWLOCALS, mod_consts[11], mod_consts[11], mod_consts[252], NULL, 2, 0, 0);
}
#endif

// The module function declarations.
static PyObject *MAKE_FUNCTION_Quartz$ImageKit$_metadata$$$function__1_sel32or64(PyThreadState *tstate);


static PyObject *MAKE_FUNCTION_Quartz$ImageKit$_metadata$$$function__3_selAorI(PyThreadState *tstate);


static PyObject *MAKE_FUNCTION_Quartz$ImageKit$_metadata$$$function__4_selAorI(PyThreadState *tstate);


// The module function definitions.
static PyObject *impl_Quartz$ImageKit$_metadata$$$function__1_sel32or64(PyThreadState *tstate, struct Nuitka_FunctionObject const *self, PyObject **python_pars) {
    // Preserve error status for checks
#ifndef __NUITKA_NO_ASSERT__
    NUITKA_MAY_BE_UNUSED bool had_error = HAS_ERROR_OCCURRED(tstate);
#endif

    // Local variable declarations.
    PyObject *par_a = python_pars[0];
    PyObject *par_b = python_pars[1];
    PyObject *tmp_return_value = NULL;

    // Actual function body.
    CHECK_OBJECT(par_b);
    tmp_return_value = par_b;
    Py_INCREF(tmp_return_value);
    goto function_return_exit;

    NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
    return NULL;


function_return_exit:
   // Function cleanup code if any.
    CHECK_OBJECT(par_a);
    Py_DECREF(par_a);
    CHECK_OBJECT(par_b);
    Py_DECREF(par_b);

   // Actual function exit with return value, making sure we did not make
   // the error status worse despite non-NULL return.
   CHECK_OBJECT(tmp_return_value);
   assert(had_error || !HAS_ERROR_OCCURRED(tstate));
   return tmp_return_value;
}


static PyObject *impl_Quartz$ImageKit$_metadata$$$function__3_selAorI(PyThreadState *tstate, struct Nuitka_FunctionObject const *self, PyObject **python_pars) {
    // Preserve error status for checks
#ifndef __NUITKA_NO_ASSERT__
    NUITKA_MAY_BE_UNUSED bool had_error = HAS_ERROR_OCCURRED(tstate);
#endif

    // Local variable declarations.
    PyObject *par_a = python_pars[0];
    PyObject *par_b = python_pars[1];
    PyObject *tmp_return_value = NULL;

    // Actual function body.
    CHECK_OBJECT(par_a);
    tmp_return_value = par_a;
    Py_INCREF(tmp_return_value);
    goto function_return_exit;

    NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
    return NULL;


function_return_exit:
   // Function cleanup code if any.
    CHECK_OBJECT(par_a);
    Py_DECREF(par_a);
    CHECK_OBJECT(par_b);
    Py_DECREF(par_b);

   // Actual function exit with return value, making sure we did not make
   // the error status worse despite non-NULL return.
   CHECK_OBJECT(tmp_return_value);
   assert(had_error || !HAS_ERROR_OCCURRED(tstate));
   return tmp_return_value;
}


static PyObject *impl_Quartz$ImageKit$_metadata$$$function__4_selAorI(PyThreadState *tstate, struct Nuitka_FunctionObject const *self, PyObject **python_pars) {
    // Preserve error status for checks
#ifndef __NUITKA_NO_ASSERT__
    NUITKA_MAY_BE_UNUSED bool had_error = HAS_ERROR_OCCURRED(tstate);
#endif

    // Local variable declarations.
    PyObject *par_a = python_pars[0];
    PyObject *par_b = python_pars[1];
    PyObject *tmp_return_value = NULL;

    // Actual function body.
    CHECK_OBJECT(par_b);
    tmp_return_value = par_b;
    Py_INCREF(tmp_return_value);
    goto function_return_exit;

    NUITKA_CANNOT_GET_HERE("Return statement must have exited already.");
    return NULL;


function_return_exit:
   // Function cleanup code if any.
    CHECK_OBJECT(par_a);
    Py_DECREF(par_a);
    CHECK_OBJECT(par_b);
    Py_DECREF(par_b);

   // Actual function exit with return value, making sure we did not make
   // the error status worse despite non-NULL return.
   CHECK_OBJECT(tmp_return_value);
   assert(had_error || !HAS_ERROR_OCCURRED(tstate));
   return tmp_return_value;
}



static PyObject *MAKE_FUNCTION_Quartz$ImageKit$_metadata$$$function__1_sel32or64(PyThreadState *tstate) {
    struct Nuitka_FunctionObject *result = Nuitka_Function_New(
        impl_Quartz$ImageKit$_metadata$$$function__1_sel32or64,
        mod_consts[8],
#if PYTHON_VERSION >= 0x300
        NULL,
#endif
        code_objects_57504da7f33b2a868ff9d017bf6faf05,
        NULL,
#if PYTHON_VERSION >= 0x300
        NULL,
        NULL,
#endif
        module_Quartz$ImageKit$_metadata,
        NULL,
        NULL,
        0
    );


    return (PyObject *)result;
}



static PyObject *MAKE_FUNCTION_Quartz$ImageKit$_metadata$$$function__3_selAorI(PyThreadState *tstate) {
    struct Nuitka_FunctionObject *result = Nuitka_Function_New(
        impl_Quartz$ImageKit$_metadata$$$function__3_selAorI,
        mod_consts[11],
#if PYTHON_VERSION >= 0x300
        NULL,
#endif
        code_objects_f284234150946c31ce6f72ffb74ce62f,
        NULL,
#if PYTHON_VERSION >= 0x300
        NULL,
        NULL,
#endif
        module_Quartz$ImageKit$_metadata,
        NULL,
        NULL,
        0
    );


    return (PyObject *)result;
}



static PyObject *MAKE_FUNCTION_Quartz$ImageKit$_metadata$$$function__4_selAorI(PyThreadState *tstate) {
    struct Nuitka_FunctionObject *result = Nuitka_Function_New(
        impl_Quartz$ImageKit$_metadata$$$function__4_selAorI,
        mod_consts[11],
#if PYTHON_VERSION >= 0x300
        NULL,
#endif
        code_objects_720b7a5ccc066117d2a54c8bd3255dbe,
        NULL,
#if PYTHON_VERSION >= 0x300
        NULL,
        NULL,
#endif
        module_Quartz$ImageKit$_metadata,
        NULL,
        NULL,
        0
    );


    return (PyObject *)result;
}


extern void _initCompiledCellType();
extern void _initCompiledGeneratorType();
extern void _initCompiledFunctionType();
extern void _initCompiledMethodType();
extern void _initCompiledFrameType();

extern PyTypeObject Nuitka_Loader_Type;

#ifdef _NUITKA_PLUGIN_DILL_ENABLED
// Provide a way to create find a function via its C code and create it back
// in another process, useful for multiprocessing extensions like dill
extern void registerDillPluginTables(PyThreadState *tstate, char const *module_name, PyMethodDef *reduce_compiled_function, PyMethodDef *create_compiled_function);

static function_impl_code const function_table_Quartz$ImageKit$_metadata[] = {
    impl_Quartz$ImageKit$_metadata$$$function__1_sel32or64,
    impl_Quartz$ImageKit$_metadata$$$function__3_selAorI,
    impl_Quartz$ImageKit$_metadata$$$function__4_selAorI,
    NULL
};

static PyObject *_reduce_compiled_function(PyObject *self, PyObject *args, PyObject *kwds) {
    PyObject *func;

    if (!PyArg_ParseTuple(args, "O:reduce_compiled_function", &func, NULL)) {
        return NULL;
    }

    if (Nuitka_Function_Check(func) == false) {
        PyThreadState *tstate = PyThreadState_GET();

        SET_CURRENT_EXCEPTION_TYPE0_STR(tstate, PyExc_TypeError, "not a compiled function");
        return NULL;
    }

    struct Nuitka_FunctionObject *function = (struct Nuitka_FunctionObject *)func;

    return Nuitka_Function_GetFunctionState(function, function_table_Quartz$ImageKit$_metadata);
}

static PyMethodDef _method_def_reduce_compiled_function = {"reduce_compiled_function", (PyCFunction)_reduce_compiled_function,
                                                           METH_VARARGS, NULL};


static PyObject *_create_compiled_function(PyObject *self, PyObject *args, PyObject *kwds) {
    CHECK_OBJECT_DEEP(args);

    PyObject *function_index;
    PyObject *code_object_desc;
    PyObject *defaults;
    PyObject *kw_defaults;
    PyObject *doc;
    PyObject *constant_return_value;
    PyObject *function_qualname;
    PyObject *closure;
    PyObject *annotations;
    PyObject *func_dict;

    if (!PyArg_ParseTuple(args, "OOOOOOOOOO:create_compiled_function", &function_index, &code_object_desc, &defaults, &kw_defaults, &doc, &constant_return_value, &function_qualname, &closure, &annotations, &func_dict, NULL)) {
        return NULL;
    }

    return (PyObject *)Nuitka_Function_CreateFunctionViaCodeIndex(
        module_Quartz$ImageKit$_metadata,
        function_qualname,
        function_index,
        code_object_desc,
        constant_return_value,
        defaults,
        kw_defaults,
        doc,
        closure,
        annotations,
        func_dict,
        function_table_Quartz$ImageKit$_metadata,
        sizeof(function_table_Quartz$ImageKit$_metadata) / sizeof(function_impl_code)
    );
}

static PyMethodDef _method_def_create_compiled_function = {
    "create_compiled_function",
    (PyCFunction)_create_compiled_function,
    METH_VARARGS, NULL
};


#endif

// Actual name might be different when loaded as a package.
#if _NUITKA_MODULE_MODE && 0
static char const *module_full_name = "Quartz.ImageKit._metadata";
#endif

// Internal entry point for module code.
PyObject *modulecode_Quartz$ImageKit$_metadata(PyThreadState *tstate, PyObject *module, struct Nuitka_MetaPathBasedLoaderEntry const *loader_entry) {
    // Report entry to PGO.
    PGO_onModuleEntered("Quartz$ImageKit$_metadata");

    // Store the module for future use.
    module_Quartz$ImageKit$_metadata = module;

    moduledict_Quartz$ImageKit$_metadata = MODULE_DICT(module_Quartz$ImageKit$_metadata);

    // Modules can be loaded again in case of errors, avoid the init being done again.
    static bool init_done = false;

    if (init_done == false) {
#if _NUITKA_MODULE_MODE && 0
        // In case of an extension module loaded into a process, we need to call
        // initialization here because that's the first and potentially only time
        // we are going called.
#if PYTHON_VERSION > 0x350 && !defined(_NUITKA_EXPERIMENTAL_DISABLE_ALLOCATORS)
        initNuitkaAllocators();
#endif
        // Initialize the constant values used.
        _initBuiltinModule();

        PyObject *real_module_name = PyObject_GetAttrString(module, "__name__");
        CHECK_OBJECT(real_module_name);
        module_full_name = strdup(Nuitka_String_AsString(real_module_name));

        createGlobalConstants(tstate, real_module_name);

        /* Initialize the compiled types of Nuitka. */
        _initCompiledCellType();
        _initCompiledGeneratorType();
        _initCompiledFunctionType();
        _initCompiledMethodType();
        _initCompiledFrameType();

        _initSlotCompare();
#if PYTHON_VERSION >= 0x270
        _initSlotIterNext();
#endif

        patchTypeComparison();

        // Enable meta path based loader if not already done.
#ifdef _NUITKA_TRACE
        PRINT_STRING("Quartz$ImageKit$_metadata: Calling setupMetaPathBasedLoader().\n");
#endif
        setupMetaPathBasedLoader(tstate);
#if 0 >= 0
#ifdef _NUITKA_TRACE
        PRINT_STRING("Quartz$ImageKit$_metadata: Calling updateMetaPathBasedLoaderModuleRoot().\n");
#endif
        updateMetaPathBasedLoaderModuleRoot(module_full_name);
#endif


#if PYTHON_VERSION >= 0x300
        patchInspectModule(tstate);
#endif

#endif

        /* The constants only used by this module are created now. */
        NUITKA_PRINT_TRACE("Quartz$ImageKit$_metadata: Calling createModuleConstants().\n");
        createModuleConstants(tstate);

#if !defined(_NUITKA_EXPERIMENTAL_NEW_CODE_OBJECTS)
        createModuleCodeObjects();
#endif
        init_done = true;
    }

#if _NUITKA_MODULE_MODE && 0
    PyObject *pre_load = IMPORT_EMBEDDED_MODULE(tstate, "Quartz.ImageKit._metadata" "-preLoad");
    if (pre_load == NULL) {
        return NULL;
    }
#endif

    // PRINT_STRING("in initQuartz$ImageKit$_metadata\n");

#ifdef _NUITKA_PLUGIN_DILL_ENABLED
    {
        char const *module_name_c;
        if (loader_entry != NULL) {
            module_name_c = loader_entry->name;
        } else {
            PyObject *module_name = GET_STRING_DICT_VALUE(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)const_str_plain___name__);
            module_name_c = Nuitka_String_AsString(module_name);
        }

        registerDillPluginTables(tstate, module_name_c, &_method_def_reduce_compiled_function, &_method_def_create_compiled_function);
    }
#endif

    // Set "__compiled__" to what version information we have.
    UPDATE_STRING_DICT0(
        moduledict_Quartz$ImageKit$_metadata,
        (Nuitka_StringObject *)const_str_plain___compiled__,
        Nuitka_dunder_compiled_value
    );

    // Update "__package__" value to what it ought to be.
    {
#if 0
        UPDATE_STRING_DICT0(
            moduledict_Quartz$ImageKit$_metadata,
            (Nuitka_StringObject *)const_str_plain___package__,
            mod_consts[253]
        );
#elif 0
        PyObject *module_name = GET_STRING_DICT_VALUE(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)const_str_plain___name__);

        UPDATE_STRING_DICT0(
            moduledict_Quartz$ImageKit$_metadata,
            (Nuitka_StringObject *)const_str_plain___package__,
            module_name
        );
#else

#if PYTHON_VERSION < 0x300
        PyObject *module_name = GET_STRING_DICT_VALUE(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)const_str_plain___name__);
        char const *module_name_cstr = PyString_AS_STRING(module_name);

        char const *last_dot = strrchr(module_name_cstr, '.');

        if (last_dot != NULL) {
            UPDATE_STRING_DICT1(
                moduledict_Quartz$ImageKit$_metadata,
                (Nuitka_StringObject *)const_str_plain___package__,
                PyString_FromStringAndSize(module_name_cstr, last_dot - module_name_cstr)
            );
        }
#else
        PyObject *module_name = GET_STRING_DICT_VALUE(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)const_str_plain___name__);
        Py_ssize_t dot_index = PyUnicode_Find(module_name, const_str_dot, 0, PyUnicode_GetLength(module_name), -1);

        if (dot_index != -1) {
            UPDATE_STRING_DICT1(
                moduledict_Quartz$ImageKit$_metadata,
                (Nuitka_StringObject *)const_str_plain___package__,
                PyUnicode_Substring(module_name, 0, dot_index)
            );
        }
#endif
#endif
    }

    CHECK_OBJECT(module_Quartz$ImageKit$_metadata);

    // For deep importing of a module we need to have "__builtins__", so we set
    // it ourselves in the same way than CPython does. Note: This must be done
    // before the frame object is allocated, or else it may fail.

    if (GET_STRING_DICT_VALUE(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)const_str_plain___builtins__) == NULL) {
        PyObject *value = (PyObject *)builtin_module;

        // Check if main module, not a dict then but the module itself.
#if _NUITKA_MODULE_MODE || !0
        value = PyModule_GetDict(value);
#endif

        UPDATE_STRING_DICT0(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)const_str_plain___builtins__, value);
    }

    PyObject *module_loader = Nuitka_Loader_New(loader_entry);
    UPDATE_STRING_DICT0(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)const_str_plain___loader__, module_loader);

#if PYTHON_VERSION >= 0x300
// Set the "__spec__" value

#if 0
    // Main modules just get "None" as spec.
    UPDATE_STRING_DICT0(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)const_str_plain___spec__, Py_None);
#else
    // Other modules get a "ModuleSpec" from the standard mechanism.
    {
        PyObject *bootstrap_module = getImportLibBootstrapModule();
        CHECK_OBJECT(bootstrap_module);

        PyObject *_spec_from_module = PyObject_GetAttrString(bootstrap_module, "_spec_from_module");
        CHECK_OBJECT(_spec_from_module);

        PyObject *spec_value = CALL_FUNCTION_WITH_SINGLE_ARG(tstate, _spec_from_module, module_Quartz$ImageKit$_metadata);
        Py_DECREF(_spec_from_module);

        // We can assume this to never fail, or else we are in trouble anyway.
        // CHECK_OBJECT(spec_value);

        if (spec_value == NULL) {
            PyErr_PrintEx(0);
            abort();
        }

        // Mark the execution in the "__spec__" value.
        SET_ATTRIBUTE(tstate, spec_value, const_str_plain__initializing, Py_True);

#if _NUITKA_MODULE_MODE && 0 && 0 >= 0
        // Set our loader object in the "__spec__" value.
        SET_ATTRIBUTE(tstate, spec_value, const_str_plain_loader, module_loader);
#endif

        UPDATE_STRING_DICT1(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)const_str_plain___spec__, spec_value);
    }
#endif
#endif

    // Temp variables if any
    struct Nuitka_FrameObject *frame_frame_Quartz$ImageKit$_metadata;
    NUITKA_MAY_BE_UNUSED char const *type_description_1 = NULL;
    bool tmp_result;
    struct Nuitka_ExceptionPreservationItem exception_state = Empty_Nuitka_ExceptionPreservationItem;
    NUITKA_MAY_BE_UNUSED int exception_lineno = 0;
    NUITKA_MAY_BE_UNUSED nuitka_void tmp_unused;
    int tmp_res;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_1;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_1;
    struct Nuitka_ExceptionStackItem exception_preserved_1;
    struct Nuitka_ExceptionPreservationItem exception_keeper_name_2;
    NUITKA_MAY_BE_UNUSED int exception_keeper_lineno_2;

    // Module init code if any


    // Module code.
    {
        PyObject *tmp_assign_source_1;
        tmp_assign_source_1 = Py_None;
        UPDATE_STRING_DICT0(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[0], tmp_assign_source_1);
    }
    {
        PyObject *tmp_assign_source_2;
        tmp_assign_source_2 = module_filename_obj;
        UPDATE_STRING_DICT0(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[1], tmp_assign_source_2);
    }
    frame_frame_Quartz$ImageKit$_metadata = MAKE_MODULE_FRAME(code_objects_da92bafc5c82fc84207b1679639494b3, module_Quartz$ImageKit$_metadata);

    // Push the new frame as the currently active one, and we should be exclusively
    // owning it.
    pushFrameStackCompiledFrame(tstate, frame_frame_Quartz$ImageKit$_metadata);
    assert(Py_REFCNT(frame_frame_Quartz$ImageKit$_metadata) == 2);

    // Framed code:
    {
        PyObject *tmp_assattr_value_1;
        PyObject *tmp_assattr_target_1;
        tmp_assattr_value_1 = module_filename_obj;
        tmp_assattr_target_1 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$__spec__(tstate);
        assert(!(tmp_assattr_target_1 == NULL));
        tmp_result = SET_ATTRIBUTE(tstate, tmp_assattr_target_1, mod_consts[2], tmp_assattr_value_1);
        if (tmp_result == false) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 1;

            goto frame_exception_exit_1;
        }
    }
    {
        PyObject *tmp_assattr_value_2;
        PyObject *tmp_assattr_target_2;
        tmp_assattr_value_2 = Py_True;
        tmp_assattr_target_2 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$__spec__(tstate);
        assert(!(tmp_assattr_target_2 == NULL));
        tmp_result = SET_ATTRIBUTE(tstate, tmp_assattr_target_2, mod_consts[3], tmp_assattr_value_2);
        if (tmp_result == false) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 1;

            goto frame_exception_exit_1;
        }
    }
    {
        PyObject *tmp_assign_source_3;
        tmp_assign_source_3 = Py_None;
        UPDATE_STRING_DICT0(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[4], tmp_assign_source_3);
    }
    {
        PyObject *tmp_assign_source_4;
        PyObject *tmp_name_value_1;
        PyObject *tmp_globals_arg_value_1;
        PyObject *tmp_locals_arg_value_1;
        PyObject *tmp_fromlist_value_1;
        PyObject *tmp_level_value_1;
        tmp_name_value_1 = mod_consts[5];
        tmp_globals_arg_value_1 = (PyObject *)moduledict_Quartz$ImageKit$_metadata;
        tmp_locals_arg_value_1 = Py_None;
        tmp_fromlist_value_1 = Py_None;
        tmp_level_value_1 = const_int_0;
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 7;
        tmp_assign_source_4 = IMPORT_MODULE5(tstate, tmp_name_value_1, tmp_globals_arg_value_1, tmp_locals_arg_value_1, tmp_fromlist_value_1, tmp_level_value_1);
        if (tmp_assign_source_4 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 7;

            goto frame_exception_exit_1;
        }
        UPDATE_STRING_DICT1(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[5], tmp_assign_source_4);
    }
    {
        PyObject *tmp_assign_source_5;
        tmp_assign_source_5 = IMPORT_HARD_SYS();
        assert(!(tmp_assign_source_5 == NULL));
        UPDATE_STRING_DICT0(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[6], tmp_assign_source_5);
    }
    {
        PyObject *tmp_assign_source_6;
        PyObject *tmp_import_name_from_1;
        tmp_import_name_from_1 = IMPORT_HARD_TYPING();
        assert(!(tmp_import_name_from_1 == NULL));
        if (PyModule_Check(tmp_import_name_from_1)) {
            tmp_assign_source_6 = IMPORT_NAME_OR_MODULE(
                tstate,
                tmp_import_name_from_1,
                (PyObject *)moduledict_Quartz$ImageKit$_metadata,
                mod_consts[7],
                const_int_0
            );
        } else {
            tmp_assign_source_6 = IMPORT_NAME_FROM_MODULE(tstate, tmp_import_name_from_1, mod_consts[7]);
        }

        if (tmp_assign_source_6 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 8;

            goto frame_exception_exit_1;
        }
        UPDATE_STRING_DICT1(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[7], tmp_assign_source_6);
    }
    {
        PyObject *tmp_assign_source_7;


        tmp_assign_source_7 = MAKE_FUNCTION_Quartz$ImageKit$_metadata$$$function__1_sel32or64(tstate);

        UPDATE_STRING_DICT1(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[8], tmp_assign_source_7);
    }
    {
        nuitka_bool tmp_condition_result_1;
        PyObject *tmp_cmp_expr_left_1;
        PyObject *tmp_cmp_expr_right_1;
        PyObject *tmp_expression_value_1;
        tmp_expression_value_1 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
        assert(!(tmp_expression_value_1 == NULL));
        tmp_cmp_expr_left_1 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_1, mod_consts[9]);
        if (tmp_cmp_expr_left_1 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 21;

            goto frame_exception_exit_1;
        }
        tmp_cmp_expr_right_1 = mod_consts[10];
        tmp_condition_result_1 = RICH_COMPARE_EQ_NBOOL_OBJECT_UNICODE(tmp_cmp_expr_left_1, tmp_cmp_expr_right_1);
        Py_DECREF(tmp_cmp_expr_left_1);
        if (tmp_condition_result_1 == NUITKA_BOOL_EXCEPTION) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 21;

            goto frame_exception_exit_1;
        }
        if (tmp_condition_result_1 == NUITKA_BOOL_TRUE) {
            goto branch_yes_1;
        } else {
            goto branch_no_1;
        }
    }
    branch_yes_1:;
    {
        PyObject *tmp_assign_source_8;


        tmp_assign_source_8 = MAKE_FUNCTION_Quartz$ImageKit$_metadata$$$function__3_selAorI(tstate);

        UPDATE_STRING_DICT1(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[11], tmp_assign_source_8);
    }
    goto branch_end_1;
    branch_no_1:;
    {
        PyObject *tmp_assign_source_9;


        tmp_assign_source_9 = MAKE_FUNCTION_Quartz$ImageKit$_metadata$$$function__4_selAorI(tstate);

        UPDATE_STRING_DICT1(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[11], tmp_assign_source_9);
    }
    branch_end_1:;
    {
        PyObject *tmp_assign_source_10;
        tmp_assign_source_10 = MAKE_DICT_EMPTY(tstate);
        UPDATE_STRING_DICT1(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[12], tmp_assign_source_10);
    }
    {
        PyObject *tmp_assign_source_11;
        tmp_assign_source_11 = mod_consts[13];
        UPDATE_STRING_DICT0(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[14], tmp_assign_source_11);
    }
    {
        PyObject *tmp_assign_source_12;
        tmp_assign_source_12 = mod_consts[15];
        UPDATE_STRING_DICT0(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[16], tmp_assign_source_12);
    }
    {
        PyObject *tmp_dict_arg_value_1;
        PyObject *tmp_iterable_value_1;
        PyObject *tmp_dict_key_1;
        PyObject *tmp_dict_value_1;
        PyObject *tmp_called_value_1;
        tmp_dict_arg_value_1 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$misc(tstate);
        assert(!(tmp_dict_arg_value_1 == NULL));
        tmp_dict_key_1 = mod_consts[17];
        tmp_called_value_1 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$NewType(tstate);
        if (unlikely(tmp_called_value_1 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
        }

        if (tmp_called_value_1 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 37;

            goto frame_exception_exit_1;
        }
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 37;
        tmp_dict_value_1 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_1, mod_consts[18]);

        if (tmp_dict_value_1 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 37;

            goto frame_exception_exit_1;
        }
        tmp_iterable_value_1 = _PyDict_NewPresized( 7 );
        {
            PyObject *tmp_called_value_2;
            PyObject *tmp_called_value_3;
            PyObject *tmp_called_value_4;
            PyObject *tmp_called_value_5;
            PyObject *tmp_called_value_6;
            PyObject *tmp_called_value_7;
            tmp_res = PyDict_SetItem(tmp_iterable_value_1, tmp_dict_key_1, tmp_dict_value_1);
            Py_DECREF(tmp_dict_value_1);
            assert(!(tmp_res != 0));
            tmp_dict_key_1 = mod_consts[19];
            tmp_called_value_2 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_2 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_2 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 38;

                goto dict_build_exception_1;
            }
            frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 38;
            tmp_dict_value_1 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_2, mod_consts[20]);

            if (tmp_dict_value_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 38;

                goto dict_build_exception_1;
            }
            tmp_res = PyDict_SetItem(tmp_iterable_value_1, tmp_dict_key_1, tmp_dict_value_1);
            Py_DECREF(tmp_dict_value_1);
            assert(!(tmp_res != 0));
            tmp_dict_key_1 = mod_consts[21];
            tmp_called_value_3 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_3 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_3 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 41;

                goto dict_build_exception_1;
            }
            frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 41;
            tmp_dict_value_1 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_3, mod_consts[22]);

            if (tmp_dict_value_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 41;

                goto dict_build_exception_1;
            }
            tmp_res = PyDict_SetItem(tmp_iterable_value_1, tmp_dict_key_1, tmp_dict_value_1);
            Py_DECREF(tmp_dict_value_1);
            assert(!(tmp_res != 0));
            tmp_dict_key_1 = mod_consts[23];
            tmp_called_value_4 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_4 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_4 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 42;

                goto dict_build_exception_1;
            }
            frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 42;
            tmp_dict_value_1 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_4, mod_consts[24]);

            if (tmp_dict_value_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 42;

                goto dict_build_exception_1;
            }
            tmp_res = PyDict_SetItem(tmp_iterable_value_1, tmp_dict_key_1, tmp_dict_value_1);
            Py_DECREF(tmp_dict_value_1);
            assert(!(tmp_res != 0));
            tmp_dict_key_1 = mod_consts[25];
            tmp_called_value_5 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_5 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_5 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 45;

                goto dict_build_exception_1;
            }
            frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 45;
            tmp_dict_value_1 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_5, mod_consts[26]);

            if (tmp_dict_value_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 45;

                goto dict_build_exception_1;
            }
            tmp_res = PyDict_SetItem(tmp_iterable_value_1, tmp_dict_key_1, tmp_dict_value_1);
            Py_DECREF(tmp_dict_value_1);
            assert(!(tmp_res != 0));
            tmp_dict_key_1 = mod_consts[27];
            tmp_called_value_6 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_6 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_6 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 48;

                goto dict_build_exception_1;
            }
            frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 48;
            tmp_dict_value_1 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_6, mod_consts[28]);

            if (tmp_dict_value_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 48;

                goto dict_build_exception_1;
            }
            tmp_res = PyDict_SetItem(tmp_iterable_value_1, tmp_dict_key_1, tmp_dict_value_1);
            Py_DECREF(tmp_dict_value_1);
            assert(!(tmp_res != 0));
            tmp_dict_key_1 = mod_consts[29];
            tmp_called_value_7 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$NewType(tstate);
            if (unlikely(tmp_called_value_7 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[7]);
            }

            if (tmp_called_value_7 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 49;

                goto dict_build_exception_1;
            }
            frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 49;
            tmp_dict_value_1 = CALL_FUNCTION_WITH_POS_ARGS2(tstate, tmp_called_value_7, mod_consts[30]);

            if (tmp_dict_value_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 49;

                goto dict_build_exception_1;
            }
            tmp_res = PyDict_SetItem(tmp_iterable_value_1, tmp_dict_key_1, tmp_dict_value_1);
            Py_DECREF(tmp_dict_value_1);
            assert(!(tmp_res != 0));
        }
        goto dict_build_noexception_1;
        // Exception handling pass through code for dict_build:
        dict_build_exception_1:;
        Py_DECREF(tmp_iterable_value_1);
        goto frame_exception_exit_1;
        // Finished with no exception for dict_build:
        dict_build_noexception_1:;
        assert(PyDict_Check(tmp_dict_arg_value_1));
            tmp_res = PyDict_Merge(tmp_dict_arg_value_1, tmp_iterable_value_1, 1);

        Py_DECREF(tmp_iterable_value_1);
        if (tmp_res != 0) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 36;

            goto frame_exception_exit_1;
        }
    }
    {
        PyObject *tmp_called_value_8;
        PyObject *tmp_expression_value_2;
        PyObject *tmp_call_result_1;
        PyObject *tmp_call_arg_element_1;
        tmp_expression_value_2 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$misc(tstate);
        if (unlikely(tmp_expression_value_2 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[12]);
        }

        if (tmp_expression_value_2 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 54;

            goto frame_exception_exit_1;
        }
        tmp_called_value_8 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_2, mod_consts[31]);
        if (tmp_called_value_8 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 54;

            goto frame_exception_exit_1;
        }
        tmp_call_arg_element_1 = MAKE_DICT_EMPTY(tstate);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 54;
        tmp_call_result_1 = CALL_FUNCTION_WITH_SINGLE_ARG(tstate, tmp_called_value_8, tmp_call_arg_element_1);
        Py_DECREF(tmp_called_value_8);
        Py_DECREF(tmp_call_arg_element_1);
        if (tmp_call_result_1 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 54;

            goto frame_exception_exit_1;
        }
        Py_DECREF(tmp_call_result_1);
    }
    {
        PyObject *tmp_called_value_9;
        PyObject *tmp_expression_value_3;
        PyObject *tmp_call_result_2;
        PyObject *tmp_call_arg_element_2;
        tmp_expression_value_3 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$misc(tstate);
        if (unlikely(tmp_expression_value_3 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[12]);
        }

        if (tmp_expression_value_3 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 55;

            goto frame_exception_exit_1;
        }
        tmp_called_value_9 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_3, mod_consts[31]);
        if (tmp_called_value_9 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 55;

            goto frame_exception_exit_1;
        }
        tmp_call_arg_element_2 = MAKE_DICT_EMPTY(tstate);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 55;
        tmp_call_result_2 = CALL_FUNCTION_WITH_SINGLE_ARG(tstate, tmp_called_value_9, tmp_call_arg_element_2);
        Py_DECREF(tmp_called_value_9);
        Py_DECREF(tmp_call_arg_element_2);
        if (tmp_call_result_2 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 55;

            goto frame_exception_exit_1;
        }
        Py_DECREF(tmp_call_result_2);
    }
    {
        PyObject *tmp_assign_source_13;
        tmp_assign_source_13 = DICT_COPY(tstate, mod_consts[32]);
        UPDATE_STRING_DICT1(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[33], tmp_assign_source_13);
    }
    {
        PyObject *tmp_assign_source_14;
        PyObject *tmp_expression_value_4;
        tmp_expression_value_4 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
        if (unlikely(tmp_expression_value_4 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
        }

        if (tmp_expression_value_4 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 69;

            goto frame_exception_exit_1;
        }
        tmp_assign_source_14 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_4, mod_consts[34]);
        if (tmp_assign_source_14 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 69;

            goto frame_exception_exit_1;
        }
        UPDATE_STRING_DICT1(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[35], tmp_assign_source_14);
    }
    {
        PyObject *tmp_called_instance_1;
        PyObject *tmp_call_result_3;
        tmp_called_instance_1 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
        if (unlikely(tmp_called_instance_1 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
        }

        if (tmp_called_instance_1 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 70;

            goto frame_exception_exit_1;
        }
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 70;
        tmp_call_result_3 = CALL_METHOD_WITH_SINGLE_ARG(
            tstate,
            tmp_called_instance_1,
            mod_consts[36],
            PyTuple_GET_ITEM(mod_consts[37], 0)
        );

        if (tmp_call_result_3 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 70;

            goto frame_exception_exit_1;
        }
        Py_DECREF(tmp_call_result_3);
    }
    // Tried code:
    {
        PyObject *tmp_called_value_10;
        PyObject *tmp_call_result_4;
        PyObject *tmp_call_arg_element_3;
        PyObject *tmp_call_arg_element_4;
        PyObject *tmp_call_arg_element_5;
        tmp_called_value_10 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_10 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_10 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 72;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_3 = mod_consts[38];
        tmp_call_arg_element_4 = mod_consts[39];
        tmp_call_arg_element_5 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 72;
        {
            PyObject *call_args[] = {tmp_call_arg_element_3, tmp_call_arg_element_4, tmp_call_arg_element_5};
            tmp_call_result_4 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_10, call_args);
        }

        Py_DECREF(tmp_call_arg_element_5);
        if (tmp_call_result_4 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 72;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_4);
    }
    {
        PyObject *tmp_called_value_11;
        PyObject *tmp_call_result_5;
        PyObject *tmp_call_arg_element_6;
        PyObject *tmp_call_arg_element_7;
        PyObject *tmp_call_arg_element_8;
        tmp_called_value_11 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_11 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_11 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 73;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_6 = mod_consts[38];
        tmp_call_arg_element_7 = mod_consts[41];
        tmp_call_arg_element_8 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 73;
        {
            PyObject *call_args[] = {tmp_call_arg_element_6, tmp_call_arg_element_7, tmp_call_arg_element_8};
            tmp_call_result_5 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_11, call_args);
        }

        Py_DECREF(tmp_call_arg_element_8);
        if (tmp_call_result_5 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 73;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_5);
    }
    {
        PyObject *tmp_called_value_12;
        PyObject *tmp_call_result_6;
        PyObject *tmp_call_arg_element_9;
        PyObject *tmp_call_arg_element_10;
        PyObject *tmp_call_arg_element_11;
        tmp_called_value_12 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_12 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_12 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 74;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_9 = mod_consts[38];
        tmp_call_arg_element_10 = mod_consts[42];
        tmp_call_arg_element_11 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 74;
        {
            PyObject *call_args[] = {tmp_call_arg_element_9, tmp_call_arg_element_10, tmp_call_arg_element_11};
            tmp_call_result_6 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_12, call_args);
        }

        Py_DECREF(tmp_call_arg_element_11);
        if (tmp_call_result_6 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 74;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_6);
    }
    {
        PyObject *tmp_called_value_13;
        PyObject *tmp_call_result_7;
        PyObject *tmp_call_arg_element_12;
        PyObject *tmp_call_arg_element_13;
        PyObject *tmp_call_arg_element_14;
        tmp_called_value_13 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_13 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_13 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 75;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_12 = mod_consts[38];
        tmp_call_arg_element_13 = mod_consts[43];
        tmp_call_arg_element_14 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 75;
        {
            PyObject *call_args[] = {tmp_call_arg_element_12, tmp_call_arg_element_13, tmp_call_arg_element_14};
            tmp_call_result_7 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_13, call_args);
        }

        Py_DECREF(tmp_call_arg_element_14);
        if (tmp_call_result_7 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 75;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_7);
    }
    {
        PyObject *tmp_called_value_14;
        PyObject *tmp_call_result_8;
        PyObject *tmp_call_arg_element_15;
        PyObject *tmp_call_arg_element_16;
        PyObject *tmp_call_arg_element_17;
        tmp_called_value_14 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_14 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_14 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 76;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_15 = mod_consts[38];
        tmp_call_arg_element_16 = mod_consts[44];
        tmp_call_arg_element_17 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 76;
        {
            PyObject *call_args[] = {tmp_call_arg_element_15, tmp_call_arg_element_16, tmp_call_arg_element_17};
            tmp_call_result_8 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_14, call_args);
        }

        Py_DECREF(tmp_call_arg_element_17);
        if (tmp_call_result_8 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 76;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_8);
    }
    {
        PyObject *tmp_called_value_15;
        PyObject *tmp_call_result_9;
        PyObject *tmp_call_arg_element_18;
        PyObject *tmp_call_arg_element_19;
        PyObject *tmp_call_arg_element_20;
        tmp_called_value_15 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_15 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_15 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 81;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_18 = mod_consts[38];
        tmp_call_arg_element_19 = mod_consts[45];
        tmp_call_arg_element_20 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 81;
        {
            PyObject *call_args[] = {tmp_call_arg_element_18, tmp_call_arg_element_19, tmp_call_arg_element_20};
            tmp_call_result_9 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_15, call_args);
        }

        Py_DECREF(tmp_call_arg_element_20);
        if (tmp_call_result_9 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 81;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_9);
    }
    {
        PyObject *tmp_called_value_16;
        PyObject *tmp_call_result_10;
        PyObject *tmp_call_arg_element_21;
        PyObject *tmp_call_arg_element_22;
        PyObject *tmp_call_arg_element_23;
        tmp_called_value_16 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_16 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_16 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 86;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_21 = mod_consts[38];
        tmp_call_arg_element_22 = mod_consts[46];
        tmp_call_arg_element_23 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 86;
        {
            PyObject *call_args[] = {tmp_call_arg_element_21, tmp_call_arg_element_22, tmp_call_arg_element_23};
            tmp_call_result_10 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_16, call_args);
        }

        Py_DECREF(tmp_call_arg_element_23);
        if (tmp_call_result_10 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 86;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_10);
    }
    {
        PyObject *tmp_called_value_17;
        PyObject *tmp_call_result_11;
        PyObject *tmp_call_arg_element_24;
        PyObject *tmp_call_arg_element_25;
        PyObject *tmp_call_arg_element_26;
        tmp_called_value_17 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_17 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_17 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 87;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_24 = mod_consts[38];
        tmp_call_arg_element_25 = mod_consts[47];
        tmp_call_arg_element_26 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 87;
        {
            PyObject *call_args[] = {tmp_call_arg_element_24, tmp_call_arg_element_25, tmp_call_arg_element_26};
            tmp_call_result_11 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_17, call_args);
        }

        Py_DECREF(tmp_call_arg_element_26);
        if (tmp_call_result_11 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 87;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_11);
    }
    {
        PyObject *tmp_called_value_18;
        PyObject *tmp_call_result_12;
        PyObject *tmp_call_arg_element_27;
        PyObject *tmp_call_arg_element_28;
        PyObject *tmp_call_arg_element_29;
        tmp_called_value_18 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_18 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_18 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 88;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_27 = mod_consts[38];
        tmp_call_arg_element_28 = mod_consts[48];
        tmp_call_arg_element_29 = DEEP_COPY_DICT(tstate, mod_consts[49]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 88;
        {
            PyObject *call_args[] = {tmp_call_arg_element_27, tmp_call_arg_element_28, tmp_call_arg_element_29};
            tmp_call_result_12 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_18, call_args);
        }

        Py_DECREF(tmp_call_arg_element_29);
        if (tmp_call_result_12 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 88;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_12);
    }
    {
        PyObject *tmp_called_value_19;
        PyObject *tmp_call_result_13;
        PyObject *tmp_call_arg_element_30;
        PyObject *tmp_call_arg_element_31;
        PyObject *tmp_call_arg_element_32;
        tmp_called_value_19 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_19 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_19 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 93;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_30 = mod_consts[38];
        tmp_call_arg_element_31 = mod_consts[50];
        tmp_call_arg_element_32 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 93;
        {
            PyObject *call_args[] = {tmp_call_arg_element_30, tmp_call_arg_element_31, tmp_call_arg_element_32};
            tmp_call_result_13 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_19, call_args);
        }

        Py_DECREF(tmp_call_arg_element_32);
        if (tmp_call_result_13 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 93;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_13);
    }
    {
        PyObject *tmp_called_value_20;
        PyObject *tmp_call_result_14;
        PyObject *tmp_call_arg_element_33;
        PyObject *tmp_call_arg_element_34;
        PyObject *tmp_call_arg_element_35;
        tmp_called_value_20 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_20 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_20 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 98;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_33 = mod_consts[38];
        tmp_call_arg_element_34 = mod_consts[52];
        tmp_call_arg_element_35 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 98;
        {
            PyObject *call_args[] = {tmp_call_arg_element_33, tmp_call_arg_element_34, tmp_call_arg_element_35};
            tmp_call_result_14 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_20, call_args);
        }

        Py_DECREF(tmp_call_arg_element_35);
        if (tmp_call_result_14 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 98;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_14);
    }
    {
        PyObject *tmp_called_value_21;
        PyObject *tmp_call_result_15;
        PyObject *tmp_call_arg_element_36;
        PyObject *tmp_call_arg_element_37;
        PyObject *tmp_call_arg_element_38;
        tmp_called_value_21 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_21 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_21 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 103;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_36 = mod_consts[38];
        tmp_call_arg_element_37 = mod_consts[53];
        tmp_call_arg_element_38 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 103;
        {
            PyObject *call_args[] = {tmp_call_arg_element_36, tmp_call_arg_element_37, tmp_call_arg_element_38};
            tmp_call_result_15 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_21, call_args);
        }

        Py_DECREF(tmp_call_arg_element_38);
        if (tmp_call_result_15 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 103;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_15);
    }
    {
        PyObject *tmp_called_value_22;
        PyObject *tmp_call_result_16;
        PyObject *tmp_call_arg_element_39;
        PyObject *tmp_call_arg_element_40;
        PyObject *tmp_call_arg_element_41;
        tmp_called_value_22 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_22 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_22 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 108;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_39 = mod_consts[38];
        tmp_call_arg_element_40 = mod_consts[54];
        tmp_call_arg_element_41 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 108;
        {
            PyObject *call_args[] = {tmp_call_arg_element_39, tmp_call_arg_element_40, tmp_call_arg_element_41};
            tmp_call_result_16 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_22, call_args);
        }

        Py_DECREF(tmp_call_arg_element_41);
        if (tmp_call_result_16 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 108;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_16);
    }
    {
        PyObject *tmp_called_value_23;
        PyObject *tmp_call_result_17;
        PyObject *tmp_call_arg_element_42;
        PyObject *tmp_call_arg_element_43;
        PyObject *tmp_call_arg_element_44;
        tmp_called_value_23 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_23 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_23 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 113;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_42 = mod_consts[38];
        tmp_call_arg_element_43 = mod_consts[55];
        tmp_call_arg_element_44 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 113;
        {
            PyObject *call_args[] = {tmp_call_arg_element_42, tmp_call_arg_element_43, tmp_call_arg_element_44};
            tmp_call_result_17 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_23, call_args);
        }

        Py_DECREF(tmp_call_arg_element_44);
        if (tmp_call_result_17 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 113;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_17);
    }
    {
        PyObject *tmp_called_value_24;
        PyObject *tmp_call_result_18;
        PyObject *tmp_call_arg_element_45;
        PyObject *tmp_call_arg_element_46;
        PyObject *tmp_call_arg_element_47;
        tmp_called_value_24 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_24 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_24 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 118;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_45 = mod_consts[56];
        tmp_call_arg_element_46 = mod_consts[57];
        tmp_call_arg_element_47 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 118;
        {
            PyObject *call_args[] = {tmp_call_arg_element_45, tmp_call_arg_element_46, tmp_call_arg_element_47};
            tmp_call_result_18 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_24, call_args);
        }

        Py_DECREF(tmp_call_arg_element_47);
        if (tmp_call_result_18 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 118;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_18);
    }
    {
        PyObject *tmp_called_value_25;
        PyObject *tmp_call_result_19;
        PyObject *tmp_call_arg_element_48;
        PyObject *tmp_call_arg_element_49;
        PyObject *tmp_call_arg_element_50;
        tmp_called_value_25 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_25 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_25 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 119;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_48 = mod_consts[56];
        tmp_call_arg_element_49 = mod_consts[58];
        tmp_call_arg_element_50 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 119;
        {
            PyObject *call_args[] = {tmp_call_arg_element_48, tmp_call_arg_element_49, tmp_call_arg_element_50};
            tmp_call_result_19 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_25, call_args);
        }

        Py_DECREF(tmp_call_arg_element_50);
        if (tmp_call_result_19 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 119;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_19);
    }
    {
        PyObject *tmp_called_value_26;
        PyObject *tmp_call_result_20;
        PyObject *tmp_call_arg_element_51;
        PyObject *tmp_call_arg_element_52;
        PyObject *tmp_call_arg_element_53;
        tmp_called_value_26 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_26 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_26 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 120;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_51 = mod_consts[56];
        tmp_call_arg_element_52 = mod_consts[59];
        tmp_call_arg_element_53 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 120;
        {
            PyObject *call_args[] = {tmp_call_arg_element_51, tmp_call_arg_element_52, tmp_call_arg_element_53};
            tmp_call_result_20 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_26, call_args);
        }

        Py_DECREF(tmp_call_arg_element_53);
        if (tmp_call_result_20 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 120;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_20);
    }
    {
        PyObject *tmp_called_value_27;
        PyObject *tmp_call_result_21;
        PyObject *tmp_call_arg_element_54;
        PyObject *tmp_call_arg_element_55;
        PyObject *tmp_call_arg_element_56;
        tmp_called_value_27 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_27 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_27 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 121;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_54 = mod_consts[56];
        tmp_call_arg_element_55 = mod_consts[60];
        tmp_call_arg_element_56 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 121;
        {
            PyObject *call_args[] = {tmp_call_arg_element_54, tmp_call_arg_element_55, tmp_call_arg_element_56};
            tmp_call_result_21 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_27, call_args);
        }

        Py_DECREF(tmp_call_arg_element_56);
        if (tmp_call_result_21 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 121;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_21);
    }
    {
        PyObject *tmp_called_value_28;
        PyObject *tmp_call_result_22;
        PyObject *tmp_call_arg_element_57;
        PyObject *tmp_call_arg_element_58;
        PyObject *tmp_call_arg_element_59;
        tmp_called_value_28 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_28 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_28 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 122;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_57 = mod_consts[56];
        tmp_call_arg_element_58 = mod_consts[61];
        tmp_call_arg_element_59 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 122;
        {
            PyObject *call_args[] = {tmp_call_arg_element_57, tmp_call_arg_element_58, tmp_call_arg_element_59};
            tmp_call_result_22 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_28, call_args);
        }

        Py_DECREF(tmp_call_arg_element_59);
        if (tmp_call_result_22 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 122;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_22);
    }
    {
        PyObject *tmp_called_value_29;
        PyObject *tmp_call_result_23;
        PyObject *tmp_call_arg_element_60;
        PyObject *tmp_call_arg_element_61;
        PyObject *tmp_call_arg_element_62;
        tmp_called_value_29 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_29 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_29 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 127;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_60 = mod_consts[56];
        tmp_call_arg_element_61 = mod_consts[62];
        tmp_call_arg_element_62 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 127;
        {
            PyObject *call_args[] = {tmp_call_arg_element_60, tmp_call_arg_element_61, tmp_call_arg_element_62};
            tmp_call_result_23 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_29, call_args);
        }

        Py_DECREF(tmp_call_arg_element_62);
        if (tmp_call_result_23 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 127;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_23);
    }
    {
        PyObject *tmp_called_value_30;
        PyObject *tmp_call_result_24;
        PyObject *tmp_call_arg_element_63;
        PyObject *tmp_call_arg_element_64;
        PyObject *tmp_call_arg_element_65;
        tmp_called_value_30 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_30 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_30 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 132;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_63 = mod_consts[56];
        tmp_call_arg_element_64 = mod_consts[63];
        tmp_call_arg_element_65 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 132;
        {
            PyObject *call_args[] = {tmp_call_arg_element_63, tmp_call_arg_element_64, tmp_call_arg_element_65};
            tmp_call_result_24 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_30, call_args);
        }

        Py_DECREF(tmp_call_arg_element_65);
        if (tmp_call_result_24 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 132;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_24);
    }
    {
        PyObject *tmp_called_value_31;
        PyObject *tmp_call_result_25;
        PyObject *tmp_call_arg_element_66;
        PyObject *tmp_call_arg_element_67;
        PyObject *tmp_call_arg_element_68;
        tmp_called_value_31 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_31 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_31 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 137;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_66 = mod_consts[56];
        tmp_call_arg_element_67 = mod_consts[64];
        tmp_call_arg_element_68 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 137;
        {
            PyObject *call_args[] = {tmp_call_arg_element_66, tmp_call_arg_element_67, tmp_call_arg_element_68};
            tmp_call_result_25 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_31, call_args);
        }

        Py_DECREF(tmp_call_arg_element_68);
        if (tmp_call_result_25 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 137;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_25);
    }
    {
        PyObject *tmp_called_value_32;
        PyObject *tmp_call_result_26;
        PyObject *tmp_call_arg_element_69;
        PyObject *tmp_call_arg_element_70;
        PyObject *tmp_call_arg_element_71;
        tmp_called_value_32 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_32 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_32 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 142;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_69 = mod_consts[65];
        tmp_call_arg_element_70 = mod_consts[66];
        tmp_call_arg_element_71 = DEEP_COPY_DICT(tstate, mod_consts[67]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 142;
        {
            PyObject *call_args[] = {tmp_call_arg_element_69, tmp_call_arg_element_70, tmp_call_arg_element_71};
            tmp_call_result_26 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_32, call_args);
        }

        Py_DECREF(tmp_call_arg_element_71);
        if (tmp_call_result_26 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 142;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_26);
    }
    {
        PyObject *tmp_called_value_33;
        PyObject *tmp_call_result_27;
        PyObject *tmp_call_arg_element_72;
        PyObject *tmp_call_arg_element_73;
        PyObject *tmp_call_arg_element_74;
        tmp_called_value_33 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_33 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_33 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 147;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_72 = mod_consts[65];
        tmp_call_arg_element_73 = mod_consts[68];
        tmp_call_arg_element_74 = DEEP_COPY_DICT(tstate, mod_consts[69]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 147;
        {
            PyObject *call_args[] = {tmp_call_arg_element_72, tmp_call_arg_element_73, tmp_call_arg_element_74};
            tmp_call_result_27 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_33, call_args);
        }

        Py_DECREF(tmp_call_arg_element_74);
        if (tmp_call_result_27 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 147;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_27);
    }
    {
        PyObject *tmp_called_value_34;
        PyObject *tmp_call_result_28;
        PyObject *tmp_call_arg_element_75;
        PyObject *tmp_call_arg_element_76;
        PyObject *tmp_call_arg_element_77;
        tmp_called_value_34 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_34 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_34 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 152;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_75 = mod_consts[70];
        tmp_call_arg_element_76 = mod_consts[71];
        tmp_call_arg_element_77 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 152;
        {
            PyObject *call_args[] = {tmp_call_arg_element_75, tmp_call_arg_element_76, tmp_call_arg_element_77};
            tmp_call_result_28 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_34, call_args);
        }

        Py_DECREF(tmp_call_arg_element_77);
        if (tmp_call_result_28 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 152;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_28);
    }
    {
        PyObject *tmp_called_value_35;
        PyObject *tmp_call_result_29;
        PyObject *tmp_call_arg_element_78;
        PyObject *tmp_call_arg_element_79;
        PyObject *tmp_call_arg_element_80;
        tmp_called_value_35 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_35 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_35 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 153;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_78 = mod_consts[72];
        tmp_call_arg_element_79 = mod_consts[73];
        tmp_call_arg_element_80 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 153;
        {
            PyObject *call_args[] = {tmp_call_arg_element_78, tmp_call_arg_element_79, tmp_call_arg_element_80};
            tmp_call_result_29 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_35, call_args);
        }

        Py_DECREF(tmp_call_arg_element_80);
        if (tmp_call_result_29 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 153;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_29);
    }
    {
        PyObject *tmp_called_value_36;
        PyObject *tmp_call_result_30;
        PyObject *tmp_call_arg_element_81;
        PyObject *tmp_call_arg_element_82;
        PyObject *tmp_call_arg_element_83;
        tmp_called_value_36 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_36 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_36 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 154;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_81 = mod_consts[74];
        tmp_call_arg_element_82 = mod_consts[75];
        tmp_call_arg_element_83 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 154;
        {
            PyObject *call_args[] = {tmp_call_arg_element_81, tmp_call_arg_element_82, tmp_call_arg_element_83};
            tmp_call_result_30 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_36, call_args);
        }

        Py_DECREF(tmp_call_arg_element_83);
        if (tmp_call_result_30 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 154;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_30);
    }
    {
        PyObject *tmp_called_value_37;
        PyObject *tmp_call_result_31;
        PyObject *tmp_call_arg_element_84;
        PyObject *tmp_call_arg_element_85;
        PyObject *tmp_call_arg_element_86;
        tmp_called_value_37 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_37 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_37 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 155;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_84 = mod_consts[74];
        tmp_call_arg_element_85 = mod_consts[76];
        tmp_call_arg_element_86 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 155;
        {
            PyObject *call_args[] = {tmp_call_arg_element_84, tmp_call_arg_element_85, tmp_call_arg_element_86};
            tmp_call_result_31 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_37, call_args);
        }

        Py_DECREF(tmp_call_arg_element_86);
        if (tmp_call_result_31 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 155;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_31);
    }
    {
        PyObject *tmp_called_value_38;
        PyObject *tmp_call_result_32;
        PyObject *tmp_call_arg_element_87;
        PyObject *tmp_call_arg_element_88;
        PyObject *tmp_call_arg_element_89;
        tmp_called_value_38 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_38 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_38 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 156;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_87 = mod_consts[74];
        tmp_call_arg_element_88 = mod_consts[77];
        tmp_call_arg_element_89 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 156;
        {
            PyObject *call_args[] = {tmp_call_arg_element_87, tmp_call_arg_element_88, tmp_call_arg_element_89};
            tmp_call_result_32 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_38, call_args);
        }

        Py_DECREF(tmp_call_arg_element_89);
        if (tmp_call_result_32 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 156;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_32);
    }
    {
        PyObject *tmp_called_value_39;
        PyObject *tmp_call_result_33;
        PyObject *tmp_call_arg_element_90;
        PyObject *tmp_call_arg_element_91;
        PyObject *tmp_call_arg_element_92;
        tmp_called_value_39 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_39 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_39 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 157;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_90 = mod_consts[74];
        tmp_call_arg_element_91 = mod_consts[78];
        tmp_call_arg_element_92 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 157;
        {
            PyObject *call_args[] = {tmp_call_arg_element_90, tmp_call_arg_element_91, tmp_call_arg_element_92};
            tmp_call_result_33 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_39, call_args);
        }

        Py_DECREF(tmp_call_arg_element_92);
        if (tmp_call_result_33 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 157;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_33);
    }
    {
        PyObject *tmp_called_value_40;
        PyObject *tmp_call_result_34;
        PyObject *tmp_call_arg_element_93;
        PyObject *tmp_call_arg_element_94;
        PyObject *tmp_call_arg_element_95;
        tmp_called_value_40 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_40 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_40 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 158;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_93 = mod_consts[74];
        tmp_call_arg_element_94 = mod_consts[79];
        tmp_call_arg_element_95 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 158;
        {
            PyObject *call_args[] = {tmp_call_arg_element_93, tmp_call_arg_element_94, tmp_call_arg_element_95};
            tmp_call_result_34 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_40, call_args);
        }

        Py_DECREF(tmp_call_arg_element_95);
        if (tmp_call_result_34 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 158;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_34);
    }
    {
        PyObject *tmp_called_value_41;
        PyObject *tmp_call_result_35;
        PyObject *tmp_call_arg_element_96;
        PyObject *tmp_call_arg_element_97;
        PyObject *tmp_call_arg_element_98;
        tmp_called_value_41 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_41 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_41 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 159;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_96 = mod_consts[74];
        tmp_call_arg_element_97 = mod_consts[80];
        tmp_call_arg_element_98 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 159;
        {
            PyObject *call_args[] = {tmp_call_arg_element_96, tmp_call_arg_element_97, tmp_call_arg_element_98};
            tmp_call_result_35 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_41, call_args);
        }

        Py_DECREF(tmp_call_arg_element_98);
        if (tmp_call_result_35 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 159;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_35);
    }
    {
        PyObject *tmp_called_value_42;
        PyObject *tmp_call_result_36;
        PyObject *tmp_call_arg_element_99;
        PyObject *tmp_call_arg_element_100;
        PyObject *tmp_call_arg_element_101;
        tmp_called_value_42 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_42 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_42 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 160;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_99 = mod_consts[74];
        tmp_call_arg_element_100 = mod_consts[81];
        tmp_call_arg_element_101 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 160;
        {
            PyObject *call_args[] = {tmp_call_arg_element_99, tmp_call_arg_element_100, tmp_call_arg_element_101};
            tmp_call_result_36 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_42, call_args);
        }

        Py_DECREF(tmp_call_arg_element_101);
        if (tmp_call_result_36 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 160;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_36);
    }
    {
        PyObject *tmp_called_value_43;
        PyObject *tmp_call_result_37;
        PyObject *tmp_call_arg_element_102;
        PyObject *tmp_call_arg_element_103;
        PyObject *tmp_call_arg_element_104;
        tmp_called_value_43 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_43 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_43 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 161;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_102 = mod_consts[74];
        tmp_call_arg_element_103 = mod_consts[82];
        tmp_call_arg_element_104 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 161;
        {
            PyObject *call_args[] = {tmp_call_arg_element_102, tmp_call_arg_element_103, tmp_call_arg_element_104};
            tmp_call_result_37 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_43, call_args);
        }

        Py_DECREF(tmp_call_arg_element_104);
        if (tmp_call_result_37 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 161;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_37);
    }
    {
        PyObject *tmp_called_value_44;
        PyObject *tmp_call_result_38;
        PyObject *tmp_call_arg_element_105;
        PyObject *tmp_call_arg_element_106;
        PyObject *tmp_call_arg_element_107;
        tmp_called_value_44 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_44 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_44 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 162;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_105 = mod_consts[74];
        tmp_call_arg_element_106 = mod_consts[83];
        tmp_call_arg_element_107 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 162;
        {
            PyObject *call_args[] = {tmp_call_arg_element_105, tmp_call_arg_element_106, tmp_call_arg_element_107};
            tmp_call_result_38 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_44, call_args);
        }

        Py_DECREF(tmp_call_arg_element_107);
        if (tmp_call_result_38 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 162;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_38);
    }
    {
        PyObject *tmp_called_value_45;
        PyObject *tmp_call_result_39;
        PyObject *tmp_call_arg_element_108;
        PyObject *tmp_call_arg_element_109;
        PyObject *tmp_call_arg_element_110;
        tmp_called_value_45 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_45 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_45 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 167;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_108 = mod_consts[74];
        tmp_call_arg_element_109 = mod_consts[84];
        tmp_call_arg_element_110 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 167;
        {
            PyObject *call_args[] = {tmp_call_arg_element_108, tmp_call_arg_element_109, tmp_call_arg_element_110};
            tmp_call_result_39 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_45, call_args);
        }

        Py_DECREF(tmp_call_arg_element_110);
        if (tmp_call_result_39 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 167;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_39);
    }
    {
        PyObject *tmp_called_value_46;
        PyObject *tmp_call_result_40;
        PyObject *tmp_call_arg_element_111;
        PyObject *tmp_call_arg_element_112;
        PyObject *tmp_call_arg_element_113;
        tmp_called_value_46 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_46 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_46 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 172;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_111 = mod_consts[74];
        tmp_call_arg_element_112 = mod_consts[85];
        tmp_call_arg_element_113 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 172;
        {
            PyObject *call_args[] = {tmp_call_arg_element_111, tmp_call_arg_element_112, tmp_call_arg_element_113};
            tmp_call_result_40 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_46, call_args);
        }

        Py_DECREF(tmp_call_arg_element_113);
        if (tmp_call_result_40 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 172;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_40);
    }
    {
        PyObject *tmp_called_value_47;
        PyObject *tmp_call_result_41;
        PyObject *tmp_call_arg_element_114;
        PyObject *tmp_call_arg_element_115;
        PyObject *tmp_call_arg_element_116;
        tmp_called_value_47 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_47 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_47 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 177;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_114 = mod_consts[74];
        tmp_call_arg_element_115 = mod_consts[86];
        tmp_call_arg_element_116 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 177;
        {
            PyObject *call_args[] = {tmp_call_arg_element_114, tmp_call_arg_element_115, tmp_call_arg_element_116};
            tmp_call_result_41 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_47, call_args);
        }

        Py_DECREF(tmp_call_arg_element_116);
        if (tmp_call_result_41 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 177;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_41);
    }
    {
        PyObject *tmp_called_value_48;
        PyObject *tmp_call_result_42;
        PyObject *tmp_call_arg_element_117;
        PyObject *tmp_call_arg_element_118;
        PyObject *tmp_call_arg_element_119;
        tmp_called_value_48 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_48 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_48 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 182;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_117 = mod_consts[74];
        tmp_call_arg_element_118 = mod_consts[87];
        tmp_call_arg_element_119 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 182;
        {
            PyObject *call_args[] = {tmp_call_arg_element_117, tmp_call_arg_element_118, tmp_call_arg_element_119};
            tmp_call_result_42 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_48, call_args);
        }

        Py_DECREF(tmp_call_arg_element_119);
        if (tmp_call_result_42 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 182;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_42);
    }
    {
        PyObject *tmp_called_value_49;
        PyObject *tmp_call_result_43;
        PyObject *tmp_call_arg_element_120;
        PyObject *tmp_call_arg_element_121;
        PyObject *tmp_call_arg_element_122;
        tmp_called_value_49 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_49 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_49 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 183;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_120 = mod_consts[74];
        tmp_call_arg_element_121 = mod_consts[88];
        tmp_call_arg_element_122 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 183;
        {
            PyObject *call_args[] = {tmp_call_arg_element_120, tmp_call_arg_element_121, tmp_call_arg_element_122};
            tmp_call_result_43 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_49, call_args);
        }

        Py_DECREF(tmp_call_arg_element_122);
        if (tmp_call_result_43 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 183;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_43);
    }
    {
        PyObject *tmp_called_value_50;
        PyObject *tmp_call_result_44;
        PyObject *tmp_call_arg_element_123;
        PyObject *tmp_call_arg_element_124;
        PyObject *tmp_call_arg_element_125;
        tmp_called_value_50 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_50 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_50 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 188;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_123 = mod_consts[74];
        tmp_call_arg_element_124 = mod_consts[89];
        tmp_call_arg_element_125 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 188;
        {
            PyObject *call_args[] = {tmp_call_arg_element_123, tmp_call_arg_element_124, tmp_call_arg_element_125};
            tmp_call_result_44 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_50, call_args);
        }

        Py_DECREF(tmp_call_arg_element_125);
        if (tmp_call_result_44 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 188;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_44);
    }
    {
        PyObject *tmp_called_value_51;
        PyObject *tmp_call_result_45;
        PyObject *tmp_call_arg_element_126;
        PyObject *tmp_call_arg_element_127;
        PyObject *tmp_call_arg_element_128;
        tmp_called_value_51 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_51 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_51 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 193;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_126 = mod_consts[74];
        tmp_call_arg_element_127 = mod_consts[90];
        tmp_call_arg_element_128 = DEEP_COPY_DICT(tstate, mod_consts[49]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 193;
        {
            PyObject *call_args[] = {tmp_call_arg_element_126, tmp_call_arg_element_127, tmp_call_arg_element_128};
            tmp_call_result_45 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_51, call_args);
        }

        Py_DECREF(tmp_call_arg_element_128);
        if (tmp_call_result_45 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 193;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_45);
    }
    {
        PyObject *tmp_called_value_52;
        PyObject *tmp_call_result_46;
        PyObject *tmp_call_arg_element_129;
        PyObject *tmp_call_arg_element_130;
        PyObject *tmp_call_arg_element_131;
        tmp_called_value_52 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_52 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_52 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 198;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_129 = mod_consts[91];
        tmp_call_arg_element_130 = mod_consts[92];
        tmp_call_arg_element_131 = DEEP_COPY_DICT(tstate, mod_consts[93]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 198;
        {
            PyObject *call_args[] = {tmp_call_arg_element_129, tmp_call_arg_element_130, tmp_call_arg_element_131};
            tmp_call_result_46 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_52, call_args);
        }

        Py_DECREF(tmp_call_arg_element_131);
        if (tmp_call_result_46 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 198;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_46);
    }
    {
        PyObject *tmp_called_value_53;
        PyObject *tmp_call_result_47;
        PyObject *tmp_call_arg_element_132;
        PyObject *tmp_call_arg_element_133;
        PyObject *tmp_call_arg_element_134;
        tmp_called_value_53 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_53 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_53 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 203;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_132 = mod_consts[91];
        tmp_call_arg_element_133 = mod_consts[94];
        tmp_call_arg_element_134 = DEEP_COPY_DICT(tstate, mod_consts[95]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 203;
        {
            PyObject *call_args[] = {tmp_call_arg_element_132, tmp_call_arg_element_133, tmp_call_arg_element_134};
            tmp_call_result_47 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_53, call_args);
        }

        Py_DECREF(tmp_call_arg_element_134);
        if (tmp_call_result_47 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 203;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_47);
    }
    {
        PyObject *tmp_called_value_54;
        PyObject *tmp_call_result_48;
        PyObject *tmp_call_arg_element_135;
        PyObject *tmp_call_arg_element_136;
        PyObject *tmp_call_arg_element_137;
        tmp_called_value_54 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_54 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_54 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 208;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_135 = mod_consts[96];
        tmp_call_arg_element_136 = mod_consts[97];
        tmp_call_arg_element_137 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 208;
        {
            PyObject *call_args[] = {tmp_call_arg_element_135, tmp_call_arg_element_136, tmp_call_arg_element_137};
            tmp_call_result_48 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_54, call_args);
        }

        Py_DECREF(tmp_call_arg_element_137);
        if (tmp_call_result_48 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 208;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_48);
    }
    {
        PyObject *tmp_called_value_55;
        PyObject *tmp_call_result_49;
        PyObject *tmp_call_arg_element_138;
        PyObject *tmp_call_arg_element_139;
        PyObject *tmp_call_arg_element_140;
        tmp_called_value_55 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_55 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_55 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 209;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_138 = mod_consts[96];
        tmp_call_arg_element_139 = mod_consts[98];
        tmp_call_arg_element_140 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 209;
        {
            PyObject *call_args[] = {tmp_call_arg_element_138, tmp_call_arg_element_139, tmp_call_arg_element_140};
            tmp_call_result_49 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_55, call_args);
        }

        Py_DECREF(tmp_call_arg_element_140);
        if (tmp_call_result_49 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 209;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_49);
    }
    {
        PyObject *tmp_called_value_56;
        PyObject *tmp_call_result_50;
        PyObject *tmp_call_arg_element_141;
        PyObject *tmp_call_arg_element_142;
        PyObject *tmp_call_arg_element_143;
        tmp_called_value_56 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_56 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_56 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 210;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_141 = mod_consts[96];
        tmp_call_arg_element_142 = mod_consts[99];
        tmp_call_arg_element_143 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 210;
        {
            PyObject *call_args[] = {tmp_call_arg_element_141, tmp_call_arg_element_142, tmp_call_arg_element_143};
            tmp_call_result_50 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_56, call_args);
        }

        Py_DECREF(tmp_call_arg_element_143);
        if (tmp_call_result_50 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 210;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_50);
    }
    {
        PyObject *tmp_called_value_57;
        PyObject *tmp_call_result_51;
        PyObject *tmp_call_arg_element_144;
        PyObject *tmp_call_arg_element_145;
        PyObject *tmp_call_arg_element_146;
        tmp_called_value_57 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_57 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_57 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 211;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_144 = mod_consts[96];
        tmp_call_arg_element_145 = mod_consts[100];
        tmp_call_arg_element_146 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 211;
        {
            PyObject *call_args[] = {tmp_call_arg_element_144, tmp_call_arg_element_145, tmp_call_arg_element_146};
            tmp_call_result_51 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_57, call_args);
        }

        Py_DECREF(tmp_call_arg_element_146);
        if (tmp_call_result_51 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 211;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_51);
    }
    {
        PyObject *tmp_called_value_58;
        PyObject *tmp_call_result_52;
        PyObject *tmp_call_arg_element_147;
        PyObject *tmp_call_arg_element_148;
        PyObject *tmp_call_arg_element_149;
        tmp_called_value_58 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_58 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_58 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 212;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_147 = mod_consts[96];
        tmp_call_arg_element_148 = mod_consts[101];
        tmp_call_arg_element_149 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 212;
        {
            PyObject *call_args[] = {tmp_call_arg_element_147, tmp_call_arg_element_148, tmp_call_arg_element_149};
            tmp_call_result_52 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_58, call_args);
        }

        Py_DECREF(tmp_call_arg_element_149);
        if (tmp_call_result_52 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 212;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_52);
    }
    {
        PyObject *tmp_called_value_59;
        PyObject *tmp_call_result_53;
        PyObject *tmp_call_arg_element_150;
        PyObject *tmp_call_arg_element_151;
        PyObject *tmp_call_arg_element_152;
        tmp_called_value_59 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_59 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_59 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 213;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_150 = mod_consts[96];
        tmp_call_arg_element_151 = mod_consts[102];
        tmp_call_arg_element_152 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 213;
        {
            PyObject *call_args[] = {tmp_call_arg_element_150, tmp_call_arg_element_151, tmp_call_arg_element_152};
            tmp_call_result_53 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_59, call_args);
        }

        Py_DECREF(tmp_call_arg_element_152);
        if (tmp_call_result_53 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 213;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_53);
    }
    {
        PyObject *tmp_called_value_60;
        PyObject *tmp_call_result_54;
        PyObject *tmp_call_arg_element_153;
        PyObject *tmp_call_arg_element_154;
        PyObject *tmp_call_arg_element_155;
        tmp_called_value_60 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_60 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_60 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 214;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_153 = mod_consts[96];
        tmp_call_arg_element_154 = mod_consts[103];
        tmp_call_arg_element_155 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 214;
        {
            PyObject *call_args[] = {tmp_call_arg_element_153, tmp_call_arg_element_154, tmp_call_arg_element_155};
            tmp_call_result_54 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_60, call_args);
        }

        Py_DECREF(tmp_call_arg_element_155);
        if (tmp_call_result_54 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 214;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_54);
    }
    {
        PyObject *tmp_called_value_61;
        PyObject *tmp_call_result_55;
        PyObject *tmp_call_arg_element_156;
        PyObject *tmp_call_arg_element_157;
        PyObject *tmp_call_arg_element_158;
        tmp_called_value_61 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_61 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_61 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 215;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_156 = mod_consts[96];
        tmp_call_arg_element_157 = mod_consts[104];
        tmp_call_arg_element_158 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 215;
        {
            PyObject *call_args[] = {tmp_call_arg_element_156, tmp_call_arg_element_157, tmp_call_arg_element_158};
            tmp_call_result_55 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_61, call_args);
        }

        Py_DECREF(tmp_call_arg_element_158);
        if (tmp_call_result_55 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 215;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_55);
    }
    {
        PyObject *tmp_called_value_62;
        PyObject *tmp_call_result_56;
        PyObject *tmp_call_arg_element_159;
        PyObject *tmp_call_arg_element_160;
        PyObject *tmp_call_arg_element_161;
        tmp_called_value_62 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_62 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_62 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 216;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_159 = mod_consts[96];
        tmp_call_arg_element_160 = mod_consts[105];
        tmp_call_arg_element_161 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 216;
        {
            PyObject *call_args[] = {tmp_call_arg_element_159, tmp_call_arg_element_160, tmp_call_arg_element_161};
            tmp_call_result_56 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_62, call_args);
        }

        Py_DECREF(tmp_call_arg_element_161);
        if (tmp_call_result_56 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 216;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_56);
    }
    {
        PyObject *tmp_called_value_63;
        PyObject *tmp_call_result_57;
        PyObject *tmp_call_arg_element_162;
        PyObject *tmp_call_arg_element_163;
        PyObject *tmp_call_arg_element_164;
        tmp_called_value_63 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_63 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_63 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 221;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_162 = mod_consts[96];
        tmp_call_arg_element_163 = mod_consts[106];
        tmp_call_arg_element_164 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 221;
        {
            PyObject *call_args[] = {tmp_call_arg_element_162, tmp_call_arg_element_163, tmp_call_arg_element_164};
            tmp_call_result_57 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_63, call_args);
        }

        Py_DECREF(tmp_call_arg_element_164);
        if (tmp_call_result_57 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 221;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_57);
    }
    {
        PyObject *tmp_called_value_64;
        PyObject *tmp_call_result_58;
        PyObject *tmp_call_arg_element_165;
        PyObject *tmp_call_arg_element_166;
        PyObject *tmp_call_arg_element_167;
        tmp_called_value_64 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_64 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_64 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 222;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_165 = mod_consts[96];
        tmp_call_arg_element_166 = mod_consts[107];
        tmp_call_arg_element_167 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 222;
        {
            PyObject *call_args[] = {tmp_call_arg_element_165, tmp_call_arg_element_166, tmp_call_arg_element_167};
            tmp_call_result_58 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_64, call_args);
        }

        Py_DECREF(tmp_call_arg_element_167);
        if (tmp_call_result_58 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 222;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_58);
    }
    {
        PyObject *tmp_called_value_65;
        PyObject *tmp_call_result_59;
        PyObject *tmp_call_arg_element_168;
        PyObject *tmp_call_arg_element_169;
        PyObject *tmp_call_arg_element_170;
        tmp_called_value_65 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_65 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_65 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 223;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_168 = mod_consts[96];
        tmp_call_arg_element_169 = mod_consts[108];
        tmp_call_arg_element_170 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 223;
        {
            PyObject *call_args[] = {tmp_call_arg_element_168, tmp_call_arg_element_169, tmp_call_arg_element_170};
            tmp_call_result_59 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_65, call_args);
        }

        Py_DECREF(tmp_call_arg_element_170);
        if (tmp_call_result_59 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 223;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_59);
    }
    {
        PyObject *tmp_called_value_66;
        PyObject *tmp_call_result_60;
        PyObject *tmp_call_arg_element_171;
        PyObject *tmp_call_arg_element_172;
        PyObject *tmp_call_arg_element_173;
        tmp_called_value_66 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_66 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_66 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 224;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_171 = mod_consts[96];
        tmp_call_arg_element_172 = mod_consts[109];
        tmp_call_arg_element_173 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 224;
        {
            PyObject *call_args[] = {tmp_call_arg_element_171, tmp_call_arg_element_172, tmp_call_arg_element_173};
            tmp_call_result_60 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_66, call_args);
        }

        Py_DECREF(tmp_call_arg_element_173);
        if (tmp_call_result_60 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 224;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_60);
    }
    {
        PyObject *tmp_called_value_67;
        PyObject *tmp_call_result_61;
        PyObject *tmp_call_arg_element_174;
        PyObject *tmp_call_arg_element_175;
        PyObject *tmp_call_arg_element_176;
        tmp_called_value_67 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_67 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_67 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 225;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_174 = mod_consts[96];
        tmp_call_arg_element_175 = mod_consts[110];
        tmp_call_arg_element_176 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 225;
        {
            PyObject *call_args[] = {tmp_call_arg_element_174, tmp_call_arg_element_175, tmp_call_arg_element_176};
            tmp_call_result_61 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_67, call_args);
        }

        Py_DECREF(tmp_call_arg_element_176);
        if (tmp_call_result_61 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 225;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_61);
    }
    {
        PyObject *tmp_called_value_68;
        PyObject *tmp_call_result_62;
        PyObject *tmp_call_arg_element_177;
        PyObject *tmp_call_arg_element_178;
        PyObject *tmp_call_arg_element_179;
        tmp_called_value_68 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_68 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_68 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 226;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_177 = mod_consts[111];
        tmp_call_arg_element_178 = mod_consts[112];
        tmp_call_arg_element_179 = DEEP_COPY_DICT(tstate, mod_consts[69]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 226;
        {
            PyObject *call_args[] = {tmp_call_arg_element_177, tmp_call_arg_element_178, tmp_call_arg_element_179};
            tmp_call_result_62 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_68, call_args);
        }

        Py_DECREF(tmp_call_arg_element_179);
        if (tmp_call_result_62 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 226;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_62);
    }
    {
        PyObject *tmp_called_value_69;
        PyObject *tmp_call_result_63;
        PyObject *tmp_call_arg_element_180;
        PyObject *tmp_call_arg_element_181;
        PyObject *tmp_call_arg_element_182;
        tmp_called_value_69 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_69 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_69 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 231;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_180 = mod_consts[111];
        tmp_call_arg_element_181 = mod_consts[113];
        tmp_call_arg_element_182 = DEEP_COPY_DICT(tstate, mod_consts[114]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 231;
        {
            PyObject *call_args[] = {tmp_call_arg_element_180, tmp_call_arg_element_181, tmp_call_arg_element_182};
            tmp_call_result_63 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_69, call_args);
        }

        Py_DECREF(tmp_call_arg_element_182);
        if (tmp_call_result_63 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 231;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_63);
    }
    {
        PyObject *tmp_called_value_70;
        PyObject *tmp_call_result_64;
        PyObject *tmp_call_arg_element_183;
        PyObject *tmp_call_arg_element_184;
        PyObject *tmp_call_arg_element_185;
        tmp_called_value_70 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_70 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_70 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 236;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_183 = mod_consts[111];
        tmp_call_arg_element_184 = mod_consts[115];
        tmp_call_arg_element_185 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 236;
        {
            PyObject *call_args[] = {tmp_call_arg_element_183, tmp_call_arg_element_184, tmp_call_arg_element_185};
            tmp_call_result_64 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_70, call_args);
        }

        Py_DECREF(tmp_call_arg_element_185);
        if (tmp_call_result_64 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 236;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_64);
    }
    {
        PyObject *tmp_called_value_71;
        PyObject *tmp_call_result_65;
        PyObject *tmp_call_arg_element_186;
        PyObject *tmp_call_arg_element_187;
        PyObject *tmp_call_arg_element_188;
        tmp_called_value_71 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_71 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_71 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 237;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_186 = mod_consts[111];
        tmp_call_arg_element_187 = mod_consts[116];
        tmp_call_arg_element_188 = DEEP_COPY_DICT(tstate, mod_consts[69]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 237;
        {
            PyObject *call_args[] = {tmp_call_arg_element_186, tmp_call_arg_element_187, tmp_call_arg_element_188};
            tmp_call_result_65 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_71, call_args);
        }

        Py_DECREF(tmp_call_arg_element_188);
        if (tmp_call_result_65 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 237;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_65);
    }
    {
        PyObject *tmp_called_value_72;
        PyObject *tmp_call_result_66;
        PyObject *tmp_call_arg_element_189;
        PyObject *tmp_call_arg_element_190;
        PyObject *tmp_call_arg_element_191;
        tmp_called_value_72 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_72 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_72 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 242;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_189 = mod_consts[111];
        tmp_call_arg_element_190 = mod_consts[117];
        tmp_call_arg_element_191 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 242;
        {
            PyObject *call_args[] = {tmp_call_arg_element_189, tmp_call_arg_element_190, tmp_call_arg_element_191};
            tmp_call_result_66 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_72, call_args);
        }

        Py_DECREF(tmp_call_arg_element_191);
        if (tmp_call_result_66 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 242;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_66);
    }
    {
        PyObject *tmp_called_value_73;
        PyObject *tmp_call_result_67;
        PyObject *tmp_call_arg_element_192;
        PyObject *tmp_call_arg_element_193;
        PyObject *tmp_call_arg_element_194;
        tmp_called_value_73 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_73 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_73 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 243;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_192 = mod_consts[118];
        tmp_call_arg_element_193 = mod_consts[119];
        tmp_call_arg_element_194 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 243;
        {
            PyObject *call_args[] = {tmp_call_arg_element_192, tmp_call_arg_element_193, tmp_call_arg_element_194};
            tmp_call_result_67 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_73, call_args);
        }

        Py_DECREF(tmp_call_arg_element_194);
        if (tmp_call_result_67 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 243;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_67);
    }
    {
        PyObject *tmp_called_value_74;
        PyObject *tmp_call_result_68;
        PyObject *tmp_call_arg_element_195;
        PyObject *tmp_call_arg_element_196;
        PyObject *tmp_call_arg_element_197;
        tmp_called_value_74 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_74 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_74 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 244;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_195 = mod_consts[118];
        tmp_call_arg_element_196 = mod_consts[120];
        tmp_call_arg_element_197 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 244;
        {
            PyObject *call_args[] = {tmp_call_arg_element_195, tmp_call_arg_element_196, tmp_call_arg_element_197};
            tmp_call_result_68 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_74, call_args);
        }

        Py_DECREF(tmp_call_arg_element_197);
        if (tmp_call_result_68 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 244;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_68);
    }
    {
        PyObject *tmp_called_value_75;
        PyObject *tmp_call_result_69;
        PyObject *tmp_call_arg_element_198;
        PyObject *tmp_call_arg_element_199;
        PyObject *tmp_call_arg_element_200;
        tmp_called_value_75 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_75 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_75 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 245;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_198 = mod_consts[121];
        tmp_call_arg_element_199 = mod_consts[44];
        tmp_call_arg_element_200 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 245;
        {
            PyObject *call_args[] = {tmp_call_arg_element_198, tmp_call_arg_element_199, tmp_call_arg_element_200};
            tmp_call_result_69 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_75, call_args);
        }

        Py_DECREF(tmp_call_arg_element_200);
        if (tmp_call_result_69 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 245;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_69);
    }
    {
        PyObject *tmp_called_value_76;
        PyObject *tmp_call_result_70;
        PyObject *tmp_call_arg_element_201;
        PyObject *tmp_call_arg_element_202;
        PyObject *tmp_call_arg_element_203;
        tmp_called_value_76 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_76 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_76 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 250;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_201 = mod_consts[121];
        tmp_call_arg_element_202 = mod_consts[45];
        tmp_call_arg_element_203 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 250;
        {
            PyObject *call_args[] = {tmp_call_arg_element_201, tmp_call_arg_element_202, tmp_call_arg_element_203};
            tmp_call_result_70 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_76, call_args);
        }

        Py_DECREF(tmp_call_arg_element_203);
        if (tmp_call_result_70 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 250;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_70);
    }
    {
        PyObject *tmp_called_value_77;
        PyObject *tmp_call_result_71;
        PyObject *tmp_call_arg_element_204;
        PyObject *tmp_call_arg_element_205;
        PyObject *tmp_call_arg_element_206;
        tmp_called_value_77 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_77 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_77 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 255;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_204 = mod_consts[121];
        tmp_call_arg_element_205 = mod_consts[122];
        tmp_call_arg_element_206 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 255;
        {
            PyObject *call_args[] = {tmp_call_arg_element_204, tmp_call_arg_element_205, tmp_call_arg_element_206};
            tmp_call_result_71 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_77, call_args);
        }

        Py_DECREF(tmp_call_arg_element_206);
        if (tmp_call_result_71 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 255;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_71);
    }
    {
        PyObject *tmp_called_value_78;
        PyObject *tmp_call_result_72;
        PyObject *tmp_call_arg_element_207;
        PyObject *tmp_call_arg_element_208;
        PyObject *tmp_call_arg_element_209;
        tmp_called_value_78 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_78 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_78 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 256;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_207 = mod_consts[121];
        tmp_call_arg_element_208 = mod_consts[123];
        tmp_call_arg_element_209 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 256;
        {
            PyObject *call_args[] = {tmp_call_arg_element_207, tmp_call_arg_element_208, tmp_call_arg_element_209};
            tmp_call_result_72 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_78, call_args);
        }

        Py_DECREF(tmp_call_arg_element_209);
        if (tmp_call_result_72 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 256;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_72);
    }
    {
        PyObject *tmp_called_value_79;
        PyObject *tmp_call_result_73;
        PyObject *tmp_call_arg_element_210;
        PyObject *tmp_call_arg_element_211;
        PyObject *tmp_call_arg_element_212;
        tmp_called_value_79 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_79 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_79 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 257;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_210 = mod_consts[121];
        tmp_call_arg_element_211 = mod_consts[50];
        tmp_call_arg_element_212 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 257;
        {
            PyObject *call_args[] = {tmp_call_arg_element_210, tmp_call_arg_element_211, tmp_call_arg_element_212};
            tmp_call_result_73 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_79, call_args);
        }

        Py_DECREF(tmp_call_arg_element_212);
        if (tmp_call_result_73 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 257;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_73);
    }
    {
        PyObject *tmp_called_value_80;
        PyObject *tmp_call_result_74;
        PyObject *tmp_call_arg_element_213;
        PyObject *tmp_call_arg_element_214;
        PyObject *tmp_call_arg_element_215;
        tmp_called_value_80 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_80 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_80 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 262;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_213 = mod_consts[121];
        tmp_call_arg_element_214 = mod_consts[52];
        tmp_call_arg_element_215 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 262;
        {
            PyObject *call_args[] = {tmp_call_arg_element_213, tmp_call_arg_element_214, tmp_call_arg_element_215};
            tmp_call_result_74 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_80, call_args);
        }

        Py_DECREF(tmp_call_arg_element_215);
        if (tmp_call_result_74 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 262;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_74);
    }
    {
        PyObject *tmp_called_value_81;
        PyObject *tmp_call_result_75;
        PyObject *tmp_call_arg_element_216;
        PyObject *tmp_call_arg_element_217;
        PyObject *tmp_call_arg_element_218;
        tmp_called_value_81 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_81 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_81 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 267;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_216 = mod_consts[121];
        tmp_call_arg_element_217 = mod_consts[124];
        tmp_call_arg_element_218 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 267;
        {
            PyObject *call_args[] = {tmp_call_arg_element_216, tmp_call_arg_element_217, tmp_call_arg_element_218};
            tmp_call_result_75 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_81, call_args);
        }

        Py_DECREF(tmp_call_arg_element_218);
        if (tmp_call_result_75 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 267;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_75);
    }
    {
        PyObject *tmp_called_value_82;
        PyObject *tmp_call_result_76;
        PyObject *tmp_call_arg_element_219;
        PyObject *tmp_call_arg_element_220;
        PyObject *tmp_call_arg_element_221;
        tmp_called_value_82 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_82 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_82 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 272;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_219 = mod_consts[121];
        tmp_call_arg_element_220 = mod_consts[125];
        tmp_call_arg_element_221 = DEEP_COPY_DICT(tstate, mod_consts[51]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 272;
        {
            PyObject *call_args[] = {tmp_call_arg_element_219, tmp_call_arg_element_220, tmp_call_arg_element_221};
            tmp_call_result_76 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_82, call_args);
        }

        Py_DECREF(tmp_call_arg_element_221);
        if (tmp_call_result_76 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 272;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_76);
    }
    {
        PyObject *tmp_called_value_83;
        PyObject *tmp_call_result_77;
        PyObject *tmp_call_arg_element_222;
        PyObject *tmp_call_arg_element_223;
        PyObject *tmp_call_arg_element_224;
        tmp_called_value_83 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_83 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_83 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 277;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_222 = mod_consts[126];
        tmp_call_arg_element_223 = mod_consts[127];
        tmp_call_arg_element_224 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 277;
        {
            PyObject *call_args[] = {tmp_call_arg_element_222, tmp_call_arg_element_223, tmp_call_arg_element_224};
            tmp_call_result_77 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_83, call_args);
        }

        Py_DECREF(tmp_call_arg_element_224);
        if (tmp_call_result_77 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 277;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_77);
    }
    {
        PyObject *tmp_called_value_84;
        PyObject *tmp_call_result_78;
        PyObject *tmp_call_arg_element_225;
        PyObject *tmp_call_arg_element_226;
        PyObject *tmp_call_arg_element_227;
        tmp_called_value_84 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_84 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_84 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 278;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_225 = mod_consts[128];
        tmp_call_arg_element_226 = mod_consts[129];
        tmp_call_arg_element_227 = DEEP_COPY_DICT(tstate, mod_consts[130]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 278;
        {
            PyObject *call_args[] = {tmp_call_arg_element_225, tmp_call_arg_element_226, tmp_call_arg_element_227};
            tmp_call_result_78 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_84, call_args);
        }

        Py_DECREF(tmp_call_arg_element_227);
        if (tmp_call_result_78 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 278;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_78);
    }
    {
        PyObject *tmp_called_value_85;
        PyObject *tmp_call_result_79;
        PyObject *tmp_call_arg_element_228;
        PyObject *tmp_call_arg_element_229;
        PyObject *tmp_call_arg_element_230;
        tmp_called_value_85 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_85 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_85 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 293;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_228 = mod_consts[128];
        tmp_call_arg_element_229 = mod_consts[131];
        tmp_call_arg_element_230 = DEEP_COPY_DICT(tstate, mod_consts[132]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 293;
        {
            PyObject *call_args[] = {tmp_call_arg_element_228, tmp_call_arg_element_229, tmp_call_arg_element_230};
            tmp_call_result_79 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_85, call_args);
        }

        Py_DECREF(tmp_call_arg_element_230);
        if (tmp_call_result_79 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 293;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_79);
    }
    {
        PyObject *tmp_called_value_86;
        PyObject *tmp_call_result_80;
        PyObject *tmp_call_arg_element_231;
        PyObject *tmp_call_arg_element_232;
        PyObject *tmp_call_arg_element_233;
        tmp_called_value_86 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_86 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_86 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 302;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_231 = mod_consts[128];
        tmp_call_arg_element_232 = mod_consts[133];
        tmp_call_arg_element_233 = DEEP_COPY_DICT(tstate, mod_consts[134]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 302;
        {
            PyObject *call_args[] = {tmp_call_arg_element_231, tmp_call_arg_element_232, tmp_call_arg_element_233};
            tmp_call_result_80 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_86, call_args);
        }

        Py_DECREF(tmp_call_arg_element_233);
        if (tmp_call_result_80 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 302;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_80);
    }
    {
        PyObject *tmp_called_value_87;
        PyObject *tmp_call_result_81;
        PyObject *tmp_call_arg_element_234;
        PyObject *tmp_call_arg_element_235;
        PyObject *tmp_call_arg_element_236;
        tmp_called_value_87 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_87 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_87 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 307;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_234 = mod_consts[128];
        tmp_call_arg_element_235 = mod_consts[135];
        tmp_call_arg_element_236 = DEEP_COPY_DICT(tstate, mod_consts[136]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 307;
        {
            PyObject *call_args[] = {tmp_call_arg_element_234, tmp_call_arg_element_235, tmp_call_arg_element_236};
            tmp_call_result_81 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_87, call_args);
        }

        Py_DECREF(tmp_call_arg_element_236);
        if (tmp_call_result_81 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 307;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_81);
    }
    {
        PyObject *tmp_called_value_88;
        PyObject *tmp_call_result_82;
        PyObject *tmp_call_arg_element_237;
        PyObject *tmp_call_arg_element_238;
        PyObject *tmp_call_arg_element_239;
        tmp_called_value_88 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_88 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_88 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 316;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_237 = mod_consts[128];
        tmp_call_arg_element_238 = mod_consts[137];
        tmp_call_arg_element_239 = DEEP_COPY_DICT(tstate, mod_consts[132]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 316;
        {
            PyObject *call_args[] = {tmp_call_arg_element_237, tmp_call_arg_element_238, tmp_call_arg_element_239};
            tmp_call_result_82 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_88, call_args);
        }

        Py_DECREF(tmp_call_arg_element_239);
        if (tmp_call_result_82 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 316;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_82);
    }
    {
        PyObject *tmp_called_value_89;
        PyObject *tmp_call_result_83;
        PyObject *tmp_call_arg_element_240;
        PyObject *tmp_call_arg_element_241;
        PyObject *tmp_call_arg_element_242;
        tmp_called_value_89 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_89 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_89 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 325;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_240 = mod_consts[128];
        tmp_call_arg_element_241 = mod_consts[138];
        tmp_call_arg_element_242 = DEEP_COPY_DICT(tstate, mod_consts[139]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 325;
        {
            PyObject *call_args[] = {tmp_call_arg_element_240, tmp_call_arg_element_241, tmp_call_arg_element_242};
            tmp_call_result_83 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_89, call_args);
        }

        Py_DECREF(tmp_call_arg_element_242);
        if (tmp_call_result_83 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 325;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_83);
    }
    {
        PyObject *tmp_called_value_90;
        PyObject *tmp_call_result_84;
        PyObject *tmp_call_arg_element_243;
        PyObject *tmp_call_arg_element_244;
        PyObject *tmp_call_arg_element_245;
        tmp_called_value_90 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_90 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_90 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 334;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_243 = mod_consts[128];
        tmp_call_arg_element_244 = mod_consts[140];
        tmp_call_arg_element_245 = DEEP_COPY_DICT(tstate, mod_consts[141]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 334;
        {
            PyObject *call_args[] = {tmp_call_arg_element_243, tmp_call_arg_element_244, tmp_call_arg_element_245};
            tmp_call_result_84 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_90, call_args);
        }

        Py_DECREF(tmp_call_arg_element_245);
        if (tmp_call_result_84 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 334;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_84);
    }
    {
        PyObject *tmp_called_value_91;
        PyObject *tmp_call_result_85;
        PyObject *tmp_call_arg_element_246;
        PyObject *tmp_call_arg_element_247;
        PyObject *tmp_call_arg_element_248;
        tmp_called_value_91 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_91 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_91 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 335;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_246 = mod_consts[128];
        tmp_call_arg_element_247 = mod_consts[142];
        tmp_call_arg_element_248 = DEEP_COPY_DICT(tstate, mod_consts[141]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 335;
        {
            PyObject *call_args[] = {tmp_call_arg_element_246, tmp_call_arg_element_247, tmp_call_arg_element_248};
            tmp_call_result_85 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_91, call_args);
        }

        Py_DECREF(tmp_call_arg_element_248);
        if (tmp_call_result_85 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 335;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_85);
    }
    {
        PyObject *tmp_called_value_92;
        PyObject *tmp_call_result_86;
        PyObject *tmp_call_arg_element_249;
        PyObject *tmp_call_arg_element_250;
        PyObject *tmp_call_arg_element_251;
        tmp_called_value_92 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_92 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_92 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 336;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_249 = mod_consts[128];
        tmp_call_arg_element_250 = mod_consts[143];
        tmp_call_arg_element_251 = DEEP_COPY_DICT(tstate, mod_consts[141]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 336;
        {
            PyObject *call_args[] = {tmp_call_arg_element_249, tmp_call_arg_element_250, tmp_call_arg_element_251};
            tmp_call_result_86 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_92, call_args);
        }

        Py_DECREF(tmp_call_arg_element_251);
        if (tmp_call_result_86 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 336;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_86);
    }
    {
        PyObject *tmp_called_value_93;
        PyObject *tmp_call_result_87;
        PyObject *tmp_call_arg_element_252;
        PyObject *tmp_call_arg_element_253;
        PyObject *tmp_call_arg_element_254;
        tmp_called_value_93 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_93 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_93 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 337;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_252 = mod_consts[128];
        tmp_call_arg_element_253 = mod_consts[144];
        tmp_call_arg_element_254 = DEEP_COPY_DICT(tstate, mod_consts[145]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 337;
        {
            PyObject *call_args[] = {tmp_call_arg_element_252, tmp_call_arg_element_253, tmp_call_arg_element_254};
            tmp_call_result_87 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_93, call_args);
        }

        Py_DECREF(tmp_call_arg_element_254);
        if (tmp_call_result_87 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 337;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_87);
    }
    {
        PyObject *tmp_called_value_94;
        PyObject *tmp_call_result_88;
        PyObject *tmp_call_arg_element_255;
        PyObject *tmp_call_arg_element_256;
        PyObject *tmp_call_arg_element_257;
        tmp_called_value_94 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_94 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_94 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 338;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_255 = mod_consts[128];
        tmp_call_arg_element_256 = mod_consts[146];
        tmp_call_arg_element_257 = DEEP_COPY_DICT(tstate, mod_consts[147]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 338;
        {
            PyObject *call_args[] = {tmp_call_arg_element_255, tmp_call_arg_element_256, tmp_call_arg_element_257};
            tmp_call_result_88 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_94, call_args);
        }

        Py_DECREF(tmp_call_arg_element_257);
        if (tmp_call_result_88 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 338;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_88);
    }
    {
        PyObject *tmp_called_value_95;
        PyObject *tmp_call_result_89;
        PyObject *tmp_call_arg_element_258;
        PyObject *tmp_call_arg_element_259;
        PyObject *tmp_call_arg_element_260;
        tmp_called_value_95 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_95 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_95 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 343;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_258 = mod_consts[128];
        tmp_call_arg_element_259 = mod_consts[148];
        tmp_call_arg_element_260 = DEEP_COPY_DICT(tstate, mod_consts[149]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 343;
        {
            PyObject *call_args[] = {tmp_call_arg_element_258, tmp_call_arg_element_259, tmp_call_arg_element_260};
            tmp_call_result_89 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_95, call_args);
        }

        Py_DECREF(tmp_call_arg_element_260);
        if (tmp_call_result_89 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 343;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_89);
    }
    {
        PyObject *tmp_called_value_96;
        PyObject *tmp_call_result_90;
        PyObject *tmp_call_arg_element_261;
        PyObject *tmp_call_arg_element_262;
        PyObject *tmp_call_arg_element_263;
        tmp_called_value_96 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_96 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_96 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 348;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_261 = mod_consts[128];
        tmp_call_arg_element_262 = mod_consts[150];
        tmp_call_arg_element_263 = DEEP_COPY_DICT(tstate, mod_consts[151]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 348;
        {
            PyObject *call_args[] = {tmp_call_arg_element_261, tmp_call_arg_element_262, tmp_call_arg_element_263};
            tmp_call_result_90 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_96, call_args);
        }

        Py_DECREF(tmp_call_arg_element_263);
        if (tmp_call_result_90 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 348;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_90);
    }
    {
        PyObject *tmp_called_value_97;
        PyObject *tmp_call_result_91;
        PyObject *tmp_call_arg_element_264;
        PyObject *tmp_call_arg_element_265;
        PyObject *tmp_call_arg_element_266;
        tmp_called_value_97 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_97 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_97 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 353;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_264 = mod_consts[128];
        tmp_call_arg_element_265 = mod_consts[152];
        tmp_call_arg_element_266 = DEEP_COPY_DICT(tstate, mod_consts[153]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 353;
        {
            PyObject *call_args[] = {tmp_call_arg_element_264, tmp_call_arg_element_265, tmp_call_arg_element_266};
            tmp_call_result_91 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_97, call_args);
        }

        Py_DECREF(tmp_call_arg_element_266);
        if (tmp_call_result_91 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 353;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_91);
    }
    {
        PyObject *tmp_called_value_98;
        PyObject *tmp_call_result_92;
        PyObject *tmp_call_arg_element_267;
        PyObject *tmp_call_arg_element_268;
        PyObject *tmp_call_arg_element_269;
        tmp_called_value_98 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_98 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_98 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 361;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_267 = mod_consts[128];
        tmp_call_arg_element_268 = mod_consts[154];
        tmp_call_arg_element_269 = DEEP_COPY_DICT(tstate, mod_consts[155]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 361;
        {
            PyObject *call_args[] = {tmp_call_arg_element_267, tmp_call_arg_element_268, tmp_call_arg_element_269};
            tmp_call_result_92 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_98, call_args);
        }

        Py_DECREF(tmp_call_arg_element_269);
        if (tmp_call_result_92 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 361;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_92);
    }
    {
        PyObject *tmp_called_value_99;
        PyObject *tmp_call_result_93;
        PyObject *tmp_call_arg_element_270;
        PyObject *tmp_call_arg_element_271;
        PyObject *tmp_call_arg_element_272;
        tmp_called_value_99 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_99 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_99 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 366;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_270 = mod_consts[128];
        tmp_call_arg_element_271 = mod_consts[156];
        tmp_call_arg_element_272 = DEEP_COPY_DICT(tstate, mod_consts[155]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 366;
        {
            PyObject *call_args[] = {tmp_call_arg_element_270, tmp_call_arg_element_271, tmp_call_arg_element_272};
            tmp_call_result_93 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_99, call_args);
        }

        Py_DECREF(tmp_call_arg_element_272);
        if (tmp_call_result_93 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 366;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_93);
    }
    {
        PyObject *tmp_called_value_100;
        PyObject *tmp_call_result_94;
        PyObject *tmp_call_arg_element_273;
        PyObject *tmp_call_arg_element_274;
        PyObject *tmp_call_arg_element_275;
        tmp_called_value_100 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_100 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_100 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 371;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_273 = mod_consts[128];
        tmp_call_arg_element_274 = mod_consts[157];
        tmp_call_arg_element_275 = DEEP_COPY_DICT(tstate, mod_consts[158]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 371;
        {
            PyObject *call_args[] = {tmp_call_arg_element_273, tmp_call_arg_element_274, tmp_call_arg_element_275};
            tmp_call_result_94 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_100, call_args);
        }

        Py_DECREF(tmp_call_arg_element_275);
        if (tmp_call_result_94 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 371;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_94);
    }
    {
        PyObject *tmp_called_value_101;
        PyObject *tmp_call_result_95;
        PyObject *tmp_call_arg_element_276;
        PyObject *tmp_call_arg_element_277;
        PyObject *tmp_call_arg_element_278;
        tmp_called_value_101 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_101 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_101 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 379;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_276 = mod_consts[128];
        tmp_call_arg_element_277 = mod_consts[159];
        tmp_call_arg_element_278 = DEEP_COPY_DICT(tstate, mod_consts[160]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 379;
        {
            PyObject *call_args[] = {tmp_call_arg_element_276, tmp_call_arg_element_277, tmp_call_arg_element_278};
            tmp_call_result_95 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_101, call_args);
        }

        Py_DECREF(tmp_call_arg_element_278);
        if (tmp_call_result_95 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 379;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_95);
    }
    {
        PyObject *tmp_called_value_102;
        PyObject *tmp_call_result_96;
        PyObject *tmp_call_arg_element_279;
        PyObject *tmp_call_arg_element_280;
        PyObject *tmp_call_arg_element_281;
        tmp_called_value_102 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_102 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_102 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 387;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_279 = mod_consts[128];
        tmp_call_arg_element_280 = mod_consts[161];
        tmp_call_arg_element_281 = DEEP_COPY_DICT(tstate, mod_consts[147]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 387;
        {
            PyObject *call_args[] = {tmp_call_arg_element_279, tmp_call_arg_element_280, tmp_call_arg_element_281};
            tmp_call_result_96 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_102, call_args);
        }

        Py_DECREF(tmp_call_arg_element_281);
        if (tmp_call_result_96 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 387;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_96);
    }
    {
        PyObject *tmp_called_value_103;
        PyObject *tmp_call_result_97;
        PyObject *tmp_call_arg_element_282;
        PyObject *tmp_call_arg_element_283;
        PyObject *tmp_call_arg_element_284;
        tmp_called_value_103 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_103 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_103 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 392;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_282 = mod_consts[128];
        tmp_call_arg_element_283 = mod_consts[162];
        tmp_call_arg_element_284 = DEEP_COPY_DICT(tstate, mod_consts[147]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 392;
        {
            PyObject *call_args[] = {tmp_call_arg_element_282, tmp_call_arg_element_283, tmp_call_arg_element_284};
            tmp_call_result_97 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_103, call_args);
        }

        Py_DECREF(tmp_call_arg_element_284);
        if (tmp_call_result_97 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 392;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_97);
    }
    {
        PyObject *tmp_called_value_104;
        PyObject *tmp_call_result_98;
        PyObject *tmp_call_arg_element_285;
        PyObject *tmp_call_arg_element_286;
        PyObject *tmp_call_arg_element_287;
        tmp_called_value_104 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_104 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_104 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 397;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_285 = mod_consts[128];
        tmp_call_arg_element_286 = mod_consts[163];
        tmp_call_arg_element_287 = DEEP_COPY_DICT(tstate, mod_consts[164]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 397;
        {
            PyObject *call_args[] = {tmp_call_arg_element_285, tmp_call_arg_element_286, tmp_call_arg_element_287};
            tmp_call_result_98 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_104, call_args);
        }

        Py_DECREF(tmp_call_arg_element_287);
        if (tmp_call_result_98 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 397;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_98);
    }
    {
        PyObject *tmp_called_value_105;
        PyObject *tmp_call_result_99;
        PyObject *tmp_call_arg_element_288;
        PyObject *tmp_call_arg_element_289;
        PyObject *tmp_call_arg_element_290;
        tmp_called_value_105 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_105 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_105 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 405;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_288 = mod_consts[128];
        tmp_call_arg_element_289 = mod_consts[165];
        tmp_call_arg_element_290 = DEEP_COPY_DICT(tstate, mod_consts[166]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 405;
        {
            PyObject *call_args[] = {tmp_call_arg_element_288, tmp_call_arg_element_289, tmp_call_arg_element_290};
            tmp_call_result_99 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_105, call_args);
        }

        Py_DECREF(tmp_call_arg_element_290);
        if (tmp_call_result_99 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 405;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_99);
    }
    {
        PyObject *tmp_called_value_106;
        PyObject *tmp_call_result_100;
        PyObject *tmp_call_arg_element_291;
        PyObject *tmp_call_arg_element_292;
        PyObject *tmp_call_arg_element_293;
        tmp_called_value_106 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_106 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_106 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 413;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_291 = mod_consts[128];
        tmp_call_arg_element_292 = mod_consts[167];
        tmp_call_arg_element_293 = DEEP_COPY_DICT(tstate, mod_consts[168]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 413;
        {
            PyObject *call_args[] = {tmp_call_arg_element_291, tmp_call_arg_element_292, tmp_call_arg_element_293};
            tmp_call_result_100 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_106, call_args);
        }

        Py_DECREF(tmp_call_arg_element_293);
        if (tmp_call_result_100 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 413;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_100);
    }
    {
        PyObject *tmp_called_value_107;
        PyObject *tmp_call_result_101;
        PyObject *tmp_call_arg_element_294;
        PyObject *tmp_call_arg_element_295;
        PyObject *tmp_call_arg_element_296;
        tmp_called_value_107 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_107 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_107 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 418;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_294 = mod_consts[128];
        tmp_call_arg_element_295 = mod_consts[169];
        tmp_call_arg_element_296 = DEEP_COPY_DICT(tstate, mod_consts[170]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 418;
        {
            PyObject *call_args[] = {tmp_call_arg_element_294, tmp_call_arg_element_295, tmp_call_arg_element_296};
            tmp_call_result_101 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_107, call_args);
        }

        Py_DECREF(tmp_call_arg_element_296);
        if (tmp_call_result_101 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 418;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_101);
    }
    {
        PyObject *tmp_called_value_108;
        PyObject *tmp_call_result_102;
        PyObject *tmp_call_arg_element_297;
        PyObject *tmp_call_arg_element_298;
        PyObject *tmp_call_arg_element_299;
        tmp_called_value_108 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_108 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_108 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 419;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_297 = mod_consts[128];
        tmp_call_arg_element_298 = mod_consts[171];
        tmp_call_arg_element_299 = DEEP_COPY_DICT(tstate, mod_consts[172]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 419;
        {
            PyObject *call_args[] = {tmp_call_arg_element_297, tmp_call_arg_element_298, tmp_call_arg_element_299};
            tmp_call_result_102 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_108, call_args);
        }

        Py_DECREF(tmp_call_arg_element_299);
        if (tmp_call_result_102 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 419;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_102);
    }
    {
        PyObject *tmp_called_value_109;
        PyObject *tmp_call_result_103;
        PyObject *tmp_call_arg_element_300;
        PyObject *tmp_call_arg_element_301;
        PyObject *tmp_call_arg_element_302;
        tmp_called_value_109 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_109 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_109 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 420;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_300 = mod_consts[128];
        tmp_call_arg_element_301 = mod_consts[173];
        tmp_call_arg_element_302 = DEEP_COPY_DICT(tstate, mod_consts[172]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 420;
        {
            PyObject *call_args[] = {tmp_call_arg_element_300, tmp_call_arg_element_301, tmp_call_arg_element_302};
            tmp_call_result_103 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_109, call_args);
        }

        Py_DECREF(tmp_call_arg_element_302);
        if (tmp_call_result_103 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 420;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_103);
    }
    {
        PyObject *tmp_called_value_110;
        PyObject *tmp_call_result_104;
        PyObject *tmp_call_arg_element_303;
        PyObject *tmp_call_arg_element_304;
        PyObject *tmp_call_arg_element_305;
        tmp_called_value_110 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_110 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_110 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 421;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_303 = mod_consts[128];
        tmp_call_arg_element_304 = mod_consts[174];
        tmp_call_arg_element_305 = DEEP_COPY_DICT(tstate, mod_consts[172]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 421;
        {
            PyObject *call_args[] = {tmp_call_arg_element_303, tmp_call_arg_element_304, tmp_call_arg_element_305};
            tmp_call_result_104 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_110, call_args);
        }

        Py_DECREF(tmp_call_arg_element_305);
        if (tmp_call_result_104 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 421;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_104);
    }
    {
        PyObject *tmp_called_value_111;
        PyObject *tmp_call_result_105;
        PyObject *tmp_call_arg_element_306;
        PyObject *tmp_call_arg_element_307;
        PyObject *tmp_call_arg_element_308;
        tmp_called_value_111 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_111 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_111 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 422;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_306 = mod_consts[128];
        tmp_call_arg_element_307 = mod_consts[175];
        tmp_call_arg_element_308 = DEEP_COPY_DICT(tstate, mod_consts[172]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 422;
        {
            PyObject *call_args[] = {tmp_call_arg_element_306, tmp_call_arg_element_307, tmp_call_arg_element_308};
            tmp_call_result_105 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_111, call_args);
        }

        Py_DECREF(tmp_call_arg_element_308);
        if (tmp_call_result_105 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 422;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_105);
    }
    {
        PyObject *tmp_called_value_112;
        PyObject *tmp_call_result_106;
        PyObject *tmp_call_arg_element_309;
        PyObject *tmp_call_arg_element_310;
        PyObject *tmp_call_arg_element_311;
        tmp_called_value_112 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_112 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_112 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 423;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_309 = mod_consts[128];
        tmp_call_arg_element_310 = mod_consts[176];
        tmp_call_arg_element_311 = DEEP_COPY_DICT(tstate, mod_consts[172]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 423;
        {
            PyObject *call_args[] = {tmp_call_arg_element_309, tmp_call_arg_element_310, tmp_call_arg_element_311};
            tmp_call_result_106 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_112, call_args);
        }

        Py_DECREF(tmp_call_arg_element_311);
        if (tmp_call_result_106 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 423;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_106);
    }
    {
        PyObject *tmp_called_value_113;
        PyObject *tmp_call_result_107;
        PyObject *tmp_call_arg_element_312;
        PyObject *tmp_call_arg_element_313;
        PyObject *tmp_call_arg_element_314;
        tmp_called_value_113 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_113 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_113 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 424;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_312 = mod_consts[128];
        tmp_call_arg_element_313 = mod_consts[177];
        tmp_call_arg_element_314 = DEEP_COPY_DICT(tstate, mod_consts[178]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 424;
        {
            PyObject *call_args[] = {tmp_call_arg_element_312, tmp_call_arg_element_313, tmp_call_arg_element_314};
            tmp_call_result_107 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_113, call_args);
        }

        Py_DECREF(tmp_call_arg_element_314);
        if (tmp_call_result_107 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 424;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_107);
    }
    {
        PyObject *tmp_called_value_114;
        PyObject *tmp_call_result_108;
        PyObject *tmp_call_arg_element_315;
        PyObject *tmp_call_arg_element_316;
        PyObject *tmp_call_arg_element_317;
        tmp_called_value_114 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_114 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_114 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 425;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_315 = mod_consts[128];
        tmp_call_arg_element_316 = mod_consts[179];
        tmp_call_arg_element_317 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 425;
        {
            PyObject *call_args[] = {tmp_call_arg_element_315, tmp_call_arg_element_316, tmp_call_arg_element_317};
            tmp_call_result_108 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_114, call_args);
        }

        Py_DECREF(tmp_call_arg_element_317);
        if (tmp_call_result_108 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 425;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_108);
    }
    {
        PyObject *tmp_called_value_115;
        PyObject *tmp_call_result_109;
        PyObject *tmp_call_arg_element_318;
        PyObject *tmp_call_arg_element_319;
        PyObject *tmp_call_arg_element_320;
        tmp_called_value_115 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_115 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_115 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 426;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_318 = mod_consts[128];
        tmp_call_arg_element_319 = mod_consts[180];
        tmp_call_arg_element_320 = DEEP_COPY_DICT(tstate, mod_consts[181]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 426;
        {
            PyObject *call_args[] = {tmp_call_arg_element_318, tmp_call_arg_element_319, tmp_call_arg_element_320};
            tmp_call_result_109 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_115, call_args);
        }

        Py_DECREF(tmp_call_arg_element_320);
        if (tmp_call_result_109 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 426;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_109);
    }
    {
        PyObject *tmp_called_value_116;
        PyObject *tmp_call_result_110;
        PyObject *tmp_call_arg_element_321;
        PyObject *tmp_call_arg_element_322;
        PyObject *tmp_call_arg_element_323;
        tmp_called_value_116 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_116 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_116 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 431;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_321 = mod_consts[128];
        tmp_call_arg_element_322 = mod_consts[182];
        tmp_call_arg_element_323 = DEEP_COPY_DICT(tstate, mod_consts[183]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 431;
        {
            PyObject *call_args[] = {tmp_call_arg_element_321, tmp_call_arg_element_322, tmp_call_arg_element_323};
            tmp_call_result_110 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_116, call_args);
        }

        Py_DECREF(tmp_call_arg_element_323);
        if (tmp_call_result_110 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 431;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_110);
    }
    {
        PyObject *tmp_called_value_117;
        PyObject *tmp_call_result_111;
        PyObject *tmp_call_arg_element_324;
        PyObject *tmp_call_arg_element_325;
        PyObject *tmp_call_arg_element_326;
        tmp_called_value_117 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_117 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_117 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 436;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_324 = mod_consts[128];
        tmp_call_arg_element_325 = mod_consts[184];
        tmp_call_arg_element_326 = DEEP_COPY_DICT(tstate, mod_consts[185]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 436;
        {
            PyObject *call_args[] = {tmp_call_arg_element_324, tmp_call_arg_element_325, tmp_call_arg_element_326};
            tmp_call_result_111 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_117, call_args);
        }

        Py_DECREF(tmp_call_arg_element_326);
        if (tmp_call_result_111 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 436;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_111);
    }
    {
        PyObject *tmp_called_value_118;
        PyObject *tmp_call_result_112;
        PyObject *tmp_call_arg_element_327;
        PyObject *tmp_call_arg_element_328;
        PyObject *tmp_call_arg_element_329;
        tmp_called_value_118 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_118 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_118 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 441;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_327 = mod_consts[128];
        tmp_call_arg_element_328 = mod_consts[186];
        tmp_call_arg_element_329 = DEEP_COPY_DICT(tstate, mod_consts[185]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 441;
        {
            PyObject *call_args[] = {tmp_call_arg_element_327, tmp_call_arg_element_328, tmp_call_arg_element_329};
            tmp_call_result_112 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_118, call_args);
        }

        Py_DECREF(tmp_call_arg_element_329);
        if (tmp_call_result_112 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 441;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_112);
    }
    {
        PyObject *tmp_called_value_119;
        PyObject *tmp_call_result_113;
        PyObject *tmp_call_arg_element_330;
        PyObject *tmp_call_arg_element_331;
        PyObject *tmp_call_arg_element_332;
        tmp_called_value_119 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_119 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_119 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 446;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_330 = mod_consts[128];
        tmp_call_arg_element_331 = mod_consts[187];
        tmp_call_arg_element_332 = DEEP_COPY_DICT(tstate, mod_consts[188]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 446;
        {
            PyObject *call_args[] = {tmp_call_arg_element_330, tmp_call_arg_element_331, tmp_call_arg_element_332};
            tmp_call_result_113 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_119, call_args);
        }

        Py_DECREF(tmp_call_arg_element_332);
        if (tmp_call_result_113 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 446;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_113);
    }
    {
        PyObject *tmp_called_value_120;
        PyObject *tmp_call_result_114;
        PyObject *tmp_call_arg_element_333;
        PyObject *tmp_call_arg_element_334;
        PyObject *tmp_call_arg_element_335;
        tmp_called_value_120 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_120 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_120 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 451;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_333 = mod_consts[128];
        tmp_call_arg_element_334 = mod_consts[189];
        tmp_call_arg_element_335 = DEEP_COPY_DICT(tstate, mod_consts[190]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 451;
        {
            PyObject *call_args[] = {tmp_call_arg_element_333, tmp_call_arg_element_334, tmp_call_arg_element_335};
            tmp_call_result_114 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_120, call_args);
        }

        Py_DECREF(tmp_call_arg_element_335);
        if (tmp_call_result_114 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 451;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_114);
    }
    {
        PyObject *tmp_called_value_121;
        PyObject *tmp_call_result_115;
        PyObject *tmp_call_arg_element_336;
        PyObject *tmp_call_arg_element_337;
        PyObject *tmp_call_arg_element_338;
        tmp_called_value_121 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_121 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_121 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 460;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_336 = mod_consts[128];
        tmp_call_arg_element_337 = mod_consts[191];
        tmp_call_arg_element_338 = DEEP_COPY_DICT(tstate, mod_consts[192]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 460;
        {
            PyObject *call_args[] = {tmp_call_arg_element_336, tmp_call_arg_element_337, tmp_call_arg_element_338};
            tmp_call_result_115 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_121, call_args);
        }

        Py_DECREF(tmp_call_arg_element_338);
        if (tmp_call_result_115 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 460;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_115);
    }
    {
        PyObject *tmp_called_value_122;
        PyObject *tmp_call_result_116;
        PyObject *tmp_call_arg_element_339;
        PyObject *tmp_call_arg_element_340;
        PyObject *tmp_call_arg_element_341;
        tmp_called_value_122 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_122 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_122 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 465;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_339 = mod_consts[128];
        tmp_call_arg_element_340 = mod_consts[193];
        tmp_call_arg_element_341 = DEEP_COPY_DICT(tstate, mod_consts[132]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 465;
        {
            PyObject *call_args[] = {tmp_call_arg_element_339, tmp_call_arg_element_340, tmp_call_arg_element_341};
            tmp_call_result_116 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_122, call_args);
        }

        Py_DECREF(tmp_call_arg_element_341);
        if (tmp_call_result_116 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 465;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_116);
    }
    {
        PyObject *tmp_called_value_123;
        PyObject *tmp_call_result_117;
        PyObject *tmp_call_arg_element_342;
        PyObject *tmp_call_arg_element_343;
        PyObject *tmp_call_arg_element_344;
        tmp_called_value_123 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_123 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_123 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 474;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_342 = mod_consts[128];
        tmp_call_arg_element_343 = mod_consts[194];
        tmp_call_arg_element_344 = DEEP_COPY_DICT(tstate, mod_consts[195]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 474;
        {
            PyObject *call_args[] = {tmp_call_arg_element_342, tmp_call_arg_element_343, tmp_call_arg_element_344};
            tmp_call_result_117 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_123, call_args);
        }

        Py_DECREF(tmp_call_arg_element_344);
        if (tmp_call_result_117 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 474;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_117);
    }
    {
        PyObject *tmp_called_value_124;
        PyObject *tmp_call_result_118;
        PyObject *tmp_call_arg_element_345;
        PyObject *tmp_call_arg_element_346;
        PyObject *tmp_call_arg_element_347;
        tmp_called_value_124 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_124 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_124 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 488;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_345 = mod_consts[128];
        tmp_call_arg_element_346 = mod_consts[196];
        tmp_call_arg_element_347 = DEEP_COPY_DICT(tstate, mod_consts[197]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 488;
        {
            PyObject *call_args[] = {tmp_call_arg_element_345, tmp_call_arg_element_346, tmp_call_arg_element_347};
            tmp_call_result_118 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_124, call_args);
        }

        Py_DECREF(tmp_call_arg_element_347);
        if (tmp_call_result_118 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 488;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_118);
    }
    {
        PyObject *tmp_called_value_125;
        PyObject *tmp_call_result_119;
        PyObject *tmp_call_arg_element_348;
        PyObject *tmp_call_arg_element_349;
        PyObject *tmp_call_arg_element_350;
        tmp_called_value_125 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_125 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_125 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 497;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_348 = mod_consts[128];
        tmp_call_arg_element_349 = mod_consts[198];
        tmp_call_arg_element_350 = DEEP_COPY_DICT(tstate, mod_consts[195]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 497;
        {
            PyObject *call_args[] = {tmp_call_arg_element_348, tmp_call_arg_element_349, tmp_call_arg_element_350};
            tmp_call_result_119 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_125, call_args);
        }

        Py_DECREF(tmp_call_arg_element_350);
        if (tmp_call_result_119 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 497;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_119);
    }
    {
        PyObject *tmp_called_value_126;
        PyObject *tmp_call_result_120;
        PyObject *tmp_call_arg_element_351;
        PyObject *tmp_call_arg_element_352;
        PyObject *tmp_call_arg_element_353;
        tmp_called_value_126 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_126 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_126 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 511;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_351 = mod_consts[128];
        tmp_call_arg_element_352 = mod_consts[199];
        tmp_call_arg_element_353 = DEEP_COPY_DICT(tstate, mod_consts[200]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 511;
        {
            PyObject *call_args[] = {tmp_call_arg_element_351, tmp_call_arg_element_352, tmp_call_arg_element_353};
            tmp_call_result_120 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_126, call_args);
        }

        Py_DECREF(tmp_call_arg_element_353);
        if (tmp_call_result_120 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 511;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_120);
    }
    {
        PyObject *tmp_called_value_127;
        PyObject *tmp_call_result_121;
        PyObject *tmp_call_arg_element_354;
        PyObject *tmp_call_arg_element_355;
        PyObject *tmp_call_arg_element_356;
        tmp_called_value_127 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_127 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_127 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 520;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_354 = mod_consts[128];
        tmp_call_arg_element_355 = mod_consts[201];
        tmp_call_arg_element_356 = DEEP_COPY_DICT(tstate, mod_consts[202]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 520;
        {
            PyObject *call_args[] = {tmp_call_arg_element_354, tmp_call_arg_element_355, tmp_call_arg_element_356};
            tmp_call_result_121 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_127, call_args);
        }

        Py_DECREF(tmp_call_arg_element_356);
        if (tmp_call_result_121 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 520;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_121);
    }
    {
        PyObject *tmp_called_value_128;
        PyObject *tmp_call_result_122;
        PyObject *tmp_call_arg_element_357;
        PyObject *tmp_call_arg_element_358;
        PyObject *tmp_call_arg_element_359;
        tmp_called_value_128 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_128 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_128 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 525;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_357 = mod_consts[128];
        tmp_call_arg_element_358 = mod_consts[203];
        tmp_call_arg_element_359 = DEEP_COPY_DICT(tstate, mod_consts[204]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 525;
        {
            PyObject *call_args[] = {tmp_call_arg_element_357, tmp_call_arg_element_358, tmp_call_arg_element_359};
            tmp_call_result_122 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_128, call_args);
        }

        Py_DECREF(tmp_call_arg_element_359);
        if (tmp_call_result_122 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 525;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_122);
    }
    {
        PyObject *tmp_called_value_129;
        PyObject *tmp_call_result_123;
        PyObject *tmp_call_arg_element_360;
        PyObject *tmp_call_arg_element_361;
        PyObject *tmp_call_arg_element_362;
        tmp_called_value_129 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_129 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_129 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 526;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_360 = mod_consts[128];
        tmp_call_arg_element_361 = mod_consts[205];
        tmp_call_arg_element_362 = DEEP_COPY_DICT(tstate, mod_consts[206]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 526;
        {
            PyObject *call_args[] = {tmp_call_arg_element_360, tmp_call_arg_element_361, tmp_call_arg_element_362};
            tmp_call_result_123 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_129, call_args);
        }

        Py_DECREF(tmp_call_arg_element_362);
        if (tmp_call_result_123 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 526;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_123);
    }
    {
        PyObject *tmp_called_value_130;
        PyObject *tmp_call_result_124;
        PyObject *tmp_call_arg_element_363;
        PyObject *tmp_call_arg_element_364;
        PyObject *tmp_call_arg_element_365;
        tmp_called_value_130 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_130 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_130 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 531;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_363 = mod_consts[128];
        tmp_call_arg_element_364 = mod_consts[207];
        tmp_call_arg_element_365 = DEEP_COPY_DICT(tstate, mod_consts[204]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 531;
        {
            PyObject *call_args[] = {tmp_call_arg_element_363, tmp_call_arg_element_364, tmp_call_arg_element_365};
            tmp_call_result_124 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_130, call_args);
        }

        Py_DECREF(tmp_call_arg_element_365);
        if (tmp_call_result_124 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 531;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_124);
    }
    {
        PyObject *tmp_called_value_131;
        PyObject *tmp_call_result_125;
        PyObject *tmp_call_arg_element_366;
        PyObject *tmp_call_arg_element_367;
        PyObject *tmp_call_arg_element_368;
        tmp_called_value_131 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_131 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_131 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 532;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_366 = mod_consts[128];
        tmp_call_arg_element_367 = mod_consts[208];
        tmp_call_arg_element_368 = DEEP_COPY_DICT(tstate, mod_consts[209]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 532;
        {
            PyObject *call_args[] = {tmp_call_arg_element_366, tmp_call_arg_element_367, tmp_call_arg_element_368};
            tmp_call_result_125 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_131, call_args);
        }

        Py_DECREF(tmp_call_arg_element_368);
        if (tmp_call_result_125 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 532;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_125);
    }
    {
        PyObject *tmp_called_value_132;
        PyObject *tmp_call_result_126;
        PyObject *tmp_call_arg_element_369;
        PyObject *tmp_call_arg_element_370;
        PyObject *tmp_call_arg_element_371;
        tmp_called_value_132 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$r(tstate);
        if (unlikely(tmp_called_value_132 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[35]);
        }

        if (tmp_called_value_132 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 541;

            goto try_except_handler_1;
        }
        tmp_call_arg_element_369 = mod_consts[210];
        tmp_call_arg_element_370 = mod_consts[191];
        tmp_call_arg_element_371 = DEEP_COPY_DICT(tstate, mod_consts[40]);
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 541;
        {
            PyObject *call_args[] = {tmp_call_arg_element_369, tmp_call_arg_element_370, tmp_call_arg_element_371};
            tmp_call_result_126 = CALL_FUNCTION_WITH_ARGS3(tstate, tmp_called_value_132, call_args);
        }

        Py_DECREF(tmp_call_arg_element_371);
        if (tmp_call_result_126 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 541;

            goto try_except_handler_1;
        }
        Py_DECREF(tmp_call_result_126);
    }
    goto try_end_1;
    // Exception handler code:
    try_except_handler_1:;
    exception_keeper_lineno_1 = exception_lineno;
    exception_lineno = 0;
    exception_keeper_name_1 = exception_state;
    INIT_ERROR_OCCURRED_STATE(&exception_state);

    // Preserve existing published exception id 1.
    exception_preserved_1 = GET_CURRENT_EXCEPTION(tstate);

    {
        PyTracebackObject *exception_tb = GET_EXCEPTION_STATE_TRACEBACK(&exception_keeper_name_1);
        if (exception_tb == NULL) {
            exception_tb = MAKE_TRACEBACK(frame_frame_Quartz$ImageKit$_metadata, exception_keeper_lineno_1);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_keeper_name_1, exception_tb);
        } else if (exception_keeper_lineno_1 != 0) {
            exception_tb = ADD_TRACEBACK(exception_tb, frame_frame_Quartz$ImageKit$_metadata, exception_keeper_lineno_1);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_keeper_name_1, exception_tb);
        }
    }

    PUBLISH_CURRENT_EXCEPTION(tstate, &exception_keeper_name_1);
    // Tried code:
    {
        PyObject *tmp_called_instance_2;
        PyObject *tmp_call_result_127;
        tmp_called_instance_2 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
        if (unlikely(tmp_called_instance_2 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
        }

        if (tmp_called_instance_2 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 543;

            goto try_except_handler_2;
        }
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 543;
        tmp_call_result_127 = CALL_METHOD_WITH_SINGLE_ARG(
            tstate,
            tmp_called_instance_2,
            mod_consts[36],
            PyTuple_GET_ITEM(mod_consts[211], 0)
        );

        if (tmp_call_result_127 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 543;

            goto try_except_handler_2;
        }
        Py_DECREF(tmp_call_result_127);
    }
    tmp_result = RERAISE_EXCEPTION(tstate, &exception_state);
    if (unlikely(tmp_result == false)) {
        exception_lineno = 71;
    }

    {
        PyTracebackObject *exception_tb = GET_EXCEPTION_STATE_TRACEBACK(&exception_state);

        if ((exception_tb != NULL) && (exception_tb->tb_frame == &frame_frame_Quartz$ImageKit$_metadata->m_frame)) {
            frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = exception_tb->tb_lineno;
        }
    }

    goto try_except_handler_2;
    NUITKA_CANNOT_GET_HERE("tried codes exits in all cases");
    return NULL;
    // Exception handler code:
    try_except_handler_2:;
    exception_keeper_lineno_2 = exception_lineno;
    exception_lineno = 0;
    exception_keeper_name_2 = exception_state;
    INIT_ERROR_OCCURRED_STATE(&exception_state);

    // Restore previous exception id 1.
    SET_CURRENT_EXCEPTION(tstate, &exception_preserved_1);

    // Re-raise.
    exception_state = exception_keeper_name_2;
    exception_lineno = exception_keeper_lineno_2;

    goto frame_exception_exit_1;
    // End of try:
    // End of try:
    try_end_1:;
    {
        PyObject *tmp_called_instance_3;
        PyObject *tmp_call_result_128;
        tmp_called_instance_3 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
        if (unlikely(tmp_called_instance_3 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
        }

        if (tmp_called_instance_3 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 543;

            goto frame_exception_exit_1;
        }
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 543;
        tmp_call_result_128 = CALL_METHOD_WITH_SINGLE_ARG(
            tstate,
            tmp_called_instance_3,
            mod_consts[36],
            PyTuple_GET_ITEM(mod_consts[211], 0)
        );

        if (tmp_call_result_128 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 543;

            goto frame_exception_exit_1;
        }
        Py_DECREF(tmp_call_result_128);
    }
    {
        PyObject *tmp_called_instance_4;
        PyObject *tmp_call_result_129;
        tmp_called_instance_4 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
        if (unlikely(tmp_called_instance_4 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
        }

        if (tmp_called_instance_4 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 545;

            goto frame_exception_exit_1;
        }
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 545;
        tmp_call_result_129 = CALL_METHOD_WITH_ARGS2(
            tstate,
            tmp_called_instance_4,
            mod_consts[212],
            &PyTuple_GET_ITEM(mod_consts[213], 0)
        );

        if (tmp_call_result_129 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 545;

            goto frame_exception_exit_1;
        }
        Py_DECREF(tmp_call_result_129);
    }
    {
        PyObject *tmp_called_instance_5;
        PyObject *tmp_call_result_130;
        tmp_called_instance_5 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
        if (unlikely(tmp_called_instance_5 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
        }

        if (tmp_called_instance_5 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 546;

            goto frame_exception_exit_1;
        }
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 546;
        tmp_call_result_130 = CALL_METHOD_WITH_ARGS2(
            tstate,
            tmp_called_instance_5,
            mod_consts[212],
            &PyTuple_GET_ITEM(mod_consts[214], 0)
        );

        if (tmp_call_result_130 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 546;

            goto frame_exception_exit_1;
        }
        Py_DECREF(tmp_call_result_130);
    }
    {
        PyObject *tmp_called_instance_6;
        PyObject *tmp_call_result_131;
        tmp_called_instance_6 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
        if (unlikely(tmp_called_instance_6 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
        }

        if (tmp_called_instance_6 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 547;

            goto frame_exception_exit_1;
        }
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 547;
        tmp_call_result_131 = CALL_METHOD_WITH_ARGS2(
            tstate,
            tmp_called_instance_6,
            mod_consts[212],
            &PyTuple_GET_ITEM(mod_consts[215], 0)
        );

        if (tmp_call_result_131 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 547;

            goto frame_exception_exit_1;
        }
        Py_DECREF(tmp_call_result_131);
    }
    {
        PyObject *tmp_assign_source_15;
        PyObject *tmp_dict_key_2;
        PyObject *tmp_dict_value_2;
        PyObject *tmp_called_value_133;
        PyObject *tmp_expression_value_5;
        PyObject *tmp_args_element_value_1;
        PyObject *tmp_args_element_value_2;
        PyObject *tmp_list_element_1;
        PyObject *tmp_called_value_134;
        PyObject *tmp_expression_value_6;
        tmp_dict_key_2 = mod_consts[216];
        tmp_expression_value_5 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
        if (unlikely(tmp_expression_value_5 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
        }

        if (tmp_expression_value_5 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));



            exception_lineno = 551;

            goto frame_exception_exit_1;
        }
        tmp_called_value_133 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_5, mod_consts[217]);
        if (tmp_called_value_133 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 551;

            goto frame_exception_exit_1;
        }
        tmp_args_element_value_1 = mod_consts[216];
        tmp_expression_value_6 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
        if (unlikely(tmp_expression_value_6 == NULL)) {
            RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
        }

        if (tmp_expression_value_6 == NULL) {
            assert(HAS_EXCEPTION_STATE(&exception_state));

            Py_DECREF(tmp_called_value_133);

            exception_lineno = 554;

            goto frame_exception_exit_1;
        }
        tmp_called_value_134 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_6, mod_consts[218]);
        if (tmp_called_value_134 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);
            Py_DECREF(tmp_called_value_133);

            exception_lineno = 554;

            goto frame_exception_exit_1;
        }
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 554;
        tmp_list_element_1 = CALL_FUNCTION_WITH_ARGS3_VECTORCALL(tstate, tmp_called_value_134, &PyTuple_GET_ITEM(mod_consts[219], 0), mod_consts[220]);
        Py_DECREF(tmp_called_value_134);
        if (tmp_list_element_1 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);
            Py_DECREF(tmp_called_value_133);

            exception_lineno = 554;

            goto frame_exception_exit_1;
        }
        tmp_args_element_value_2 = MAKE_LIST_EMPTY(tstate, 7);
        {
            PyObject *tmp_called_value_135;
            PyObject *tmp_expression_value_7;
            PyObject *tmp_called_value_136;
            PyObject *tmp_expression_value_8;
            PyObject *tmp_called_value_137;
            PyObject *tmp_expression_value_9;
            PyObject *tmp_called_value_138;
            PyObject *tmp_expression_value_10;
            PyObject *tmp_called_value_139;
            PyObject *tmp_expression_value_11;
            PyObject *tmp_called_value_140;
            PyObject *tmp_expression_value_12;
            PyList_SET_ITEM(tmp_args_element_value_2, 0, tmp_list_element_1);
            tmp_expression_value_7 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
            if (unlikely(tmp_expression_value_7 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_expression_value_7 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 555;

                goto list_build_exception_1;
            }
            tmp_called_value_135 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_7, mod_consts[218]);
            if (tmp_called_value_135 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 555;

                goto list_build_exception_1;
            }
            frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 555;
            tmp_list_element_1 = CALL_FUNCTION_WITH_ARGS3_VECTORCALL(tstate, tmp_called_value_135, &PyTuple_GET_ITEM(mod_consts[221], 0), mod_consts[220]);
            Py_DECREF(tmp_called_value_135);
            if (tmp_list_element_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 555;

                goto list_build_exception_1;
            }
            PyList_SET_ITEM(tmp_args_element_value_2, 1, tmp_list_element_1);
            tmp_expression_value_8 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
            if (unlikely(tmp_expression_value_8 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_expression_value_8 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 556;

                goto list_build_exception_1;
            }
            tmp_called_value_136 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_8, mod_consts[218]);
            if (tmp_called_value_136 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 556;

                goto list_build_exception_1;
            }
            frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 556;
            tmp_list_element_1 = CALL_FUNCTION_WITH_ARGS3_VECTORCALL(tstate, tmp_called_value_136, &PyTuple_GET_ITEM(mod_consts[222], 0), mod_consts[220]);
            Py_DECREF(tmp_called_value_136);
            if (tmp_list_element_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 556;

                goto list_build_exception_1;
            }
            PyList_SET_ITEM(tmp_args_element_value_2, 2, tmp_list_element_1);
            tmp_expression_value_9 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
            if (unlikely(tmp_expression_value_9 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_expression_value_9 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 557;

                goto list_build_exception_1;
            }
            tmp_called_value_137 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_9, mod_consts[218]);
            if (tmp_called_value_137 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 557;

                goto list_build_exception_1;
            }
            frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 557;
            tmp_list_element_1 = CALL_FUNCTION_WITH_ARGS3_VECTORCALL(tstate, tmp_called_value_137, &PyTuple_GET_ITEM(mod_consts[223], 0), mod_consts[220]);
            Py_DECREF(tmp_called_value_137);
            if (tmp_list_element_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 557;

                goto list_build_exception_1;
            }
            PyList_SET_ITEM(tmp_args_element_value_2, 3, tmp_list_element_1);
            tmp_expression_value_10 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
            if (unlikely(tmp_expression_value_10 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_expression_value_10 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 558;

                goto list_build_exception_1;
            }
            tmp_called_value_138 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_10, mod_consts[218]);
            if (tmp_called_value_138 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 558;

                goto list_build_exception_1;
            }
            frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 558;
            tmp_list_element_1 = CALL_FUNCTION_WITH_ARGS3_VECTORCALL(tstate, tmp_called_value_138, &PyTuple_GET_ITEM(mod_consts[224], 0), mod_consts[220]);
            Py_DECREF(tmp_called_value_138);
            if (tmp_list_element_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 558;

                goto list_build_exception_1;
            }
            PyList_SET_ITEM(tmp_args_element_value_2, 4, tmp_list_element_1);
            tmp_expression_value_11 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
            if (unlikely(tmp_expression_value_11 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_expression_value_11 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 559;

                goto list_build_exception_1;
            }
            tmp_called_value_139 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_11, mod_consts[218]);
            if (tmp_called_value_139 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 559;

                goto list_build_exception_1;
            }
            frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 559;
            tmp_list_element_1 = CALL_FUNCTION_WITH_ARGS3_VECTORCALL(tstate, tmp_called_value_139, &PyTuple_GET_ITEM(mod_consts[225], 0), mod_consts[220]);
            Py_DECREF(tmp_called_value_139);
            if (tmp_list_element_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 559;

                goto list_build_exception_1;
            }
            PyList_SET_ITEM(tmp_args_element_value_2, 5, tmp_list_element_1);
            tmp_expression_value_12 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
            if (unlikely(tmp_expression_value_12 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_expression_value_12 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 560;

                goto list_build_exception_1;
            }
            tmp_called_value_140 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_12, mod_consts[218]);
            if (tmp_called_value_140 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 560;

                goto list_build_exception_1;
            }
            frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 560;
            tmp_list_element_1 = CALL_FUNCTION_WITH_ARGS3_VECTORCALL(tstate, tmp_called_value_140, &PyTuple_GET_ITEM(mod_consts[226], 0), mod_consts[220]);
            Py_DECREF(tmp_called_value_140);
            if (tmp_list_element_1 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 560;

                goto list_build_exception_1;
            }
            PyList_SET_ITEM(tmp_args_element_value_2, 6, tmp_list_element_1);
        }
        goto list_build_noexception_1;
        // Exception handling pass through code for list_build:
        list_build_exception_1:;
        Py_DECREF(tmp_called_value_133);
        Py_DECREF(tmp_args_element_value_2);
        goto frame_exception_exit_1;
        // Finished with no exception for list_build:
        list_build_noexception_1:;
        frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 551;
        {
            PyObject *call_args[] = {tmp_args_element_value_1, tmp_args_element_value_2};
            tmp_dict_value_2 = CALL_FUNCTION_WITH_ARGS2(tstate, tmp_called_value_133, call_args);
        }

        Py_DECREF(tmp_called_value_133);
        Py_DECREF(tmp_args_element_value_2);
        if (tmp_dict_value_2 == NULL) {
            assert(HAS_ERROR_OCCURRED(tstate));

            FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


            exception_lineno = 551;

            goto frame_exception_exit_1;
        }
        tmp_assign_source_15 = _PyDict_NewPresized( 5 );
        {
            PyObject *tmp_called_value_141;
            PyObject *tmp_expression_value_13;
            PyObject *tmp_args_element_value_3;
            PyObject *tmp_args_element_value_4;
            PyObject *tmp_list_element_2;
            PyObject *tmp_called_value_142;
            PyObject *tmp_expression_value_14;
            PyObject *tmp_called_value_147;
            PyObject *tmp_expression_value_19;
            PyObject *tmp_args_element_value_5;
            PyObject *tmp_args_element_value_6;
            PyObject *tmp_list_element_3;
            PyObject *tmp_called_value_148;
            PyObject *tmp_expression_value_20;
            PyObject *tmp_called_value_149;
            PyObject *tmp_expression_value_21;
            PyObject *tmp_args_element_value_7;
            PyObject *tmp_args_element_value_8;
            PyObject *tmp_list_element_4;
            PyObject *tmp_called_value_150;
            PyObject *tmp_expression_value_22;
            PyObject *tmp_called_value_154;
            PyObject *tmp_expression_value_26;
            PyObject *tmp_args_element_value_9;
            PyObject *tmp_args_element_value_10;
            PyObject *tmp_list_element_5;
            PyObject *tmp_called_value_155;
            PyObject *tmp_expression_value_27;
            tmp_res = PyDict_SetItem(tmp_assign_source_15, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[227];
            tmp_expression_value_13 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
            if (unlikely(tmp_expression_value_13 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_expression_value_13 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 563;

                goto dict_build_exception_2;
            }
            tmp_called_value_141 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_13, mod_consts[217]);
            if (tmp_called_value_141 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 563;

                goto dict_build_exception_2;
            }
            tmp_args_element_value_3 = mod_consts[227];
            tmp_expression_value_14 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
            if (unlikely(tmp_expression_value_14 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_expression_value_14 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));

                Py_DECREF(tmp_called_value_141);

                exception_lineno = 566;

                goto dict_build_exception_2;
            }
            tmp_called_value_142 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_14, mod_consts[218]);
            if (tmp_called_value_142 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);
                Py_DECREF(tmp_called_value_141);

                exception_lineno = 566;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 566;
            tmp_list_element_2 = CALL_FUNCTION_WITH_ARGS3_VECTORCALL(tstate, tmp_called_value_142, &PyTuple_GET_ITEM(mod_consts[228], 0), mod_consts[220]);
            Py_DECREF(tmp_called_value_142);
            if (tmp_list_element_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);
                Py_DECREF(tmp_called_value_141);

                exception_lineno = 566;

                goto dict_build_exception_2;
            }
            tmp_args_element_value_4 = MAKE_LIST_EMPTY(tstate, 5);
            {
                PyObject *tmp_called_value_143;
                PyObject *tmp_expression_value_15;
                PyObject *tmp_called_value_144;
                PyObject *tmp_expression_value_16;
                PyObject *tmp_called_value_145;
                PyObject *tmp_expression_value_17;
                PyObject *tmp_called_value_146;
                PyObject *tmp_expression_value_18;
                PyList_SET_ITEM(tmp_args_element_value_4, 0, tmp_list_element_2);
                tmp_expression_value_15 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
                if (unlikely(tmp_expression_value_15 == NULL)) {
                    RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
                }

                if (tmp_expression_value_15 == NULL) {
                    assert(HAS_EXCEPTION_STATE(&exception_state));



                    exception_lineno = 569;

                    goto list_build_exception_2;
                }
                tmp_called_value_143 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_15, mod_consts[218]);
                if (tmp_called_value_143 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 569;

                    goto list_build_exception_2;
                }
                frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 569;
                tmp_list_element_2 = CALL_FUNCTION_WITH_ARGS3_VECTORCALL(tstate, tmp_called_value_143, &PyTuple_GET_ITEM(mod_consts[229], 0), mod_consts[220]);
                Py_DECREF(tmp_called_value_143);
                if (tmp_list_element_2 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 569;

                    goto list_build_exception_2;
                }
                PyList_SET_ITEM(tmp_args_element_value_4, 1, tmp_list_element_2);
                tmp_expression_value_16 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
                if (unlikely(tmp_expression_value_16 == NULL)) {
                    RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
                }

                if (tmp_expression_value_16 == NULL) {
                    assert(HAS_EXCEPTION_STATE(&exception_state));



                    exception_lineno = 572;

                    goto list_build_exception_2;
                }
                tmp_called_value_144 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_16, mod_consts[218]);
                if (tmp_called_value_144 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 572;

                    goto list_build_exception_2;
                }
                frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 572;
                tmp_list_element_2 = CALL_FUNCTION_WITH_ARGS3_VECTORCALL(tstate, tmp_called_value_144, &PyTuple_GET_ITEM(mod_consts[230], 0), mod_consts[220]);
                Py_DECREF(tmp_called_value_144);
                if (tmp_list_element_2 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 572;

                    goto list_build_exception_2;
                }
                PyList_SET_ITEM(tmp_args_element_value_4, 2, tmp_list_element_2);
                tmp_expression_value_17 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
                if (unlikely(tmp_expression_value_17 == NULL)) {
                    RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
                }

                if (tmp_expression_value_17 == NULL) {
                    assert(HAS_EXCEPTION_STATE(&exception_state));



                    exception_lineno = 575;

                    goto list_build_exception_2;
                }
                tmp_called_value_145 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_17, mod_consts[218]);
                if (tmp_called_value_145 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 575;

                    goto list_build_exception_2;
                }
                frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 575;
                tmp_list_element_2 = CALL_FUNCTION_WITH_ARGS3_VECTORCALL(tstate, tmp_called_value_145, &PyTuple_GET_ITEM(mod_consts[231], 0), mod_consts[220]);
                Py_DECREF(tmp_called_value_145);
                if (tmp_list_element_2 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 575;

                    goto list_build_exception_2;
                }
                PyList_SET_ITEM(tmp_args_element_value_4, 3, tmp_list_element_2);
                tmp_expression_value_18 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
                if (unlikely(tmp_expression_value_18 == NULL)) {
                    RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
                }

                if (tmp_expression_value_18 == NULL) {
                    assert(HAS_EXCEPTION_STATE(&exception_state));



                    exception_lineno = 581;

                    goto list_build_exception_2;
                }
                tmp_called_value_146 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_18, mod_consts[218]);
                if (tmp_called_value_146 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 581;

                    goto list_build_exception_2;
                }
                frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 581;
                tmp_list_element_2 = CALL_FUNCTION_WITH_ARGS3_VECTORCALL(tstate, tmp_called_value_146, &PyTuple_GET_ITEM(mod_consts[232], 0), mod_consts[220]);
                Py_DECREF(tmp_called_value_146);
                if (tmp_list_element_2 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 581;

                    goto list_build_exception_2;
                }
                PyList_SET_ITEM(tmp_args_element_value_4, 4, tmp_list_element_2);
            }
            goto list_build_noexception_2;
            // Exception handling pass through code for list_build:
            list_build_exception_2:;
            Py_DECREF(tmp_called_value_141);
            Py_DECREF(tmp_args_element_value_4);
            goto dict_build_exception_2;
            // Finished with no exception for list_build:
            list_build_noexception_2:;
            frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 563;
            {
                PyObject *call_args[] = {tmp_args_element_value_3, tmp_args_element_value_4};
                tmp_dict_value_2 = CALL_FUNCTION_WITH_ARGS2(tstate, tmp_called_value_141, call_args);
            }

            Py_DECREF(tmp_called_value_141);
            Py_DECREF(tmp_args_element_value_4);
            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 563;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_assign_source_15, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[233];
            tmp_expression_value_19 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
            if (unlikely(tmp_expression_value_19 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_expression_value_19 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 589;

                goto dict_build_exception_2;
            }
            tmp_called_value_147 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_19, mod_consts[217]);
            if (tmp_called_value_147 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 589;

                goto dict_build_exception_2;
            }
            tmp_args_element_value_5 = mod_consts[233];
            tmp_expression_value_20 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
            if (unlikely(tmp_expression_value_20 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_expression_value_20 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));

                Py_DECREF(tmp_called_value_147);

                exception_lineno = 592;

                goto dict_build_exception_2;
            }
            tmp_called_value_148 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_20, mod_consts[218]);
            if (tmp_called_value_148 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);
                Py_DECREF(tmp_called_value_147);

                exception_lineno = 592;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 592;
            tmp_list_element_3 = CALL_FUNCTION_WITH_ARGS3_VECTORCALL(tstate, tmp_called_value_148, &PyTuple_GET_ITEM(mod_consts[234], 0), mod_consts[220]);
            Py_DECREF(tmp_called_value_148);
            if (tmp_list_element_3 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);
                Py_DECREF(tmp_called_value_147);

                exception_lineno = 592;

                goto dict_build_exception_2;
            }
            tmp_args_element_value_6 = MAKE_LIST_EMPTY(tstate, 1);
            PyList_SET_ITEM(tmp_args_element_value_6, 0, tmp_list_element_3);
            frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 589;
            {
                PyObject *call_args[] = {tmp_args_element_value_5, tmp_args_element_value_6};
                tmp_dict_value_2 = CALL_FUNCTION_WITH_ARGS2(tstate, tmp_called_value_147, call_args);
            }

            Py_DECREF(tmp_called_value_147);
            Py_DECREF(tmp_args_element_value_6);
            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 589;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_assign_source_15, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[235];
            tmp_expression_value_21 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
            if (unlikely(tmp_expression_value_21 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_expression_value_21 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 597;

                goto dict_build_exception_2;
            }
            tmp_called_value_149 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_21, mod_consts[217]);
            if (tmp_called_value_149 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 597;

                goto dict_build_exception_2;
            }
            tmp_args_element_value_7 = mod_consts[235];
            tmp_expression_value_22 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
            if (unlikely(tmp_expression_value_22 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_expression_value_22 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));

                Py_DECREF(tmp_called_value_149);

                exception_lineno = 600;

                goto dict_build_exception_2;
            }
            tmp_called_value_150 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_22, mod_consts[218]);
            if (tmp_called_value_150 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);
                Py_DECREF(tmp_called_value_149);

                exception_lineno = 600;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 600;
            tmp_list_element_4 = CALL_FUNCTION_WITH_ARGS3_VECTORCALL(tstate, tmp_called_value_150, &PyTuple_GET_ITEM(mod_consts[236], 0), mod_consts[220]);
            Py_DECREF(tmp_called_value_150);
            if (tmp_list_element_4 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);
                Py_DECREF(tmp_called_value_149);

                exception_lineno = 600;

                goto dict_build_exception_2;
            }
            tmp_args_element_value_8 = MAKE_LIST_EMPTY(tstate, 4);
            {
                PyObject *tmp_called_value_151;
                PyObject *tmp_expression_value_23;
                PyObject *tmp_called_value_152;
                PyObject *tmp_expression_value_24;
                PyObject *tmp_called_value_153;
                PyObject *tmp_expression_value_25;
                PyList_SET_ITEM(tmp_args_element_value_8, 0, tmp_list_element_4);
                tmp_expression_value_23 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
                if (unlikely(tmp_expression_value_23 == NULL)) {
                    RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
                }

                if (tmp_expression_value_23 == NULL) {
                    assert(HAS_EXCEPTION_STATE(&exception_state));



                    exception_lineno = 606;

                    goto list_build_exception_3;
                }
                tmp_called_value_151 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_23, mod_consts[218]);
                if (tmp_called_value_151 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 606;

                    goto list_build_exception_3;
                }
                frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 606;
                tmp_list_element_4 = CALL_FUNCTION_WITH_ARGS3_VECTORCALL(tstate, tmp_called_value_151, &PyTuple_GET_ITEM(mod_consts[237], 0), mod_consts[220]);
                Py_DECREF(tmp_called_value_151);
                if (tmp_list_element_4 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 606;

                    goto list_build_exception_3;
                }
                PyList_SET_ITEM(tmp_args_element_value_8, 1, tmp_list_element_4);
                tmp_expression_value_24 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
                if (unlikely(tmp_expression_value_24 == NULL)) {
                    RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
                }

                if (tmp_expression_value_24 == NULL) {
                    assert(HAS_EXCEPTION_STATE(&exception_state));



                    exception_lineno = 609;

                    goto list_build_exception_3;
                }
                tmp_called_value_152 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_24, mod_consts[218]);
                if (tmp_called_value_152 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 609;

                    goto list_build_exception_3;
                }
                frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 609;
                tmp_list_element_4 = CALL_FUNCTION_WITH_ARGS3_VECTORCALL(tstate, tmp_called_value_152, &PyTuple_GET_ITEM(mod_consts[238], 0), mod_consts[220]);
                Py_DECREF(tmp_called_value_152);
                if (tmp_list_element_4 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 609;

                    goto list_build_exception_3;
                }
                PyList_SET_ITEM(tmp_args_element_value_8, 2, tmp_list_element_4);
                tmp_expression_value_25 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
                if (unlikely(tmp_expression_value_25 == NULL)) {
                    RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
                }

                if (tmp_expression_value_25 == NULL) {
                    assert(HAS_EXCEPTION_STATE(&exception_state));



                    exception_lineno = 615;

                    goto list_build_exception_3;
                }
                tmp_called_value_153 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_25, mod_consts[218]);
                if (tmp_called_value_153 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 615;

                    goto list_build_exception_3;
                }
                frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 615;
                tmp_list_element_4 = CALL_FUNCTION_WITH_ARGS3_VECTORCALL(tstate, tmp_called_value_153, &PyTuple_GET_ITEM(mod_consts[239], 0), mod_consts[220]);
                Py_DECREF(tmp_called_value_153);
                if (tmp_list_element_4 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 615;

                    goto list_build_exception_3;
                }
                PyList_SET_ITEM(tmp_args_element_value_8, 3, tmp_list_element_4);
            }
            goto list_build_noexception_3;
            // Exception handling pass through code for list_build:
            list_build_exception_3:;
            Py_DECREF(tmp_called_value_149);
            Py_DECREF(tmp_args_element_value_8);
            goto dict_build_exception_2;
            // Finished with no exception for list_build:
            list_build_noexception_3:;
            frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 597;
            {
                PyObject *call_args[] = {tmp_args_element_value_7, tmp_args_element_value_8};
                tmp_dict_value_2 = CALL_FUNCTION_WITH_ARGS2(tstate, tmp_called_value_149, call_args);
            }

            Py_DECREF(tmp_called_value_149);
            Py_DECREF(tmp_args_element_value_8);
            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 597;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_assign_source_15, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
            tmp_dict_key_2 = mod_consts[240];
            tmp_expression_value_26 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
            if (unlikely(tmp_expression_value_26 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_expression_value_26 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));



                exception_lineno = 623;

                goto dict_build_exception_2;
            }
            tmp_called_value_154 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_26, mod_consts[217]);
            if (tmp_called_value_154 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 623;

                goto dict_build_exception_2;
            }
            tmp_args_element_value_9 = mod_consts[240];
            tmp_expression_value_27 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
            if (unlikely(tmp_expression_value_27 == NULL)) {
                RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
            }

            if (tmp_expression_value_27 == NULL) {
                assert(HAS_EXCEPTION_STATE(&exception_state));

                Py_DECREF(tmp_called_value_154);

                exception_lineno = 626;

                goto dict_build_exception_2;
            }
            tmp_called_value_155 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_27, mod_consts[218]);
            if (tmp_called_value_155 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);
                Py_DECREF(tmp_called_value_154);

                exception_lineno = 626;

                goto dict_build_exception_2;
            }
            frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 626;
            tmp_list_element_5 = CALL_FUNCTION_WITH_ARGS3_VECTORCALL(tstate, tmp_called_value_155, &PyTuple_GET_ITEM(mod_consts[241], 0), mod_consts[220]);
            Py_DECREF(tmp_called_value_155);
            if (tmp_list_element_5 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);
                Py_DECREF(tmp_called_value_154);

                exception_lineno = 626;

                goto dict_build_exception_2;
            }
            tmp_args_element_value_10 = MAKE_LIST_EMPTY(tstate, 7);
            {
                PyObject *tmp_called_value_156;
                PyObject *tmp_expression_value_28;
                PyObject *tmp_called_value_157;
                PyObject *tmp_expression_value_29;
                PyObject *tmp_called_value_158;
                PyObject *tmp_expression_value_30;
                PyObject *tmp_called_value_159;
                PyObject *tmp_expression_value_31;
                PyObject *tmp_called_value_160;
                PyObject *tmp_expression_value_32;
                PyObject *tmp_called_value_161;
                PyObject *tmp_expression_value_33;
                PyList_SET_ITEM(tmp_args_element_value_10, 0, tmp_list_element_5);
                tmp_expression_value_28 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
                if (unlikely(tmp_expression_value_28 == NULL)) {
                    RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
                }

                if (tmp_expression_value_28 == NULL) {
                    assert(HAS_EXCEPTION_STATE(&exception_state));



                    exception_lineno = 629;

                    goto list_build_exception_4;
                }
                tmp_called_value_156 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_28, mod_consts[218]);
                if (tmp_called_value_156 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 629;

                    goto list_build_exception_4;
                }
                frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 629;
                tmp_list_element_5 = CALL_FUNCTION_WITH_ARGS3_VECTORCALL(tstate, tmp_called_value_156, &PyTuple_GET_ITEM(mod_consts[242], 0), mod_consts[220]);
                Py_DECREF(tmp_called_value_156);
                if (tmp_list_element_5 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 629;

                    goto list_build_exception_4;
                }
                PyList_SET_ITEM(tmp_args_element_value_10, 1, tmp_list_element_5);
                tmp_expression_value_29 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
                if (unlikely(tmp_expression_value_29 == NULL)) {
                    RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
                }

                if (tmp_expression_value_29 == NULL) {
                    assert(HAS_EXCEPTION_STATE(&exception_state));



                    exception_lineno = 632;

                    goto list_build_exception_4;
                }
                tmp_called_value_157 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_29, mod_consts[218]);
                if (tmp_called_value_157 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 632;

                    goto list_build_exception_4;
                }
                frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 632;
                tmp_list_element_5 = CALL_FUNCTION_WITH_ARGS3_VECTORCALL(tstate, tmp_called_value_157, &PyTuple_GET_ITEM(mod_consts[243], 0), mod_consts[220]);
                Py_DECREF(tmp_called_value_157);
                if (tmp_list_element_5 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 632;

                    goto list_build_exception_4;
                }
                PyList_SET_ITEM(tmp_args_element_value_10, 2, tmp_list_element_5);
                tmp_expression_value_30 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
                if (unlikely(tmp_expression_value_30 == NULL)) {
                    RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
                }

                if (tmp_expression_value_30 == NULL) {
                    assert(HAS_EXCEPTION_STATE(&exception_state));



                    exception_lineno = 638;

                    goto list_build_exception_4;
                }
                tmp_called_value_158 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_30, mod_consts[218]);
                if (tmp_called_value_158 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 638;

                    goto list_build_exception_4;
                }
                frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 638;
                tmp_list_element_5 = CALL_FUNCTION_WITH_ARGS3_VECTORCALL(tstate, tmp_called_value_158, &PyTuple_GET_ITEM(mod_consts[244], 0), mod_consts[220]);
                Py_DECREF(tmp_called_value_158);
                if (tmp_list_element_5 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 638;

                    goto list_build_exception_4;
                }
                PyList_SET_ITEM(tmp_args_element_value_10, 3, tmp_list_element_5);
                tmp_expression_value_31 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
                if (unlikely(tmp_expression_value_31 == NULL)) {
                    RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
                }

                if (tmp_expression_value_31 == NULL) {
                    assert(HAS_EXCEPTION_STATE(&exception_state));



                    exception_lineno = 641;

                    goto list_build_exception_4;
                }
                tmp_called_value_159 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_31, mod_consts[218]);
                if (tmp_called_value_159 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 641;

                    goto list_build_exception_4;
                }
                frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 641;
                tmp_list_element_5 = CALL_FUNCTION_WITH_ARGS3_VECTORCALL(tstate, tmp_called_value_159, &PyTuple_GET_ITEM(mod_consts[245], 0), mod_consts[220]);
                Py_DECREF(tmp_called_value_159);
                if (tmp_list_element_5 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 641;

                    goto list_build_exception_4;
                }
                PyList_SET_ITEM(tmp_args_element_value_10, 4, tmp_list_element_5);
                tmp_expression_value_32 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
                if (unlikely(tmp_expression_value_32 == NULL)) {
                    RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
                }

                if (tmp_expression_value_32 == NULL) {
                    assert(HAS_EXCEPTION_STATE(&exception_state));



                    exception_lineno = 644;

                    goto list_build_exception_4;
                }
                tmp_called_value_160 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_32, mod_consts[218]);
                if (tmp_called_value_160 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 644;

                    goto list_build_exception_4;
                }
                frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 644;
                tmp_list_element_5 = CALL_FUNCTION_WITH_ARGS3_VECTORCALL(tstate, tmp_called_value_160, &PyTuple_GET_ITEM(mod_consts[246], 0), mod_consts[220]);
                Py_DECREF(tmp_called_value_160);
                if (tmp_list_element_5 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 644;

                    goto list_build_exception_4;
                }
                PyList_SET_ITEM(tmp_args_element_value_10, 5, tmp_list_element_5);
                tmp_expression_value_33 = module_var_accessor_Quartz$$36$ImageKit$$36$_metadata$objc(tstate);
                if (unlikely(tmp_expression_value_33 == NULL)) {
                    RAISE_CURRENT_EXCEPTION_NAME_ERROR(tstate, &exception_state, mod_consts[5]);
                }

                if (tmp_expression_value_33 == NULL) {
                    assert(HAS_EXCEPTION_STATE(&exception_state));



                    exception_lineno = 647;

                    goto list_build_exception_4;
                }
                tmp_called_value_161 = LOOKUP_ATTRIBUTE(tstate, tmp_expression_value_33, mod_consts[218]);
                if (tmp_called_value_161 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 647;

                    goto list_build_exception_4;
                }
                frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 647;
                tmp_list_element_5 = CALL_FUNCTION_WITH_ARGS3_VECTORCALL(tstate, tmp_called_value_161, &PyTuple_GET_ITEM(mod_consts[247], 0), mod_consts[220]);
                Py_DECREF(tmp_called_value_161);
                if (tmp_list_element_5 == NULL) {
                    assert(HAS_ERROR_OCCURRED(tstate));

                    FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                    exception_lineno = 647;

                    goto list_build_exception_4;
                }
                PyList_SET_ITEM(tmp_args_element_value_10, 6, tmp_list_element_5);
            }
            goto list_build_noexception_4;
            // Exception handling pass through code for list_build:
            list_build_exception_4:;
            Py_DECREF(tmp_called_value_154);
            Py_DECREF(tmp_args_element_value_10);
            goto dict_build_exception_2;
            // Finished with no exception for list_build:
            list_build_noexception_4:;
            frame_frame_Quartz$ImageKit$_metadata->m_frame.f_lineno = 623;
            {
                PyObject *call_args[] = {tmp_args_element_value_9, tmp_args_element_value_10};
                tmp_dict_value_2 = CALL_FUNCTION_WITH_ARGS2(tstate, tmp_called_value_154, call_args);
            }

            Py_DECREF(tmp_called_value_154);
            Py_DECREF(tmp_args_element_value_10);
            if (tmp_dict_value_2 == NULL) {
                assert(HAS_ERROR_OCCURRED(tstate));

                FETCH_ERROR_OCCURRED_STATE(tstate, &exception_state);


                exception_lineno = 623;

                goto dict_build_exception_2;
            }
            tmp_res = PyDict_SetItem(tmp_assign_source_15, tmp_dict_key_2, tmp_dict_value_2);
            Py_DECREF(tmp_dict_value_2);
            assert(!(tmp_res != 0));
        }
        goto dict_build_noexception_2;
        // Exception handling pass through code for dict_build:
        dict_build_exception_2:;
        Py_DECREF(tmp_assign_source_15);
        goto frame_exception_exit_1;
        // Finished with no exception for dict_build:
        dict_build_noexception_2:;
        UPDATE_STRING_DICT1(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[248], tmp_assign_source_15);
    }


    // Put the previous frame back on top.
    popFrameStack(tstate);

    goto frame_no_exception_1;
    frame_exception_exit_1:


    {
        PyTracebackObject *exception_tb = GET_EXCEPTION_STATE_TRACEBACK(&exception_state);
        if (exception_tb == NULL) {
            exception_tb = MAKE_TRACEBACK(frame_frame_Quartz$ImageKit$_metadata, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        } else if (exception_tb->tb_frame != &frame_frame_Quartz$ImageKit$_metadata->m_frame) {
            exception_tb = ADD_TRACEBACK(exception_tb, frame_frame_Quartz$ImageKit$_metadata, exception_lineno);
            SET_EXCEPTION_STATE_TRACEBACK(&exception_state, exception_tb);
        }
    }



    assertFrameObject(frame_frame_Quartz$ImageKit$_metadata);

    // Put the previous frame back on top.
    popFrameStack(tstate);

    // Return the error.
    goto module_exception_exit;
    frame_no_exception_1:;
    {
        PyObject *tmp_assign_source_16;
        tmp_assign_source_16 = MAKE_DICT_EMPTY(tstate);
        UPDATE_STRING_DICT1(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)mod_consts[249], tmp_assign_source_16);
    }

    // Report to PGO about leaving the module without error.
    PGO_onModuleExit("Quartz$ImageKit$_metadata", false);

#if _NUITKA_MODULE_MODE && 0
    {
        PyObject *post_load = IMPORT_EMBEDDED_MODULE(tstate, "Quartz.ImageKit._metadata" "-postLoad");
        if (post_load == NULL) {
            return NULL;
        }
    }
#endif

    Py_INCREF(module_Quartz$ImageKit$_metadata);
    return module_Quartz$ImageKit$_metadata;
    module_exception_exit:

#if _NUITKA_MODULE_MODE && 0
    {
        PyObject *module_name = GET_STRING_DICT_VALUE(moduledict_Quartz$ImageKit$_metadata, (Nuitka_StringObject *)const_str_plain___name__);

        if (module_name != NULL) {
            Nuitka_DelModule(tstate, module_name);
        }
    }
#endif
    PGO_onModuleExit("Quartz$ImageKit$_metadata", false);

    RESTORE_ERROR_OCCURRED_STATE(tstate, &exception_state);
    return NULL;
}
