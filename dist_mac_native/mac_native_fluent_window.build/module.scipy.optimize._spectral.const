��       �_check_unknown_options�.��       �cheng��cruz���.��       �Invalid value �.��       � �.��       � for 'line_search'�.�K.��       �eta_strategy�.��'       �#_root_df_sane.<locals>.eta_strategy�.��	       �fnorm�.��        �_root_df_sane.<locals>.fnorm�.��
       �fmerit�.��!       �_root_df_sane.<locals>.fmerit�.��       �
_wrap_func�.��       �collections�.��	       �deque�.��
       G?�      .��*       �&too many function evaluations required�.��       �F_k�.��	       �print�.��#       �iter %d: ||F|| = %g, sigma = %g�.��       �k�.��       �sigma_k�.��       �callback�.��       �x_k�.��       �ftol�.��       �F_0_norm�.��	       �fatol�.��       �successful convergence�.��
       �	sigma_eps�.��       �np�.��       �sign�.��       �cruz�.��!       �_nonmonotone_line_search_cruz�.��       �f�.��       �prev_fs�.��	       �eta���.��	       �cheng�.��"       �_nonmonotone_line_search_cheng�.��       �f_k�.��       �C�.��       �Q�.��       �_NoConvergence�.��       �xp�.��       �Fp�.��       �vdot�.��       �fp�.��
       �append�.��       �_wrap_result�.��       �shape���.��       �OptimizeResult�.��       �df-sane�.��8       (�x��success��message��fun��nfev��nit��method�t�.���      X�  
    Solve nonlinear equation with the DF-SANE method

    Options
    -------
    ftol : float, optional
        Relative norm tolerance.
    fatol : float, optional
        Absolute norm tolerance.
        Algorithm terminates when ``||func(x)|| < fatol + ftol ||func(x_0)||``.
    fnorm : callable, optional
        Norm to use in the convergence check. If None, 2-norm is used.
    maxfev : int, optional
        Maximum number of function evaluations.
    disp : bool, optional
        Whether to print convergence process to stdout.
    eta_strategy : callable, optional
        Choice of the ``eta_k`` parameter, which gives slack for growth
        of ``||F||**2``.  Called as ``eta_k = eta_strategy(k, x, F)`` with
        `k` the iteration number, `x` the current iterate and `F` the current
        residual. Should satisfy ``eta_k > 0`` and ``sum(eta, k=0..inf) < inf``.
        Default: ``||F||**2 / (1 + k)**2``.
    sigma_eps : float, optional
        The spectral coefficient is constrained to ``sigma_eps < sigma < 1/sigma_eps``.
        Default: 1e-10
    sigma_0 : float, optional
        Initial spectral coefficient.
        Default: 1.0
    M : int, optional
        Number of iterates to include in the nonmonotonic line search.
        Default: 10
    line_search : {'cruz', 'cheng'}
        Type of line search to employ. 'cruz' is the original one defined in
        [Martinez & Raydan. Math. Comp. 75, 1429 (2006)], 'cheng' is
        a modified search defined in [Cheng & Li. IMA J. Numer. Anal. 29, 814 (2009)].
        Default: 'cruz'

    References
    ----------
    .. [1] "Spectral residual method without gradient information for solving
           large-scale nonlinear systems of equations." W. La Cruz,
           J.M. Martinez, M. Raydan. Math. Comp. **75**, 1429 (2006).
    .. [2] W. La Cruz, Opt. Meth. Software, 29, 24 (2014).
    .. [3] W. Cheng, D.-H. Li. IMA J. Numer. Anal. **29**, 814 (2009).

    �.��       �f_0�.��       �nexp�.��
       �linalg�.��       �norm�.��       �asarray�.�h1.��	       �ravel�.��       �iscomplexobj�.��
       �	wrap_func�.��!       �_wrap_func.<locals>.wrap_func�.��       �
_complex2real�.��       �F�.���      X�  
    Wrap a function and an initial value so that (i) complex values
    are wrapped to reals, and (ii) value for a merit function
    fmerit(x, f) is computed at the same time, (iii) iteration count
    is maintained and an exception is raised if it is exceeded.

    Parameters
    ----------
    func : callable
        Function to wrap
    x0 : ndarray
        Initial value
    fmerit : callable
        Merit function fmerit(f) for computing merit value from residual.
    nfev_list : list
        List to store number of evaluations in. Should be [0] in the beginning.
    maxfev : int
        Maximum number of evaluations before _NoConvergence is raised.
    args : tuple
        Extra arguments to func

    Returns
    -------
    wrap_func : callable
        Wrapped function, to be called as
        ``F, fp = wrap_func(x0)``
    x0_wrap : ndarray of float
        Wrapped initial value; raveled to 1-D and complex
        values mapped to reals.
    x0_shape : tuple
        Shape of the initial value array
    f : float
        Merit function at F
    F : ndarray of float
        Residual at x0_wrap
    is_complex : bool
        Whether complex values were mapped to reals

    �.��
       �	nfev_list�.��
       �maxfev�.��       �
_real2complex�.��       �reshape�.��       �x0_shape�.��       �func�.��       �args�.��E       �A
    Convert from real to complex and reshape result arrays.
    �.��       �ascontiguousarray�.��!       }��dtype��builtins��float���s.��       �view�.��       �
complex128�.��       }�hThU�complex���s.��       �float64�.��0       �,
Spectral Algorithm for Nonlinear Equations
�.��       �__doc__�.��       �__file__�.��
       �origin�.��       �has_location�.��       �
__cached__�.��	       �numpy�.��       �scipy.optimize�.��       �scipy.optimize._optimize�.��       h ��.��       �_linesearch�.��       h h&��.��       hU�	Exception�����.��       �__prepare__�.��       �__getitem__�.��2       �.%s.__prepare__() must return a mapping, not %s�.��       �__name__�.��       �<metaclass>�.��       �scipy.optimize._spectral�.��       �
__module__�.��       �__qualname__�.��       �__orig_bases__�.��9       ()G>Ey��0�:G�n���YM�NN�K
NG=�|��׽�G?�      �cruz�t�.��       �
_root_df_sane�.��       )��.��       N��.��       �scipy/optimize/_spectral.py�.��%       �!<module scipy.optimize._spectral>�.��       �z���.��       h5��.���       (hO�x0�hPhhhKh	h�disp��M�hh�sigma_0��line_search��unknown_options�h?hh9h!h�x_shape�h'h�
is_complex�hh>hhh"h)h(�	converged�h7�F_k_norm��d�h#�alpha�h+h.h,�s_k��y_k�h5hH�result�t�.��       (hOh�hhJhKhPhNhHh�hEt�.��       (h�h�h1h}t�.��       (hh5hHh>t�.��       h>��.��       hHh?��.��       h?��.��	       hHh'h?��.��       h'h?��.��       (h5hHh!hJhKhNhOhPht�.��       (hPhhOhKhJhNt�.��       (h5h}�v�hHh!hJhKhNhOhPht�.��       �__spec__�.