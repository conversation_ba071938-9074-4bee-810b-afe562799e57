LineEdit, TextEdit, PlainTextEdit {
    color: black;
    background-color: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(0, 0, 0, 13);
    border-bottom: 1px solid rgba(0, 0, 0, 100);
    border-radius: 5px;
    /* font: 14px "Segoe UI", "Microsoft YaHei"; */
    padding: 0px 10px;
    selection-background-color: --ThemeColorLight1;
}

TextEdit,
PlainTextEdit {
    padding: 2px 3px 2px 8px;
}

LineEdit:hover, TextEdit:hover, PlainTextEdit:hover {
    background-color: rgba(249, 249, 249, 0.5);
    border: 1px solid rgba(0, 0, 0, 13);
    border-bottom: 1px solid rgba(0, 0, 0, 100);
}

LineEdit:focus {
    border-bottom: 1px solid rgba(0, 0, 0, 13);
    background-color: white;
}

TextEdit:focus,
PlainTextEdit:focus {
    border-bottom: 1px solid --ThemeColorPrimary;
    background-color: white;
}

LineEdit:disabled, TextEdit:disabled,
PlainTextEdit:disabled {
    color: rgba(0, 0, 0, 92);
    background-color: rgba(249, 249, 249, 0.3);
    border: 1px solid rgba(0, 0, 0, 13);
    border-bottom: 1px solid rgba(0, 0, 0, 13);
}

#lineEditButton {
    background-color: transparent;
    border-radius: 4px;
    margin: 0;
}

#lineEditButton:hover {
    background-color: rgba(0, 0, 0, 9);
}

#lineEditButton:pressed {
    background-color: rgba(0, 0, 0, 6);
}
