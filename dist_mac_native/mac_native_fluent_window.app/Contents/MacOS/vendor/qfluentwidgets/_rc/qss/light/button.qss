P<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ToggleToolButton {
    color: black;
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(0, 0, 0, 0.073);
    border-bottom: 1px solid rgba(0, 0, 0, 0.183);
    border-radius: 5px;
    /* font: 14px 'Segoe UI', 'Microsoft YaHei'; */
    padding: 5px 12px 6px 12px;
    outline: none;
}

ToolButton {
    padding: 5px 9px 6px 8px;
}

PushButton[isPlaceholderText=true] {
    color: rgba(0, 0, 0, 0.6063);
}

PushButton[hasIcon=false] {
    padding: 5px 12px 6px 12px;
}

PushButton[hasIcon=true] {
    padding: 5px 12px 6px 36px;
}

DropDownToolButton, PrimaryDropDownToolButton {
    padding: 5px 31px 6px 8px;
}

DropDownPushButton[hasIcon=false],
PrimaryDropDownPushButton[hasIcon=false] {
    padding: 5px 31px 6px 12px;
}

DropDownPushButton[hasIcon=true],
PrimaryDropDownPushButton[hasIcon=true] {
    padding: 5px 31px 6px 36px;
}

PushButton:hover, ToolButton:hover, ToggleButton:hover, ToggleToolButton:hover {
    background: rgba(249, 249, 249, 0.5);
}

PushButton:pressed, ToolButton:pressed, ToggleButton:pressed, ToggleToolButton:pressed {
    color: rgba(0, 0, 0, 0.63);
    background: rgba(249, 249, 249, 0.3);
    border-bottom: 1px solid rgba(0, 0, 0, 0.073);
}

PushButton:disabled, ToolButton:disabled, ToggleButton:disabled, ToggleToolButton:disabled {
    color: rgba(0, 0, 0, 0.36);
    background: rgba(249, 249, 249, 0.3);
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}


PrimaryPushButton,
PrimaryToolButton,
ToggleButton:checked,
ToggleToolButton:checked {
    color: white;
    background-color: --ThemeColorPrimary;
    border: 1px solid --ThemeColorLight1;
    border-bottom: 1px solid --ThemeColorDark1;
}

PrimaryPushButton:hover,
PrimaryToolButton:hover,
ToggleButton:checked:hover,
ToggleToolButton:checked:hover {
    background-color: --ThemeColorLight1;
    border: 1px solid --ThemeColorLight2;
    border-bottom: 1px solid --ThemeColorDark1;
}

PrimaryPushButton:pressed,
PrimaryToolButton:pressed,
ToggleButton:checked:pressed,
ToggleToolButton:checked:pressed {
    color: rgba(255, 255, 255, 0.63);
    background-color: --ThemeColorLight3;
    border: 1px solid --ThemeColorLight3;
}

PrimaryPushButton:disabled,
PrimaryToolButton:disabled,
ToggleButton:checked:disabled,
ToggleToolButton:checked:disabled {
    color: rgba(255, 255, 255, 0.9);
    background-color: rgb(205, 205, 205);
    border: 1px solid rgb(205, 205, 205);
}

SplitDropButton,
PrimarySplitDropButton {
    border-left: none;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

#splitPushButton,
#splitToolButton,
#primarySplitPushButton,
#primarySplitToolButton {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

#splitPushButton:pressed,
#splitToolButton:pressed,
SplitDropButton:pressed {
    border-bottom: 1px solid rgba(0, 0, 0, 0.183);
}

PrimarySplitDropButton:pressed {
    border-bottom: 1px solid --ThemeColorDark1;
}

#primarySplitPushButton, #primarySplitToolButton {
    border-right: 1px solid --ThemeColorLight3;
}

#primarySplitPushButton:pressed, #primarySplitToolButton:pressed {
    border-bottom: 1px solid --ThemeColorDark1;
}

HyperlinkButton {
    /* font: 14px 'Segoe UI', 'Microsoft YaHei'; */
    padding: 6px 12px 6px 12px;
    color: --ThemeColorPrimary;
    border: none;
    border-radius: 6px;
    background-color: transparent;
}

HyperlinkButton[hasIcon=false] {
    padding: 6px 12px 6px 12px;
}

HyperlinkButton[hasIcon=true] {
    padding: 6px 12px 6px 36px;
}

HyperlinkButton:hover {
    color: --ThemeColorPrimary;
    background-color: rgba(0, 0, 0, 10);
    border: none;
}

HyperlinkButton:pressed {
    color: --ThemeColorPrimary;
    background-color: rgba(0, 0, 0, 6);
    border: none;
}

HyperlinkButton:disabled {
    color: rgba(0, 0, 0, 0.43);
    background-color: transparent;
    border: none;
}


RadioButton {
    min-height: 24px;
    max-height: 24px;
    background-color: transparent;
    font: 14px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
    color: black;
}

RadioButton::indicator {
    width: 18px;
    height: 18px;
    border-radius: 11px;
    border: 2px solid #999999;
    background-color: rgba(0, 0, 0, 5);
    margin-right: 4px;
}

RadioButton::indicator:hover {
    background-color: rgba(0, 0, 0, 0);
}

RadioButton::indicator:pressed {
    border: 2px solid #bbbbbb;
    background-color: qradialgradient(spread:pad, cx:0.5, cy:0.5, radius:0.5, fx:0.5, fy:0.5,
            stop:0 rgb(255, 255, 255),
            stop:0.5 rgb(255, 255, 255),
            stop:0.6 rgb(225, 224, 223),
            stop:1 rgb(225, 224, 223));
}

RadioButton::indicator:checked {
    height: 22px;
    width: 22px;
    border: none;
    border-radius: 11px;
    background-color: qradialgradient(spread:pad, cx:0.5, cy:0.5, radius:0.5, fx:0.5, fy:0.5,
            stop:0 rgb(255, 255, 255),
            stop:0.5 rgb(255, 255, 255),
            stop:0.6 --ThemeColorPrimary,
            stop:1 --ThemeColorPrimary);
}

RadioButton::indicator:checked:hover {
    background-color: qradialgradient(spread:pad, cx:0.5, cy:0.5, radius:0.5, fx:0.5, fy:0.5,
            stop:0 rgb(255, 255, 255),
            stop:0.6 rgb(255, 255, 255),
            stop:0.7 --ThemeColorPrimary,
            stop:1 --ThemeColorPrimary);
}

RadioButton::indicator:checked:pressed {
    background-color: qradialgradient(spread:pad, cx:0.5, cy:0.5, radius:0.5, fx:0.5, fy:0.5,
            stop:0 rgb(255, 255, 255),
            stop:0.5 rgb(255, 255, 255),
            stop:0.6 --ThemeColorPrimary,
            stop:1 --ThemeColorPrimary);
}

RadioButton:disabled {
    color: rgba(0, 0, 0, 110);
}

RadioButton::indicator:disabled {
    border: 2px solid #bbbbbb;
    background-color: transparent;
}

RadioButton::indicator:disabled:checked {
    border: none;
    background-color: qradialgradient(spread:pad, cx:0.5, cy:0.5, radius:0.5, fx:0.5, fy:0.5,
            stop:0 rgb(255, 255, 255),
            stop:0.5 rgb(255, 255, 255),
            stop:0.6 rgba(0, 0, 0, 0.2169),
            stop:1 rgba(0, 0, 0, 0.2169));
}

TransparentToolButton,
TransparentToggleToolButton,
TransparentDropDownToolButton,
TransparentPushButton,
TransparentDropDownPushButton,
TransparentTogglePushButton {
    background-color: transparent;
    border: none;
    border-radius: 5px;
    margin: 0;
}

TransparentToolButton:hover,
TransparentToggleToolButton:hover,
TransparentDropDownToolButton:hover,
TransparentPushButton:hover,
TransparentDropDownPushButton:hover,
TransparentTogglePushButton:hover {
    background-color: rgba(0, 0, 0, 9);
    border: none;
}

TransparentToolButton:pressed,
TransparentToggleToolButton:pressed,
TransparentDropDownToolButton:pressed,
TransparentPushButton:pressed,
TransparentDropDownPushButton:pressed,
TransparentTogglePushButton:pressed {
    background-color: rgba(0, 0, 0, 6);
    border: none;
}

TransparentToolButton:disabled,
TransparentToggleToolButton:disabled,
TransparentDropDownToolButton:disabled,
TransparentPushButton:disabled,
TransparentDropDownPushButton:disabled,
TransparentTogglePushButton:disabled {
    background-color: transparent;
    border: none;
}


PillPushButton,
PillPushButton:hover,
PillPushButton:pressed,
PillPushButton:disabled,
PillPushButton:checked,
PillPushButton:checked:hover,
PillPushButton:checked:pressed,
PillPushButton:disabled:checked,
PillToolButton,
PillToolButton:hover,
PillToolButton:pressed,
PillToolButton:disabled,
PillToolButton:checked,
PillToolButton:checked:hover,
PillToolButton:checked:pressed,
PillToolButton:disabled:checked {
    background-color: transparent;
    border: none;
}
