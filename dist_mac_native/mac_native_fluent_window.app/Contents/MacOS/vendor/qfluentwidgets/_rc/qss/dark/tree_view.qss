QTreeView {
    background-color: transparent;
    border: none;
    border-radius: 5px;
    outline: 0;
    padding-right: 5px;
    /* font: 13px 'Segoe UI', 'Microsoft YaHei'; */
    selection-background-color: transparent;
}

QTableView[isBorderVisible=true] {
    border: 1px solid rgba(255, 255, 255, 21);
}

QTreeView::item {
    padding: 4px;
    margin-top: 2px;
    margin-bottom: 2px;
    padding-left: 20px;
    border-radius: 5px;
    /* color: white; */
}


QTreeView::branch:has-children:!has-siblings:closed,
QTreeView::branch:closed:has-children:has-siblings {
    image: url(:/qfluentwidgets/images/tree_view/TreeViewClose_white.svg);
}

QTreeView::branch:open:has-children:!has-siblings,
QTreeView::branch:open:has-children:has-siblings {
    image: url(:/qfluentwidgets/images/tree_view/TreeViewOpen_white.svg);
}

QTreeView:disabled {
    color: rgba(255, 255, 255, 110);
}

QTreeView::indicator {
    width: 18px;
    height: 18px;
    border-radius: 5px;
    border: none;
    background-color: transparent;
}

QHeaderView {
    background-color: transparent;
}

QHeaderView::section {
    background-color: transparent;
    color: rgb(203, 203, 203);
    padding-left: 5px;
    padding-right: 5px;
    border: 1px solid rgba(0, 0, 0, 21);
    font: 13px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
}

QHeaderView::section:horizontal {
    border-left: none;
    height: 33px;
}

QTreeView[isBorderVisible=true] QHeaderView::section:horizontal {
    border-top: none;
}

QHeaderView::section:horizontal:last {
    border-right: none;
}

QHeaderView::section:vertical {
    border-top: none;
}

QHeaderView::section:checked {
    background-color: transparent;
}

QHeaderView::down-arrow {
    subcontrol-origin: padding;
    subcontrol-position: center right;
    margin-right: 6px;
    image: url(:/qfluentwidgets/images/table_view/Down_white.svg);
}

QHeaderView::up-arrow {
    subcontrol-origin: padding;
    subcontrol-position: center right;
    margin-right: 6px;
    image: url(:/qfluentwidgets/images/table_view/Up_white.svg);
}

QTableCornerButton::section {
    background-color: transparent;
    border: 1px solid rgba(0, 0, 0, 21);
}

QTableCornerButton::section:pressed {
    background-color: rgba(255, 255, 255, 16);
}