InfoBar {
    border: 1px solid rgb(229, 229, 229);
    border-radius: 6px;
    background-color: rgb(244, 244, 244);
}

#titleLabel {
    font: 14px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
    font-weight: bold;
    color: black;
    background-color: transparent;
}

#contentLabel {
    font: 14px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
    color: black;
    background-color: transparent;
}

InfoBar[type="Info"] {
    background-color: rgb(244, 244, 244);
}

InfoBar[type="Success"] {
    background-color: rgb(223, 246, 221);
}

InfoBar[type="Warning"] {
    background-color: rgb(255, 244, 206);
}

InfoBar[type="Error"] {
    background-color: rgb(253, 231, 233);
}

InfoBarCloseButton {
    background-color: transparent;
    border-radius: 5px;
}

InfoBarCloseButton:hover {
    background-color: rgba(0, 0, 0, 9);
}

InfoBarCloseButton:pressed {
    background-color: rgba(0, 0, 0, 6);
}