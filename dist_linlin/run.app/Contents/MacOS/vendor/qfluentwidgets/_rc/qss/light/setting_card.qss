QLabel {
    font: 14px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
    color: black;
    padding: 0;
    border: none;
    background-color: transparent;
}

QLabel#contentLabel {
    font: 11px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
    color: rgb(96, 96, 96);
    padding: 0;
}

RangeSettingCard > QLabel#valueLabel{
    color: rgb(96, 96, 96);
}

QLabel:disabled,
QLabel#contentLabel:disabled,
RangeSettingCard>QLabel#valueLabel:disabled {
    color: rgba(0, 0, 0, 0.36);
}

QPushButton {
    border: 1px solid rgba(0, 0, 0, 0.073);
    border-radius: 5px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.183);
    padding: 5px 36px 5px 36px;
    font: 14px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
    color: black;
    background: rgba(255, 255, 255, 0.7);
    outline: none;
}

QPushButton:hover {
    background: rgba(249, 249, 249, 0.5);
}

QPushButton:pressed {
    color: rgba(0, 0, 0, 0.63);
    background: rgba(249, 249, 249, 0.3);
    border-bottom: 1px solid rgba(0, 0, 0, 0.073);
}

QPushButton:disabled {
    color: rgba(0, 0, 0, 0.36);
    background: rgba(249, 249, 249, 0.3);
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}


#primaryButton {
    color: white;
    background-color: --ThemeColorPrimary;
    border: 1px solid --ThemeColorLight1;
    border-bottom: 1px solid --ThemeColorDark1;
    padding: 5px 12px 5px 12px;
    outline: none;
}

#primaryButton:hover {
    background-color: --ThemeColorLight1;
    border: 1px solid --ThemeColorLight2;
    border-bottom: 1px solid --ThemeColorDark1;
}

#primaryButton:pressed {
    color: rgba(255, 255, 255, 0.63);
    background-color: --ThemeColorLight3;
    border: 1px solid --ThemeColorLight3;
}

ColorPickerButton {
    border: 1px solid rgb(240, 240, 240);
    border-radius: 5px;
    border-bottom: 1px solid rgb(214, 214, 214);
}
