Push<PERSON><PERSON>on, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ToggleToolButton {
    background: rgba(255, 255, 255, 0.0605);
    border: 1px solid rgba(255, 255, 255, 0.053);
    border-top: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 5px;
    color: white;
    /* font: 14px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC'; */
    padding: 5px 12px 6px 12px;
    outline: none;
}

ToolButton {
    padding: 5px 9px 6px 8px;
}

PushButton[isPlaceholderText=true] {
    color: rgba(255, 255, 255, 0.6063);
}

PushButton[hasIcon=false] {
    padding: 5px 12px 6px 12px;
}

PushButton[hasIcon=true] {
    padding: 5px 12px 6px 36px;
}

DropDownToolButton,
PrimaryDropDownToolButton {
    padding: 5px 31px 6px 8px;
}

DropDownPushButton[hasIcon=false],
PrimaryDropDownPushButton[hasIcon=false] {
    padding: 5px 31px 6px 12px;
}

DropDownPushButton[hasIcon=true],
PrimaryDropDownPushButton[hasIcon=true] {
    padding: 5px 31px 6px 36px;
}

PushButton:hover, ToolButton:hover, ToggleButton:hover,ToggleToolButton:hover {
    background: rgba(255, 255, 255, 0.0837);
}

PushButton:pressed, ToolButton:pressed, ToggleButton:pressed, ToggleToolButton:pressed {
    color: rgba(255, 255, 255, 0.786);
    background: rgba(255, 255, 255, 0.0326);
    border-top: 1px solid rgba(255, 255, 255, 0.053);
}

PushButton:disabled, ToolButton:disabled, ToggleButton:disabled, ToggleToolButton:disabled {
    color: rgba(255, 255, 255, 0.3628);
    background: rgba(255, 255, 255, 0.0419);
    border: 1px solid rgba(255, 255, 255, 0.053);
    border-top: 1px solid rgba(255, 255, 255, 0.053);
}

#splitPushButton:pressed,
#splitToolButton:pressed,
SplitDropButton:pressed {
    border-top: 1px solid rgba(255, 255, 255, 0.08);
}


PrimaryPushButton,
PrimaryToolButton,
ToggleButton:checked,
ToggleToolButton:checked {
    color: black;
    background-color: --ThemeColorPrimary;
    border: 1px solid --ThemeColorLight1;
    border-bottom: 1px solid --ThemeColorLight2;
}

PrimaryPushButton:hover,
PrimaryToolButton:hover,
ToggleButton:checked:hover,
ToggleToolButton:checked:hover {
    background-color: --ThemeColorDark1;
    border: 1px solid --ThemeColorLight1;
    border-bottom: 1px solid --ThemeColorLight2;
}

PrimaryPushButton:pressed,
PrimaryToolButton:pressed,
ToggleButton:checked:pressed,
ToggleToolButton:checked:pressed {
    color: rgba(0, 0, 0, 0.63);
    background-color: --ThemeColorDark2;
    border: 1px solid --ThemeColorDark2;
}

PrimaryPushButton:disabled,
PrimaryToolButton:disabled,
ToggleButton:checked:disabled,
ToggleToolButton:checked:disabled {
    color: rgba(255, 255, 255, 0.43);
    background-color: rgb(52, 52, 52);
    border: 1px solid rgb(52, 52, 52);
}

SplitDropButton,
PrimarySplitDropButton {
    border-left: none;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

#splitPushButton,
#splitToolButton,
#primarySplitPushButton,
#primarySplitToolButton {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right-color: rgba(255, 255, 255, 0.08);
}

#primarySplitPushButton,
#primarySplitToolButton {
    border-right-color: --ThemeColorDark3;
}

#primarySplitPushButton:pressed,
#primarySplitToolButton:pressed {
    border-right: 1px solid --ThemeColorLight2;
}

HyperlinkButton {
    /* font: 14px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC'; */
    padding: 5px 12px 6px 12px;
    color: --ThemeColorPrimary;
    border: none;
    border-radius: 6px;
    background-color: transparent;
}

HyperlinkButton[hasIcon=false] {
    padding: 5px 12px 6px 12px;
}

HyperlinkButton[hasIcon=true] {
    padding: 5px 12px 6px 36px;
}

HyperlinkButton:hover {
    color: --ThemeColorPrimary;
    background-color: rgba(255, 255, 255, 10);
    border: none;
}

HyperlinkButton:pressed {
    color: --ThemeColorDark1;
    background-color: rgba(255, 255, 255, 7);
    border: none;
}

HyperlinkButton:disabled {
    color: rgba(255, 255, 255, 0.43);
    background-color: transparent;
    border: none;
}

RadioButton {
    min-height: 24px;
    max-height: 24px;
    background-color: transparent;
    font: 14px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
    color: white;
}

RadioButton::indicator {
    width: 18px;
    height: 18px;
    border-radius: 11px;
    border: 2px solid #848484;
    background-color: transparent;
    margin-right: 4px;
}

RadioButton::indicator:hover {
    background-color: rgba(255, 255, 255, 16);
}

RadioButton::indicator:pressed {
    border: 2px solid #434343;
    background-color: qradialgradient(spread:pad, cx:0.5, cy:0.5, radius:0.5, fx:0.5, fy:0.5,
            stop:0 rgb(0, 0, 0),
            stop:0.5 rgb(0, 0, 0),
            stop:0.6 rgb(43, 42, 42),
            stop:1 rgb(43, 42, 42));
}

RadioButton::indicator:checked {
    height: 22px;
    width: 22px;
    border: none;
    border-radius: 11px;
    background-color: qradialgradient(spread:pad, cx:0.5, cy:0.5, radius:0.5, fx:0.5, fy:0.5,
            stop:0 rgb(0, 0, 0),
            stop:0.5 rgb(0, 0, 0),
            stop:0.6 --ThemeColorPrimary,
            stop:1 --ThemeColorPrimary);
}

RadioButton::indicator:checked:hover {
    background-color: qradialgradient(spread:pad, cx:0.5, cy:0.5, radius:0.5, fx:0.5, fy:0.5,
            stop:0 rgb(0, 0, 0),
            stop:0.6 rgb(0, 0, 0),
            stop:0.7 --ThemeColorPrimary,
            stop:1 --ThemeColorPrimary);
}

RadioButton::indicator:checked:pressed {
    background-color: qradialgradient(spread:pad, cx:0.5, cy:0.5, radius:0.5, fx:0.5, fy:0.5,
            stop:0 rgb(0, 0, 0),
            stop:0.5 rgb(0, 0, 0),
            stop:0.6 --ThemeColorPrimary,
            stop:1 --ThemeColorPrimary);
}

RadioButton:disabled {
    color: rgba(255, 255, 255, 0.3628);
}

RadioButton::indicator:disabled {
    border: 2px solid #646464;
    background-color: transparent;
}

RadioButton::indicator:disabled:checked {
    border: none;
    background-color: qradialgradient(spread:pad, cx:0.5, cy:0.5, radius:0.5, fx:0.5, fy:0.5,
            stop:0 rgb(0, 0, 0),
            stop:0.5 rgb(0, 0, 0),
            stop:0.6 rgba(255, 255, 255, 0.158),
            stop:1 rgba(255, 255, 255, 0.158));
}

TransparentToolButton,
TransparentToggleToolButton,
TransparentDropDownToolButton,
TransparentPushButton,
TransparentDropDownPushButton,
TransparentTogglePushButton {
    background-color: transparent;
    border: none;
    border-radius: 4px;
    margin: 0;
}

TransparentToolButton:hover,
TransparentToggleToolButton:hover,
TransparentDropDownToolButton:hover,
TransparentPushButton:hover,
TransparentDropDownPushButton:hover,
TransparentTogglePushButton:hover {
    background-color: rgba(255, 255, 255, 9);
    border: none;
}

TransparentToolButton:pressed,
TransparentToggleToolButton:pressed,
TransparentDropDownToolButton:pressed,
TransparentPushButton:pressed,
TransparentDropDownPushButton:pressed,
TransparentTogglePushButton:pressed {
    background-color: rgba(255, 255, 255, 6);
    border: none;
}

TransparentToolButton:disabled,
TransparentToggleToolButton:disabled,
TransparentDropDownToolButton:disabled,
TransparentPushButton:disabled,
TransparentDropDownPushButton:disabled,
TransparentTogglePushButton:disabled {
    background-color: transparent;
    border: none;
}

PillPushButton,
PillPushButton:hover,
PillPushButton:pressed,
PillPushButton:disabled,
PillPushButton:checked,
PillPushButton:checked:hover,
PillPushButton:checked:pressed,
PillPushButton:disabled:checked,
PillToolButton,
PillToolButton:hover,
PillToolButton:pressed,
PillToolButton:disabled,
PillToolButton:checked,
PillToolButton:checked:hover,
PillToolButton:checked:pressed,
PillToolButton:disabled:checked {
    background-color: transparent;
    border: none;
}